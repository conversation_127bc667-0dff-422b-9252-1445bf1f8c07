package com.hl.test.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;
import com.hl.test.Utils.BzgsUtil;
import com.hl.test.Utils.Export.ExportInterface;
import com.hl.test.Utils.Export.ExportXlsxHelper;
import com.hl.test.Utils.Lib;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.WjscJqSjxx;
import com.hl.test.domain.req.ReqJQStaQuery;
import com.hl.test.domain.resp.*;
import com.hl.test.mapper.DictMapper;
import com.hl.test.mapper.WjscJqCjxxMapper;
import com.hl.test.mapper.WjscJqSjxxMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "警情统计")
@RequestMapping("/jq_sta")
@SuppressWarnings("unchecked")
public class JQStaController {
    @Value("${upload_path}")
    private String upload_path;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Value("${jq_sta_url}")
    private String jq_url;

    @Autowired
    private DictMapper dictMapper;

    @Autowired
    private WjscJqSjxxMapper wjscJqSjxxMapper;

    @Autowired
    private WjscJqCjxxMapper wjscJqCjxxMapper;

    // 警情统计
    @PostMapping("/query")
    @ApiOperation(value = "警情统计")
    public R<List<RespJQSta>> list(@Valid @RequestBody ReqJQStaQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        return JqSta(data);
        //return JqSta_http(data);
    }

    private R<String> JqSta_http(JSONObject data) {
        long start = System.currentTimeMillis();
        String cont = "";
        try {
            String s = "select content from sta_temp where cols='" + data + "'";
            cont = jdbcTemplate.queryForObject(s, String.class);
        } catch (Exception ex) {

        }
        if (cont != null && cont.length() > 0) {
            log.warn("==his-->");

        } else {

            data.put("opt", "list");

            String ret = GethttpOk(data, "", jq_url);
            System.out.println("--1-->" + (System.currentTimeMillis() - start));
            if (ret.startsWith("ok") && !ret.contains("xxx")) {
                String id = ret.split("_")[1];

                String sql = "select content from sta_temp where id='" + id + "'";
                cont = jdbcTemplate.queryForObject(sql, String.class);
                System.out.println("--2-->" + (System.currentTimeMillis() - start));


            } else {
                log.error("xxx");
                return R.ok("");
            }

            return R.ok(cont);
        }
        return R.ok(cont);

    }

    // 警情统计导出
    @PostMapping("/query_exp")
    @ApiOperation(value = "警情统计导出")
    public void listExp(@Valid @RequestBody ReqJQStaQuery params, HttpServletResponse resp) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        int type = 0;
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getInteger("type");
        }
        data.put("unit", u.getString("organization_id"));
        R<List<RespJQSta>> ret = JqSta(data);
        List<RespJQSta> ll = ret.getData();
        JSONArray dets = new JSONArray();
        List<RespJQStaDet> lists = new ArrayList<>();
        if (ll.size() > 0) {
            for (int i = 0; i < ll.size(); i++) {
                RespJQSta d = ll.get(i);
                int t = d.getType();
                if (t == type) {
                    lists = d.getDets();
                    break;
                }

            }

            for (int i = 0; i < lists.size(); i++) {
                RespJQStaDet d = lists.get(i);
                JSONObject o = JSONObject.parseObject(gson.toJson(d));
                dets.add(o);

            }
            String heads = "标签名称,个数,所属单位";
            String keys = "memo,count,unit";


            String fileName = ExportTables(dets, heads, keys, "jqSta");
            resp = GetResp(fileName, resp);
        }
    }

    // 警情统计导出
    @PostMapping("/query_sjry_exp")
    @ApiOperation(value = "警情统计导出")
    public void listSjryExp(@Valid @RequestBody ReqJQStaQuery params, HttpServletResponse resp) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        int type = 0;
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getInteger("type");
        }
        data.put("unit", u.getString("organization_id"));
        R<List<RespJQSta>> ret = JqStaSjry(data);
        List<RespJQSta> ll = ret.getData();
        JSONArray dets = new JSONArray();
        List<RespJQStaDet> lists = new ArrayList<>();
        if (ll.size() > 0) {
            for (int i = 0; i < ll.size(); i++) {
                RespJQSta d = ll.get(i);
                int t = d.getType();
                if (t == type) {
                    lists = d.getDets();
                    break;
                }

            }

            for (int i = 0; i < lists.size(); i++) {
                RespJQStaDet d = lists.get(i);
                JSONObject o = JSONObject.parseObject(gson.toJson(d));
                dets.add(o);

            }
            String heads = "姓名(身份证),次数,所属单位";
            String keys = "memo,count,unit";


            String fileName = ExportTables(dets, heads, keys, "jqSta");
            resp = GetResp(fileName, resp);
        }
    }

    private R<List<RespJQSta>> JqSta(JSONObject data) {
        String sql = "";
        String sql4jq = "";

        //接警单位
        String unit = "";
        String relau = data.getString("unit");
        if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
            unit = data.getString("jjdw");

        } else {
            unit = data.getString("unit");

        }
        List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
        String sq = "";
        for (int i = 0; i < us.size(); i++) {
            String u = us.get(i);

            int type = 23;
            try {
                type = RIUtil.dicts.get(u).getInteger("type");
            } catch (Exception e) {
                log.warn(u);
                log.warn("d.size-->" + RIUtil.dicts.size());

            }
            if (type == 21 || type == 22 || type == 27) {
                // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
            } else if (type == 23 || type == 24) {
                sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
                //sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
//            } else if (type == 25) {
//                sq = sq + "  jjdw='" + u + "' or ";
            } else if (type == 26 || type == 25) {
                sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
            } else {
                sq = sq + "  jjdw='" + u + "' or ";
            }
        }
        if (sq.endsWith("or ")) {
            sq = sq.substring(0, sq.length() - 3);
        //    sql = sql + " and (" + sq + ") ";
            sql4jq = sql4jq + " and (" + sq + ") ";
        }
        //接警时间开始
        if (data.containsKey("bjdhsj_time_start") && data.getString("bjdhsj_time_start").length() > 0) {
            String bjdhsj_time_start = data.getString("bjdhsj_time_start").replace("-", "").replace(":", "").replace(
                    " ", "");
            ;
         //   sql = sql + " and bjdhsj_time>='" + bjdhsj_time_start + "' ";
            sql4jq = sql4jq + " and bjdhsj_time>='" + bjdhsj_time_start + "' ";
        }
        //接警时间结束
        if (data.containsKey("bjdhsj_time_end") && data.getString("bjdhsj_time_end").length() > 0) {
            String bjdhsj_time_end = data.getString("bjdhsj_time_end").replace("-", "").replace(":", "").replace(" ",
                    "");
            ;
        //    sql = sql + " and bjdhsj_time<='" + bjdhsj_time_end + "' ";
            sql4jq = sql4jq + " and bjdhsj_time<='" + bjdhsj_time_end + "' ";
        }
        //标注时间开始
        if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
            String bz_time_start = data.getString("bz_time_start").replace("-", "").replace(":", "").replace(" ", "");
            ;
           // sql = sql + " and bz_time>='" + bz_time_start + "' ";
            sql4jq = sql4jq + " and bz_time>='" + bz_time_start + "' ";

        }
        //标注时间结束
        if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
            String bz_time_end = data.getString("bz_time_end").replace("-", "").replace(":", "").replace(" ", "");
            ;
         //   sql = sql + " and bz_time<='" + bz_time_end + "' ";
            sql4jq = sql4jq + " and bz_time<='" + bz_time_end + "' ";
        }
        //处警类别
        if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

            String lbs = data.getString("cjlb");
            if (lbs.length() > 3) {
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 2) + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cjlb='" + cjlb + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
          //      sql = sql + " and (" + s + ") ";
                sql4jq = sql4jq + " and (" + s + ") ";
            }
        }

        String tabId = "";
        if (data.containsKey("tabId") && data.getString("tabId").length() > 0) {
            tabId = data.getString("tabId");
        }
        //统计层级
        int level = -1;
        if (data.containsKey("level") && data.getString("level").length() > 0) {
            level = data.getInteger("level");
            if (level > 0) {
                sql = sql + " and level = '" + level + "' ";
            } else {
                level = -1;
            }
        }


        //警情标签
//        if (data.containsKey("jqbz") && data.getString("jqbz").length() > 2) {
//            String lbs = data.getString("jqbz");
        if (data.containsKey("bzbq") && data.getString("bzbq").length() > 2) {
            String lbs = data.getString("bzbq");
            if (lbs.length() > 3) {
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String jqbz = cone.getKey();
                    int lev = RIUtil.dicts.get(jqbz).getInteger("static_index");

                    if (lev == 1) {
                        int t = RIUtil.dicts.get(jqbz).getIntValue("type");
                        if (level != -1) {
                            s = s + " type='" + t + "' or ";
                        } else {
                            s = s + " (type='" + t + "'  and level=2)or ";
                        }
                    } else {
                        String n = RIUtil.dicts.get(jqbz).getString("dict_name");
                        String m = RIUtil.dicts.get(jqbz).getString("memo");
                        String fid = RIUtil.dicts.get(jqbz).getString("father_id");
                        if (level == -1) {
                            JSONArray ns = RIUtil.GetDictByFather(jqbz);
                            if (ns.size() > 0) {
                                //s = s + " (memo like '%-" + n + "%' and level='" + (lev + 1) + "')or ";
                                s = s + " (memo like '" + m + "%' and level='" + (lev + 1) + "')or ";
                            } else {
                                // 对于地址标签为所属商圈、工业园区下面的，统计的数据需要为关联的发生部位的统计数据
                                if(StringUtils.equalsAny(fid, "5FF9C8735F3243DA8C391859D371D9BD", "3226EB8CC1A649A7AD87E0B825F7AD01")) {
                                    if (tabId.length() == 0) {
                                        sql = sql + " and ((type=3 and level=5 ) or (type=4 and level=3 and memo like '人员职业%' ) or(type=6 and level=2 )or(type=7 and level=2 )or(type=8 and level=2 )or(type=9 and level=2 ))  ";
                                    }
                                    // 版本1
//                                    if (jqbz.contains(":") || jqbz.contains("-")){
//                                        jqbz = jqbz.replaceAll("[:-]", " +");
//                                    }
//                                    jqbz = "+" + jqbz;
//                                    s = s + " MATCH(jqbz) AGAINST ('" + jqbz + "' in boolean mode ) or ";

                                    s = s+ "jjbh in (\n" +
                                            "\t\tselect DISTINCT jjbh from v_jq_label where label = '" + jqbz + "'\n" +
                                            "\t) or ";

                                } else {
                                    s = s + " label = '" + jqbz + "' or ";

                                }

                               // s = s + " memo like '%-" + n + "%' or ";
                            }
                        } else {


                            s = s + " memo like '%-" + n + "%' or ";
                        }
                    }


                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
        }
        else {
            if (level == -1) {
                if (tabId.length() == 0) {
                    sql = sql + " and ((type=3 and level=5 ) or (type=4 and level=3 and memo like '人员职业%' ) or(type=6 and level=2 )or(type=7 and level=2 )or(type=8 and level=2 )or(type=9 and level=2 ))  ";
                } else {
                    sql = sql + " and ((type=3 and level=5 ) or (type=4 and level=3 and memo like '人员职业%' ) or(type=6 and level=2 )or(type=7 and level=2 )or(type=8 and level=2 )or(type=9 and level=2 ))  ";

                    String ts[] = tabId.split(",");
                    for (int i = 0; i < ts.length; i++) {


                        String t1 = ts[i];
                        String t1Type = RIUtil.dicts.get(t1).getString("type");
                        String meStr = RIUtil.dicts.get(t1).getString("dict_name");
                        if (t1Type.equals("4")) {

                            sql = sql.replace("人员职业", meStr);
                            System.out.println(sql);
                        } else {
                            String s = "memo like '" + meStr + "%' and level=3";
                            sql = sql.replace("type=7 and level=2", s);
                            System.out.println(sql);
                        }

                    }

                }
            }
        }
        //标注公式
        if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
            //todo
            String bzgs = data.getString("bzgs");
            List<String> gss = RIUtil.StringToArrayList(bzgs);
            //gss = BzgsUtil.fullBzgsList(gss);
            String s = BzgsUtil.convertBzCondition(gss);
            String s4jq = s;
//            String s4jq = "";
//            String s = "";
//            for (int i = 0; i < gss.size(); i++) {
//                String g = gss.get(i);
//
//                if (g.equals("(") || g.equals(")") || g.equals("AND") || g.equals("NOT") || g.equals("OR")) {
//
//                    s = s + " " + g + " ";
//                    s4jq = s4jq + " " + g + " ";
//
//                } else {
////                    if ((s.startsWith("AND ") || s.startsWith(") ") || s.startsWith("OR ")) && s.length() > 5) {
////                        s = s + " and ";
////                    }
//                    if ((!StringUtils.endsWith(s, "AND ") && !StringUtils.endsWith(s, "OR ")) && s.length() > 5) {
//                        s = s + " AND ";
//                        s4jq = s4jq + " AND ";
//                    }
//                    int t = RIUtil.dicts.get(g).getIntValue("type");
//
//                    g = "\"" + g + "\"";
//                    if (t != 4) {
//                        s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                        s4jq = s4jq + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                    } else {
//                        s = s + " jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
//                        s4jq = s4jq + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
//                    }
//                }
//            }
            if (s.length() > 3) {
                s = s.replace("NOT", "!=");
                s4jq = s4jq.replace("NOT", "!=");
                System.out.println(s);
      //          sql = sql + " and (" + s + ")";
                sql4jq = sql4jq + " and (" + s4jq + ")";
            }

        }
        String sqls4Jq = "select cj.jjbh from wjsc_jq_cjxx cj left join wjsc_jq_jjxx jj on cj.jjbh = jj.jjbh where 1 = 1 " + sql4jq;
        log.warn(sqls4Jq);
        List<Map<String, Object>> list1 = jdbcTemplate.queryForList(sqls4Jq + " limit ?,?", 0, 999999);
        Set<String> jjbhs = list1.stream().map(x -> x.get("jjbh").toString()).collect(Collectors.toSet());

        String jjbhsCondition = jjbhs.stream().map(x -> "'" + x + "'").collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(jjbhsCondition)) {
            sql = sql + " and jjbh in (" + jjbhsCondition +")";
        }

        String sqls = "select count(DISTINCT jjbh) as count,type,dict_name,memo,label,unit from v_jq_sta_new cj where 1=1 " + sql + " " +
                "group " + "by " + "label order by count desc";
        log.warn(sqls);

        List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 999999);
        if (list.size() > 0) {
            JSONArray ret = RIUtil.ListMap2jsa(list);
            HashMap<Integer, List<RespJQStaDet>> stats = new HashMap<>();

            Gson gson = new Gson();
            for (int i = 0; i < ret.size(); i++) {
                JSONObject one = ret.getJSONObject(i);
                int type = one.getInteger("type");
                String label = one.getString("label") + "_" + type;

                List<RespJQStaDet> sds = new ArrayList<>();
                if (stats.containsKey(type)) {
                    sds = stats.get(type);

                }
                String u = one.getString("unit");
                try {
                    one.put("unit", RIUtil.dicts.get(u).getString("remark"));
                } catch (Exception ex) {
                }
                RespJQStaDet sd = gson.fromJson(String.valueOf(one), RespJQStaDet.class);
                sds.add(sd);

                stats.put(type, sds);
            }
            // System.out.println(stats);
            List<RespJQSta> stas = new ArrayList<>();
            for (Map.Entry<Integer, List<RespJQStaDet>> dd : stats.entrySet()) {
                int name = dd.getKey();
                List<RespJQStaDet> v = dd.getValue();
                String tName = RIUtil.GetBqTypeName(name);
                RespJQSta d = new RespJQSta();
                d.setName(tName);
                d.setDets(v);
                d.setType(name);
                if ((name == 4 || name == 7) && level < 2) {
                    JSONArray tabs = RIUtil.GetDictByFather(RIUtil.GetBqTypeId(name));
                    JSONArray tdets = new JSONArray();
                    for (Object oo :
                            tabs) {
                        JSONObject oone = (JSONObject) oo;
                        JSONObject tone = new JSONObject();
                        tone.put("id", oone.getString("id"));
                        tone.put("name", oone.getString("dict_name"));


                        tdets.add(tone);
                    }
                    d.setTabs(tdets);
                } else {
                    d.setTabs(new JSONArray());
                }
                stas.add(d);


            }


            return R.ok(stas);

        } else {
            return R.ok(new ArrayList<>(), 0);
        }

        //return null;
    }


    // 警情统计涉警人员
    @PostMapping("/query_sjry")
    @ApiOperation(value = "涉警人员统计")
    public R<List<RespJQSta>> list_sjry(@Valid @RequestBody ReqJQStaQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        return JqStaSjry(data);
        //return JqSta_http(data);
    }

    private R<List<RespJQSta>> JqStaSjry(JSONObject data) {
        String sql = "";

        //接警单位
        String unit = "";
        String relau = data.getString("unit");
        if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
            unit = data.getString("jjdw");


        } else {
            unit = data.getString("unit");

        }
        List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
        String sq = "";
        for (int i = 0; i < us.size(); i++) {
            String u = us.get(i);

            int type = 23;
            try {
                type = RIUtil.dicts.get(u).getInteger("type");
            } catch (Exception e) {
                log.warn(u);
                log.warn("d.size-->" + RIUtil.dicts.size());

            }
            if (type == 21 || type == 22 || type == 27) {
                // sql = sql + " and jj.jjdw like '%" + unit.substring(0, 4) + "%'";
            } else if (type == 23 || type == 24) {
                sq = sq + "  jj.jjdw like '" + u.substring(0, 6) + "%' or ";
            } else if (type == 26 || type == 25) {

                sq = sq + "  jj.jjdw like '" + u.substring(0, 8) + "%' or ";
            } else {
                sq = sq + "  jj.jjdw='" + u + "' or ";
            }
//            if (type == 21 || type == 22 || type == 27) {
//                // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
//            } else if (type == 23 || type == 24) {
//
//                //sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
//            } else if (type == 25) {
//                sq = sq + "  jjdw='" + u + "' or ";
//            } else if (type == 26) {
//
//                sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
//            } else {
//                sq = sq + "  jjdw='" + u + "' or ";
//            }
        }
        if (sq.endsWith("or ")) {
            sq = sq.substring(0, sq.length() - 3);
            sql = sql + " and (" + sq + ") ";
        }
        //接警时间开始
        if (data.containsKey("bjdhsj_time_start") && data.getString("bjdhsj_time_start").length() > 0) {
            String bjdhsj_time_start = data.getString("bjdhsj_time_start").replace("-", "").replace(":", "").replace(
                    " ", "");
            ;
            sql = sql + " and bjdhsj_time>='" + bjdhsj_time_start + "' ";
        }
        //接警时间结束
        if (data.containsKey("bjdhsj_time_end") && data.getString("bjdhsj_time_end").length() > 0) {
            String bjdhsj_time_end = data.getString("bjdhsj_time_end").replace("-", "").replace(":", "").replace(" ",
                    "");
            ;
            sql = sql + " and bjdhsj_time<='" + bjdhsj_time_end + "' ";
        }
        //标注时间开始
        if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
            String bz_time_start = data.getString("bz_time_start").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and bz_time>='" + bz_time_start + "' ";
        }
        //标注时间结束
        if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
            String bz_time_end = data.getString("bz_time_end").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and bz_time<='" + bz_time_end + "' ";
        }
        //处警类别
        if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

            String lbs = data.getString("cjlb");
            if (lbs.length() > 3) {
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 2) + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cjlb='" + cjlb + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
        }

        String tabId = "";
        if (data.containsKey("tabId") && data.getString("tabId").length() > 0) {
            tabId = data.getString("tabId");
        }
        //统计层级
//        int level = -1;
//        if (data.containsKey("level") && data.getString("level").length() > 0) {
//            level = data.getInteger("level");
//            if (level > 0) {
//                sql = sql + " and level = '" + level + "' ";
//            } else {
//                level = -1;
//            }
//        }
        //警情标签
        if (data.containsKey("bzbq") && data.getString("bzbq").length() > 2) {
            String lbs = data.getString("bzbq");
            System.out.println(lbs);

            String plbs = "";
            String dlbs = "";

            List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
            for (int i = 0; i < lbss.size(); i++) {
                String lb = lbss.get(i);
                int lbt = RIUtil.dicts.get(lb).getIntValue("type");
                if (lbt == 4) {
//                    if (lb.contains(":") || lb.contains("-")){
//                        lb = lb.replaceAll("[:-]", " +");
//                    }
//                    plbs = plbs + "+" + lb + " ";
                    //plbs = plbs + lb + " ";
                    plbs = plbs + "+\"" + lb + "\" ";
                } else {
//                    if (lb.contains(":") || lb.contains("-")){
//                        lb = lb.replaceAll("[:-]", " +");
//                    }
//                    dlbs = dlbs + "+" + lb + " ";
                    //dlbs = dlbs + lb + " ";
                    dlbs = dlbs + "\"" + lb + "\" ";
                }
            }

            String lbsql = "";
            if (dlbs.length() > 2) {
                lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode ))  ";
            }


            if (plbs.length() > 2) {
                if (lbsql.contains("MATCH")) {
                    lbsql = lbsql + " or ";
                }
                lbsql = lbsql + " ( MATCH(sj.personMs) AGAINST ('" + plbs + "' in boolean mode ) ) ";
            }

            if (lbsql.length() > 2) {
                sql = sql + " and (" + lbsql + ") ";
            }

        }
        //标注公式
        if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {

            String bzgs = data.getString("bzgs");
            List<String> gss = RIUtil.StringToArrayList(bzgs);
            String s = BzgsUtil.convertBzCondition(gss);
//            String bzgs = data.getString("bzgs");
//            List<String> gss = RIUtil.StringToArrayList(bzgs);
//            gss = BzgsUtil.fullBzgsList(gss);
//
//            String s = "";
//            for (int i = 0; i < gss.size(); i++) {
//                String g = gss.get(i);
//
//                if (g.equals("(") || g.equals(")") || g.equals("AND") || g.equals("NOT") || g.equals("OR")) {
//
//                    s = s + " " + g + " ";
//
//                } else {
////                    if ((s.startsWith("AND ") || s.startsWith(") ") || s.startsWith("OR ")) && s.length() > 5) {
////                        s = s + " and ";
////                    }
//                    if ((!StringUtils.endsWith(s, "AND ") && !StringUtils.endsWith(s, "OR ")) && s.length() > 5) {
//                        s = s + " AND ";
//                    }
//                    int t = RIUtil.dicts.get(g).getIntValue("type");
////                    if (g.contains(":") || g.contains("-")){
////                        g = g.replaceAll("[:-]", " +");
////                    }
////                    g = "+" + g;
////                    if (t != 4) {
////                        s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
////                    } else {
////                        s = s + " MATCH(personMs) AGAINST ('" + g + "' in boolean mode )";
////                    }
//                    g = "\"" + g + "\"";
//                    if (t != 4) {
//                        s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                    } else {
//                        s = s + " MATCH(personMs) AGAINST ('" + g + "' in boolean mode )";
//                    }
//                }
//            }
            if (s.length() > 3) {
                s = s.replace("NOT", "!=");
                System.out.println(s);

                sql = sql + " and (" + s + ")";
            }

        }


        String sqls = "select count(gmsfhm) as count,xm,gmsfhm from wjsc_jq_sjxx sj LEFT JOIN wjsc_jq_jjxx jj ON sj.jjbh = jj.jjbh  LEFT JOIN wjsc_jq_cjxx cj on sj.jjbh=cj.jjbh where 1=1 " + sql + " " +
                "and gmsfhm!='' and (sj.personM is not null and sj.personM!='') group " + "by " + "gmsfhm order by count desc";
        log.warn(sqls);

        List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 999999);
        if (list.size() > 0) {
            JSONArray ret = RIUtil.ListMap2jsa(list);

            List<RespJQStaDet> sds = new ArrayList<>();
            Gson gson = new Gson();
            for (int i = 0; i < ret.size(); i++) {
                JSONObject one = ret.getJSONObject(i);
                if (one.getInteger("count") > 1) {
                    RespJQStaDet sd = new RespJQStaDet();
                    sd.setCount(one.getInteger("count"));
                    sd.setMemo(one.getString("xm") + "(" + one.getString("gmsfhm") + ")");
                    sd.setDict_name(one.getString("xm") + "(" + one.getString("gmsfhm") + ")");
                    sd.setLabel(one.getString("gmsfhm"));
                    sd.setUnit("");
                    sds.add(sd);
                }

            }
            // System.out.println(stats);
            List<RespJQSta> stas = new ArrayList<>();

            RespJQSta d = new RespJQSta();
            d.setTabs(new JSONArray());
            d.setDets(sds);
            d.setName("多次涉警人员");
            d.setType(99);

            stas.add(d);

            return R.ok(stas);

        } else {
            return R.ok(new ArrayList<>(), 0);
        }

        //return null;
    }


    // 警情统计明细
    @PostMapping("/sta_det")
    @ApiOperation(value = "警情统计明细")
    public R<RespSjryBq> staDet(@Valid @RequestBody ReqJQStaQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        if (data.containsKey("label") && data.getString("label").length() > 0) {

        } else {
            return R.fail("label缺失");
        }

        return JqStaDet(data);
        //return JqStaDetHttp(data);
    }

    private R<RespSjryBq> JqStaDetHttp(JSONObject data) {

        data.put("opt", "det");

        String ret = GethttpOk(data, "", jq_url);
        // System.out.println("--1-->" + (System.currentTimeMillis() - start));
        if (ret.startsWith("ok") && !ret.contains("xxx")) {
            String id = ret.split("_")[1];

            String sql = "select content from sta_temp where id='" + id + "'";
            String cron = jdbcTemplate.queryForObject(sql, String.class);

            RespSjryBq det = new Gson().fromJson(cron, RespSjryBq.class);
            return R.ok(det);
        } else {
            return R.ok(new RespSjryBq());
        }
    }

    private R<RespSjryBq> JqStaDet(JSONObject data) {
        String sql = "";

        //接警单位
        String unit = "";
        String relau = data.getString("unit");
        if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
            unit = data.getString("jjdw");


        } else {
            unit = data.getString("unit");

        }
        List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
        String sq = "";
        for (int i = 0; i < us.size(); i++) {
            String u = us.get(i);

            int type = 23;
            try {
                type = RIUtil.dicts.get(u).getInteger("type");
            } catch (Exception e) {
                log.warn(u);
                log.warn("d.size-->" + RIUtil.dicts.size());

            }
            if (type == 21 || type == 22 || type == 27) {
                // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
            } else if (type == 23 || type == 24) {
                sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
                //sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
//            } else if (type == 25) {
//                sq = sq + "  jjdw='" + u + "' or ";
            } else if (type == 26 || type == 25) {
                sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
            } else {
                sq = sq + "  jjdw='" + u + "' or ";
            }
        }
        if (sq.endsWith("or ")) {
            sq = sq.substring(0, sq.length() - 3);
            sql = sql + " and (" + sq + ") ";
        }
//        List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
//        String sq = "";
//        for (int i = 0; i < us.size(); i++) {
//            String u = us.get(i);
//
//            int type = 23;
//            try {
//                type = RIUtil.dicts.get(u).getInteger("type");
//            } catch (Exception e) {
//                log.warn(u);
//                log.warn("d.size-->" + RIUtil.dicts.size());
//
//            }
//            if (type == 21 || type == 22 || type == 27) {
//                // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
//            } else if (type == 23 || type == 24) {
//
//                // sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
//            } else if (type == 25) {
//                sq = sq + "  jjdw='" + u + "' or ";
//            } else if (type == 26) {
//
//                sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
//            } else {
//                sq = sq + "  jjdw='" + u + "' or ";
//            }
//        }
//        if (sq.endsWith("or ")) {
//            sq = sq.substring(0, sq.length() - 3);
//            sql = sql + " and (" + sq + ") ";
//        }
        //接警时间开始
        if (data.containsKey("bjdhsj_time_start") && data.getString("bjdhsj_time_start").length() > 0) {
            String bjdhsj_time_start = data.getString("bjdhsj_time_start").replace("-", "").replace(":", "").replace(
                    " ", "");
            ;
            sql = sql + " and bjdhsj_time>='" + bjdhsj_time_start + "' ";
        }
        //接警时间结束
        if (data.containsKey("bjdhsj_time_end") && data.getString("bjdhsj_time_end").length() > 0) {
            String bjdhsj_time_end = data.getString("bjdhsj_time_end").replace("-", "").replace(":", "").replace(" ",
                    "");
            ;
            sql = sql + " and bjdhsj_time<='" + bjdhsj_time_end + "' ";
        }
        //标注时间开始
        if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
            String bz_time_start = data.getString("bz_time_start").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and bz_time>='" + bz_time_start + "' ";
        }
        //标注时间结束
        if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
            String bz_time_end = data.getString("bz_time_end").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and bz_time<='" + bz_time_end + "' ";
        }
        //处警类别
        if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

            String lbs = data.getString("cjlb");
            if (lbs.length() > 3) {
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 2) + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cjlb='" + cjlb + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
        }
        //警情标签
        if (data.containsKey("jqbz") && data.getString("jqbz").length() > 0) {
            String lbs = data.getString("jqbz");
            if (lbs.length() > 3) {
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String jqbz = cone.getKey();
                    int lev = RIUtil.dicts.get(jqbz).getInteger("static_index");

                    if (lev == 1) {
                        int t = RIUtil.dicts.get(jqbz).getIntValue("type");

                            s = s + " type='" + t + "' or ";

                    } else {
                        String n = RIUtil.dicts.get(jqbz).getString("dict_name");

                            s = s + " memo like '%-" + n + "%' or ";

                    }


                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
        }

        if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
            String bzgs = data.getString("bzgs");
            List<String> gss = RIUtil.StringToArrayList(bzgs);
            //gss = BzgsUtil.fullBzgsList(gss);
            String s = BzgsUtil.convertBzCondition(gss);

            if (s.length() > 3) {
                s = s.replace("NOT", "!=");
                System.out.println(s);

                sql = sql + " and (" + s + ")";
            }

        }

        if (data.containsKey("bzbq") && data.getString("bzbq").length() > 3) {
//            String bzgs = data.getString("bzbq");
//            List<String> gss = RIUtil.StringToArrayList(bzgs);
//            String s = BzgsUtil.convertBzCondition(gss);
//            if (s.length() > 3) {
//                s = s.replace("NOT", "!=");
//                System.out.println(s);
//
//                sql = sql + " and (" + s + ")";
//            }
            String lbs = data.getString("bzbq");
            System.out.println(lbs);

            String plbs = "";
            String dlbs = "";

            List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
            for (int i = 0; i < lbss.size(); i++) {
                String lb = lbss.get(i);
                int lbt = RIUtil.dicts.get(lb).getIntValue("type");
                if (lbt == 4) {
                    plbs = plbs + "\"" + lb + "\" ";
                } else {
                    dlbs = dlbs + "\"" + lb + "\" ";
                }
            }

            String lbsql = "";
            if (dlbs.length() > 2) {
                lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode ))  ";
            }


            if (plbs.length() > 2) {
                if (lbsql.contains("MATCH")) {
                    lbsql = lbsql + " or ";
                }
                //lbsql = lbsql + " ( MATCH(sj.personMs) AGAINST ('" + plbs + "' in boolean mode ) ) ";
                lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                        + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";

            }

            if (lbsql.length() > 2) {
                sql = sql + " and (" + lbsql + ") ";
            }
        }

        //统计层级
        if (data.containsKey("level") && data.getString("level").length() > 0) {
            int level = data.getInteger("level");
            if (level > 0) {
                sql = sql + " and level = '" + level + "' ";
            }
        }
        String label = "";
        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
            sql = sql + " and label = '" + label + "' ";
        }

        List<String> jjbhList = data.getList("jjbh_list",String.class);
        if (jjbhList !=null && !jjbhList.isEmpty()){
            sql = sql + "and jjbh in ("+jjbhList.stream().collect(Collectors.joining(","))+")";
        }


        String sqls = "select * from v_jq_sta_new cj where 1=1 " + sql + " group by jjbh";
        log.warn(sqls);
        RespSjryBq bq = new RespSjryBq();
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 999999);
        if (list.size() > 0) {
            JSONArray ret = RIUtil.ListMap2jsa(list);
            JSONObject stas = GetStaDets(ret, label, data);
            bq = new Gson().fromJson(String.valueOf(stas), RespSjryBq.class);


        }

        return R.ok(bq);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SjxxTmp{
        private Set<String> personMList;
        private Set<String> sjlbList;
    }

    private static List<String> parsePersonM(String personM) {
        if (StringUtils.isBlank(personM)) return Collections.emptyList();
        String newPersonM = personM.replaceAll("[\\[\\]\\s\"]", "");
        return Arrays.stream(newPersonM.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }


    private JSONObject GetStaDets(JSONArray ret, String label, JSONObject data) {
        data.put("jjrqsj_start", data.getString("bjdhsj_time_start"));
        data.put("jjrqsj_end", data.getString("bjdhsj_time_end"));
        List<String> bqs = new ArrayList<>();
        bqs.add(label);
        try {
            int week = 0;
            String weekBhs = "";
            int month = 0;
            String mbhs = "";
            int month3 = 0;
            String m3bhs = "";
            int month6 = 0;
            String m6bhs = "";
            int year = 0;
            String ybhs = "";
            int year2 = 0;
            String y2hbs = "";
            int total = 0;
            String thbhs = "";

            String fjs = "";
            String pcss = "";

            String sjrylbs = "";
            ;
            String bjlxs = "";
            String cjjgs = "";
            String cjlbs = "";


            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            int y2 = Integer.parseInt(RIUtil.GetNextDate(today, -365 * 2).replace("-", ""));
            //       System.out.println(y2);
            int y1 = Integer.parseInt(RIUtil.GetNextDate(today, -365).replace("-", ""));
            ;
            //     System.out.println(y1);
            int m6 = Integer.parseInt(RIUtil.GetNextDate(today, -180).replace("-", ""));
            ;
            //     System.out.println(m6);
            int m3 = Integer.parseInt(RIUtil.GetNextDate(today, -90).replace("-", ""));
            ;
            //     System.out.println(m3);
            int m1 = Integer.parseInt(RIUtil.GetNextDate(today, -30).replace("-", ""));
            ;
            //    System.out.println(m1);
            int w = Integer.parseInt(RIUtil.GetNextDate(today, -7).replace("-", ""));
            for (int i = 0; i < ret.size(); i++) {
                JSONObject one = ret.getJSONObject(i);
                String jjbh = one.getString("jjbh");
                String bjsj = one.getString("bjdhsj_time");
                String cjdw = one.getString("jjdw");


                String fj = cjdw.substring(0, 6) + "000000";
                String pcs = cjdw.substring(0, 8) + "0000";

                fjs = fjs + fj + ",";
                pcss = pcss + pcs + ",";

                String bjlx = one.getString("bjlx");
                bjlxs = bjlxs + bjlx + ",";

                String cjjg = one.getString("cjjg");
                cjjgs = cjjgs + cjjg + ",";

                String cjlb = one.getString("cjlb");
                cjlbs = cjlbs + cjlb + ",";

                total++;
                thbhs = thbhs + jjbh + ",";

                //J3204125725022480017
                String jqdate = bjsj.substring(0, 8);
                int jd = Integer.parseInt(jqdate);
                //  System.out.println(jqdate);
                if (jd > y2) {
                    year2++;
                    y2hbs = y2hbs + jjbh + ",";
                }
                if (jd > y1) {
                    year++;
                    ybhs = ybhs + jjbh + ",";
                }
                if (jd > m6) {
                    month6++;
                    m6bhs = m6bhs + jjbh + ",";
                }
                if (jd > m3) {
                    month3++;
                }
                m3bhs = m3bhs + jjbh + ",";
                if (jd > m1) {
                    month++;
                    mbhs = mbhs + jjbh + ",";
                }
                if (jd > w) {
                    week++;
                    weekBhs = weekBhs + jjbh + ",";
                }

            }
            JSONObject one = new JSONObject();
            //week	近一周涉警数
            JSONObject d = new JSONObject();
            d.put("count", week);
            JSONObject opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", weekBhs);
            d.put("opts", opts);
            one.put("week", d);
//month	近一月涉警数
            d = new JSONObject();
            d.put("count", month);
            opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", mbhs);
            d.put("opts", opts);
            one.put("month", d);
//month3	近3个月涉警数
            d = new JSONObject();
            d.put("count", month3);
            opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", m3bhs);
            d.put("opts", opts);
            one.put("month3", d);
//month6	近半年涉警数
            d = new JSONObject();
            d.put("count", month6);
            opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", m6bhs);
            d.put("opts", opts);
            one.put("month6", d);
//year	近一年涉警数
            d = new JSONObject();
            d.put("count", year);
            opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", ybhs);
            d.put("opts", opts);
            one.put("year", d);
//year2	近2年涉警数
            d = new JSONObject();
            d.put("count", year2);
            opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", y2hbs);
            d.put("opts", opts);
            one.put("year2", d);
//total	历史涉警数
            d = new JSONObject();
            d.put("count", total);
            opts = new JSONObject();
            opts.putAll(data);
            opts.put("jjbh", thbhs);
            d.put("opts", opts);

            one.put("total", d);
            System.out.println(one);
//bjlxs	报警类型
            try {
                // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
                String ccjjlbs[] = bjlxs.split(",");
                String lbs = "";
                for (int i = 0; i < ccjjlbs.length; i++) {
                    String lb = ccjjlbs[i].substring(0, 4) + "00";

                    lbs = lbs + "51-" + lb + ",";


                }
                opts = new JSONObject();
                opts.putAll(data);
                opts.put("bzbq", bqs);
                one.put("bjlxs", RIUtil.GetDictCountsOpts(lbs, "bjlx", 0, opts.toString()));
                System.out.println(one.get("bjlxs"));
            } catch (Exception ex) {
                one.put("bjlxs", new JSONArray());
            }
//cjjgs	处警结果
            try {
                // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
                String ccjjlbs[] = cjjgs.split(",");
                String lbs = "";
                for (int i = 0; i < ccjjlbs.length; i++) {
                    String lb = ccjjlbs[i];

                    lbs = lbs + "46-" + lb + ",";


                }
                opts = new JSONObject();
                opts.putAll(data);
                opts.put("bzbq", bqs);
                one.put("cjjgs", RIUtil.GetDictCountsOpts(lbs, "cjjg", 0, opts.toString()));
                System.out.println(one.get("cjjgs"));
            } catch (Exception ex) {
                one.put("cjjgs", new JSONArray());
            }
//处警类别

            try {
                // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
                String ccjjlbs[] = cjlbs.split(",");
                String lbs = "";
                for (int i = 0; i < ccjjlbs.length; i++) {
                    String lb = ccjjlbs[i];
                    if (lb.length() == 6) {
                        lbs = lbs + "50-" + lb + ",";
                    } else {
                        lbs = lbs + "51-" + lb + ",";
                    }

                }
                opts = new JSONObject();
                opts.putAll(data);
                opts.put("bzbq", bqs);
                one.put("cjlbs", RIUtil.GetDictCountsOpts(lbs, "cjlb", 1, opts.toString()));
            } catch (Exception ex) {
                one.put("cjlbs", new JSONArray());
            }
            //处警单位
            try {


                opts = new JSONObject();
                opts.putAll(data);
                opts.put("bzbq", bqs);
                one.put("sjfj", RIUtil.GetDictCountsOpts(fjs, "jjdw", 1, opts.toString()));
                one.put("cjdws", RIUtil.GetDictCountsOpts(pcss, "jjdw", 1, opts.toString()));
            } catch (Exception ex) {
                one.put("cjdws", new JSONArray());
                one.put("sjfj", new JSONArray());
            }


            //标签
            String addMs = "";
            String timeMs = "";
            String reasonMs = "";
            String resultMs = "";
            String toolMs = "";
            String sql = "select jjbh,addressM,timeM,reasonM,resultM,toolM from v_jq_cjxx_bz where jjbh in ('" + thbhs.replace(",", "','") + "') group by jjbh ";
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 999999);
            if (list.size() > 0) {
                JSONArray dets = RIUtil.ListMap2jsa(list);
                for (int i = 0; i < dets.size(); i++) {
                    JSONObject done = dets.getJSONObject(i);
                    try {
                        String ams = done.getString("addressM").replace("[", "").replace("]", "").replace("\"", "");
                        String join = Arrays.stream(ams.split(",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(","));
//                        HashSet<String> strings = new HashSet<>(Arrays.asList(ams.split(",")));
//                        String join = String.join(",", strings);
                        addMs = addMs + join + ",";
                    } catch (Exception ex) {

                    }
                    try {
                        String tms = done.getString("timeM").replace("[", "").replace("]", "").replace("\"", "");
                        String join = Arrays.stream(tms.split(",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(","));
                        timeMs = timeMs + join + ",";
                    } catch (Exception ex) {

                    }
                    try {
                        String reas = done.getString("reasonM").replace("[", "").replace("]", "").replace("\"", "");
                        String join = Arrays.stream(reas.split(",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(","));

                        reasonMs = reasonMs + join + ",";
                    } catch (Exception ex) {

                    }
                    try {
                        String ress = done.getString("resultM").replace("[", "").replace("]", "").replace("\"", "");
                        String join = Arrays.stream(ress.split(",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(","));
                        resultMs = resultMs + join + ",";
                    } catch (Exception ex) {

                    }
                    try {
                        String ts = done.getString("toolM").replace("[", "").replace("]", "").replace("\"", "");
                        String join = Arrays.stream(ts.split(",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(","));
                        toolMs = toolMs + join + ",";
                    } catch (Exception ex) {

                    }

                }
                opts = new JSONObject();
                opts.putAll(data);
                opts.put("bzbq", bqs);
                try {

                    JSONArray adms = RIUtil.GetDictCountsOpts(addMs, "bzbq", 1, opts.toString());


                    one.put("_addressM", adms);
                } catch (Exception ex) {
                    one.put("_addressM", new JSONArray());
                }

                try {
                    //one.put("_resultM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("resultMs"))));
                    one.put("_resultM", RIUtil.GetDictCountsOpts(resultMs, "bzbq", 1, opts.toString()));

                } catch (Exception ex) {
                    one.put("_resultM", new JSONArray());
                }
                try {
                    //one.put("_reasonM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("reasonMs"))));
                    one.put("_reasonM", RIUtil.GetDictCountsOpts(reasonMs, "bzbq", 1, opts.toString()));
                } catch (Exception ex) {
                    one.put("_reasonM", new JSONArray());
                }

                try {
                    // one.put("_toolM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("toolMs"))));
                    one.put("_toolM", RIUtil.GetDictCountsOpts(toolMs, "bzbq", 1, opts.toString()));
                } catch (Exception ex) {
                    one.put("_toolM", new JSONArray());
                }

                try {
                    // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
                    one.put("_timeM", RIUtil.GetDictCountsOpts(timeMs, "bzbq", 1, opts.toString()));
                } catch (Exception ex) {
                    one.put("_timeM", new JSONArray());
                }


            }
            String personMs = "";
            String collect = Arrays.stream(thbhs.split(",")).filter(StringUtils::isNotBlank).map(t -> "'" + t.trim() + "'").collect(Collectors.joining(","));

            String sql2filterCjlb = "select DISTINCT jjbh from wjsc_jq_cjxx where \n" +
                    "\t\t\t(substr( `cjlb`, 1, 2 ) = '01' \t \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '10' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '04' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '98' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '02' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '08' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '06' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '05' ) \n" +
                    "\t\t\tor ( substr(`cjlb`, 1, 2 ) = '09' ) \n" +
                    "\t\t\tor ( substr( `cjlb`, 1, 2 ) = '00' )) \n" +
                    "\tAND\n" +
                    "\t(`cjdw` LIKE '320412%') and jjbh in (" + collect + ") ";

            List<String> list2filterCjlb = jdbcTemplate.queryForList(sql2filterCjlb, String.class);

            //sql = "select personM,sjlb from wjsc_jq_sjxx where jjbh in (" + collect + ")";
            if (list2filterCjlb.isEmpty()) one.put("_personM", new JSONArray());
            else {
                List<WjscJqSjxx> wjscJqSjxxes = wjscJqSjxxMapper.selectList(Wrappers.<WjscJqSjxx>lambdaQuery()
                        .select(WjscJqSjxx::getJjbh, WjscJqSjxx::getPersonM, WjscJqSjxx::getSjlb)
                        .in(WjscJqSjxx::getJjbh, list2filterCjlb));
                Map<String, SjxxTmp> collect2 = wjscJqSjxxes.stream().collect(Collectors.groupingBy(WjscJqSjxx::getJjbh, Collectors.collectingAndThen(
                        Collectors.toList(),
                        sjxxList -> {
                            Set<String> personMList = sjxxList.stream().flatMap(x -> parsePersonM(x.getPersonM()).stream()).collect(Collectors.toSet());
                            Set<String> sjlbList = sjxxList.stream().map(WjscJqSjxx::getSjlb).collect(Collectors.toSet());
                            return SjxxTmp.builder()
                                    .personMList(personMList)
                                    .sjlbList(sjlbList)
                                    .build();
                        }
                )));
                personMs = collect2.values().stream().flatMap(x -> x.getPersonMList().stream()).collect(Collectors.joining(","));
                sjrylbs =  collect2.values().stream().flatMap(x -> x.getSjlbList().stream()).collect(Collectors.joining(","));
//                        String collect1 = list2filterCjlb.stream().map(t -> "'" + t.trim() + "'").collect(Collectors.joining(","));
//            sql = "select sj.jjbh,sj.personM,sjlb from wjsc_jq_sjxx sj  where sj.jjbh in (" + collect1 + ") ";
//            //sql = "select personM,sjlb from wjsc_jq_sjxx where jjbh in ('" + thbhs.replace(",", "','") + "')";
//            list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 999999);
//            if (list.size() > 0) {
//                JSONArray dets = RIUtil.ListMap2jsa(list);
//
//                for (int i = 0; i < dets.size(); i++) {
//                    JSONObject done = dets.getJSONObject(i);
//                    try {
//                        String pms = done.getString("personM").replace("[", "").replace("]", "").replace("\"", "");
//                        String join = Arrays.stream(pms.split(",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(","));
//                        personMs = personMs + join + ",";
//                    } catch (Exception ex) {
//                        log.error("{}<---- 错误", ex);
//                    }
//                    String sjlb = done.getString("sjlb");
//                    sjrylbs = sjrylbs + sjlb + ",";
//                }
//            }
                try {
                    //one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("personMs"))));
                    one.put("_personM", RIUtil.GetDictCountsOpts(personMs, "bzbq", 1, opts.toString()));
                } catch (Exception ex) {
                    one.put("_personM", new JSONArray());
                }
            }

            //rylbs	涉警人员类别
            try {
                // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
                String ccjjlbs[] = sjrylbs.split(",");
                String lbs = "";
                for (int i = 0; i < ccjjlbs.length; i++) {
                    String lb = ccjjlbs[i];

                    lbs = lbs + "55-" + lb + ",";


                }
                opts = new JSONObject();
                opts.putAll(data);
                opts.put("bzbq", bqs);
                one.put("rylbs", RIUtil.GetDictCountsOpts(lbs, "sjlb", 0, opts.toString()));
                System.out.println(one.get("rylbs"));
            } catch (Exception ex) {
                one.put("rylbs", new JSONArray());
            }

            return one;

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
        }
        return null;
    }


    // 警情统计明细警情列表
    @PostMapping("/sta_jq_list")
    @ApiOperation(value = "警情统计明细-警情列表")
    public R<List<RespSjryJqList>> StaJqList(@Valid @RequestBody ReqJQStaQuery params) {
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        return JqStaList(data);

    }

    // 警情统计明细警情列表
    @PostMapping("/sta_jq_list_exp")
    @ApiOperation(value = "警情统计明细-警情列表导出")
    public void StaJqListExp(@Valid @RequestBody ReqJQStaQuery params, HttpServletResponse resp) {
        User user = UserUtils.getUserNull();
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        JqStaListExp(data, resp);

    }

    private void JqStaListExp(JSONObject data, HttpServletResponse resp) {
        R<List<RespSjryJqList>> rets = JqStaList(data);
        List<RespSjryJqList> ret = rets.getData();
        JSONArray dets = new JSONArray();
        Gson gson = new Gson();
        for (int i = 0; i < ret.size(); i++) {
            RespSjryJqList d = ret.get(i);
            JSONObject o = JSONObject.parseObject(gson.toJson(d));
            dets.add(o);

        }
        String heads = "警情编号,涉警人员类别,接警时间,报警类型,处警类别,处警结果,报警人,报警电话,处警单位,简要警情";
        String keys = "jjbh,sjlb,bjdhsj_time,cjlb,cjjg,bjr,lxdh,cjdwmc,cljgnr";


        String fileName = ExportTables(dets, heads, keys, "jq");
        resp = GetResp(fileName, resp);

    }

    private R<List<RespSjryJqList>> JqStaList(JSONObject data) {
        int page = 1;
        int limit = 20;
        {
            String sql = "";

            //接警单位
            String unit = "";
            String relau = data.getString("unit");
            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
                unit = data.getString("jjdw");
            } else {
                unit = data.getString("unit");
            }
            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
            String sq = "";
            for (int i = 0; i < us.size(); i++) {
                String u = us.get(i);

                int type = 23;
                try {
                    type = RIUtil.dicts.get(u).getInteger("type");
                } catch (Exception e) {
                    log.warn(u);
                    log.warn("d.size-->" + RIUtil.dicts.size());

                }
                if (type == 21 || type == 22 || type == 27) {
                    // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
                } else if (type == 23 || type == 24) {
                    sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
                    //sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
//            } else if (type == 25) {
//                sq = sq + "  jjdw='" + u + "' or ";
                } else if (type == 26 || type == 25) {
                    sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
                } else {
                    sq = sq + "  jjdw='" + u + "' or ";
                }
            }
            if (sq.endsWith("or ")) {
                sq = sq.substring(0, sq.length() - 3);
                sql = sql + " and (" + sq + ") ";
            }

//            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
//            String sq = "";
//            for (int i = 0; i < us.size(); i++) {
//                String u = us.get(i);
//
//                int type = 23;
//                try {
//                    type = RIUtil.dicts.get(u).getInteger("type");
//                } catch (Exception e) {
//                    log.warn(u);
//                    log.warn("d.size-->" + RIUtil.dicts.size());
//
//                }
//                if (type == 21 || type == 22 || type == 27) {
//                    // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
//                } else if (type == 23 || type == 24) {
//
//                    //  sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
//                } else if (type == 25) {
//                    sq = sq + "  jjdw='" + u + "' or ";
//                } else if (type == 26) {
//
//                    sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
//                } else {
//                    sq = sq + "  jjdw='" + u + "' or ";
//                }
//            }
//            if (sq.endsWith("or ")) {
//                sq = sq.substring(0, sq.length() - 3);
//                sql = sql + " and (" + sq + ") ";
//            }
            //接警时间开始
            if (data.containsKey("bjdhsj_time_start") && data.getString("bjdhsj_time_start").length() > 0) {
                String bjdhsj_time_start = data.getString("bjdhsj_time_start").replace("-", "").replace(":", "").replace(
                        " ", "");
                ;
                sql = sql + " and bjdhsj_time>='" + bjdhsj_time_start + "' ";
            }
            //接警时间结束
            if (data.containsKey("bjdhsj_time_end") && data.getString("bjdhsj_time_end").length() > 0) {
                String bjdhsj_time_end = data.getString("bjdhsj_time_end").replace("-", "").replace(":", "").replace(" ",
                        "");
                ;
                sql = sql + " and bjdhsj_time<='" + bjdhsj_time_end + "' ";
            }
            //标注时间开始
            if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
                String bz_time_start = data.getString("bz_time_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and bz_time>='" + bz_time_start + "' ";
            }
            //标注时间结束
            if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
                String bz_time_end = data.getString("bz_time_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and bz_time<='" + bz_time_end + "' ";
            }
            //处警类别
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lbs = data.getString("cjlb");
                if (lbs.length() > 3) {
                    HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                    String s = "";
                    for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                        String cjlb = cone.getKey();

                        if (cjlb.contains("-")) {
                            cjlb = cjlb.split("\\-")[1];
                        }
                        if (cjlb.endsWith("000000")) {
                            s = s + "  cjlb like '" + cjlb.substring(0, 2) + "%' or ";
                        } else if (cjlb.endsWith("0000")) {
                            s = s + "  cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                        } else if (cjlb.endsWith("00")) {
                            s = s + "  cjlb like '" + cjlb.substring(0, 6) + "%' or ";
                        } else {

                            s = s + "  cjlb='" + cjlb + "' or ";
                        }
                    }
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }
            //警情标签
            //警情标签
            if (data.containsKey("jqbz") && data.getString("jqbz").length() > 0) {
                String lbs = data.getString("jqbz");
                if (lbs.length() > 3) {
                    HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                    String s = "";
                    for (Map.Entry<String, String> cone : lbss.entrySet()) {
                        String jqbz = cone.getKey();
                        int lev = RIUtil.dicts.get(jqbz).getInteger("static_index");

                        if (lev == 1) {
                            int t = RIUtil.dicts.get(jqbz).getIntValue("type");

                            s = s + " type='" + t + "' or ";

                        } else {
                            String n = RIUtil.dicts.get(jqbz).getString("dict_name");

                            s = s + " memo like '%-" + n + "%' or ";

                        }


                    }
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

            if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
                String bzgs = data.getString("bzgs");
                List<String> gss = RIUtil.StringToArrayList(bzgs);
//                gss = BzgsUtil.fullBzgsList(gss);
                String s = BzgsUtil.convertBzCondition(gss);
//                String s = "";
//                for (int i = 0; i < gss.size(); i++) {
//                    String g = gss.get(i);
//
//                    if (g.equals("(") || g.equals(")") || g.equals("AND") || g.equals("NOT") || g.equals("OR")) {
//
//                        s = s + " " + g + " ";
//
//                    } else {
////                        if ((s.startsWith("AND ") || s.startsWith(") ") || s.startsWith("OR ")) && s.length() > 5) {
////                            s = s + " and ";
////                        }
//                        if ((!StringUtils.endsWith(s, "AND ") && !StringUtils.endsWith(s, "OR ")) && s.length() > 5) {
//                            s = s + " AND ";
//                        }
//                        int t = RIUtil.dicts.get(g).getIntValue("type");
////                        if (g.contains(":") || g.contains("-")){
////                            g = g.replaceAll("[:-]", " +");
////                        }
////                        g = "+" + g;
////                        if (t != 4) {
////                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
////                        } else {
////                            s = s + " jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
////                        }
//                        g = "\"" + g + "\"";
//                        if (t != 4) {
//                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                        } else {
//                            s = s + " jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
//                        }
//                    }
//
//
//                }

                if (s.length() > 3) {
                    s = s.replace("NOT", "!=");
                    System.out.println(s);

                    sql = sql + " and (" + s + ")";
                }

            }
            //bzbq

            if (data.containsKey("bzbq") && data.getString("bzbq").length() > 3) {
//                String bzgs = data.getString("bzbq");
//                List<String> gss = RIUtil.StringToArrayList(bzgs);
//                String s = BzgsUtil.convertBzCondition(gss);
//                if (s.length() > 3) {
//                    s = s.replace("NOT", "!=");
//                    System.out.println(s);
//
//                    sql = sql + " and (" + s + ")";
//                }
                String lbs = data.getString("bzbq");
                System.out.println(lbs);

                String plbs = "";
                String dlbs = "";

                List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                for (int i = 0; i < lbss.size(); i++) {
                    String lb = lbss.get(i);
                    int lbt = RIUtil.dicts.get(lb).getIntValue("type");
                    if (lbt == 4) {
                        plbs = plbs + "+\"" + lb + "\" ";
                    } else {
                        dlbs = dlbs + "\"" + lb + "\" ";
                    }
                }
                String lbsql = "";
                if (dlbs.length() > 2) {
                    lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode ))  ";
                }
                if (plbs.length() > 2) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " or ";
                    }
                    //lbsql = lbsql + " ( MATCH(sj.personMs) AGAINST ('" + plbs + "' in boolean mode ) ) ";
                    lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                            + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";

                }
                if (lbsql.length() > 2) {
                    sql = sql + " and (" + lbsql + ") ";
                }
            }

            //统计层级
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                int level = data.getInteger("level");
                if (level > 0) {
                    sql = sql + " and level = '" + level + "' ";
                }
            }
            String label = "";
            if (data.containsKey("label") && data.getString("label").length() > 0) {
                label = data.getString("label");
                sql = sql + " and label = '" + label + "' ";
            }

            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }

            String sqls = "select jjbh from v_jq_sta_new cj where 1=1 " + sql + " group by jjbh";
            log.warn(sqls);
            String jjbhs = RIUtil.GetStringFListSql(sqls, "jjbh", jdbcTemplate);

            sqls = "select cj.jjbh,bjdhsj_time ,bjlx,cjlb,cjjg,bjr,lxdh,cjdwmc ,cljgnr from " +
                    "wjsc_jq_cjxx cj LEFT JOIN wjsc_jq_jjxx jj on cj.jjbh=jj.jjbh " +
                    "where cj.jjbh in ('" + jjbhs.replace(",", "','") + "')" +
                    " GROUP BY cj.jjbh order by bjdhsj_time desc ";
            log.warn(sqls);


            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespSjryJqList> d = RealJQINfoList(ret);
                sqls =
                        "select count(distinct cj.jjbh) as count from wjsc_jq_cjxx cj" + " LEFT JOIN" + " wjsc_jq_jjxx jj on " + "cj" + ".jjbh=jj.jjbh where cj.jjbh in ('" + jjbhs.replace(",", "','") + "')";
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        }
    }

    private List<RespSjryJqList> RealJQINfoList(JSONArray ret) {
        List<RespSjryJqList> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String jjbh = one.getString("jjbh");

            one.put("sjlb", "");


            if (one.containsKey("bjdhsj_time") && one.getString("bjdhsj_time") != null && one.getString("bjdhsj_time").length() > 0) {
                try {
                    one.put("bjdhsj_time", RIUtil.get_Time(one.getString("bjdhsj_time")));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjlx") && one.getString("bjlx") != null && one.getString("bjlx").length() > 0) {
                try {
                    one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjlb") && one.getString("cjlb") != null && one.getString("cjlb").length() > 0) {
                try {
                    one.put("cjlb", RIUtil.dicts.get("51-" + one.getString("cjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjjg") && one.getString("cjjg") != null && one.getString("cjjg").length() > 0) {
                try {
                    one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            RespSjryJqList dd = gson.fromJson(String.valueOf(one), RespSjryJqList.class);
            back.add(dd);
        }
        return back;
    }


    private HttpServletResponse GetResp(String fileName, HttpServletResponse response) {
        //fileName = "./files/2025/01/jq_20250109164701.xlsx";
        try {
            File file = new File(fileName);
            System.out.println(file.exists());
            long lens = file.length();
            //  response.setContentType("multipart/form-data");
            //   response.setContentLength(lens);


            String fnames[] = fileName.split("/");
            log.warn(fileName);
            String pfile = fnames[fnames.length - 1];
            log.warn(pfile);


            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
//            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Content-Disposition", "attachment; filename=" + pfile);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");


            InputStream dis = new FileInputStream(fileName);
            // OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            // OutputStreamWriter osw=new OutputStreamWriter(outputStream,"gbk");
            // response.setCharacterEncoding("gbk");
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = dis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            // while((len = dis.read()) != -1) {
            // osw.write(len);
            // }
            dis.close();
            // osw.close();
            outputStream.close();
            log.warn("success-->");
        } catch (Exception e) {
            log.error(Lib.getTrace(e));
        }
        return response;

    }

    private String ExportTables(JSONArray datas, String head, String keys, String name) {

        String path = "";
        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(upload_path + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            path = upload_path + filePath + FileName;
            exporthelper.init(path);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            return path;

        } catch (Exception e) {
            log.error(Lib.getTrace(e));
            return "";
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }

    }

    private String GethttpOk(JSONObject one, String token, String url) {

        try {
            OkHttpClient client = new OkHttpClient().newBuilder().readTimeout(1000 * 60, TimeUnit.MILLISECONDS).writeTimeout(1000 * 60, TimeUnit.MILLISECONDS).
                    build();
            MediaType mediaType = MediaType.parse("application/json");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, String.valueOf(one));
            okhttp3.Request request =
                    new Request.Builder().url(url).method("POST", body).addHeader("token", token).addHeader(
                            "Content_Type",
                            "application/json").build();
            okhttp3.Response response = client.newCall(request).execute();
            String ret = response.body().string();
            return ret;
        } catch (Exception e) {
            log.error(Lib.getTrace(e));
            return "";
        }
    }
}
