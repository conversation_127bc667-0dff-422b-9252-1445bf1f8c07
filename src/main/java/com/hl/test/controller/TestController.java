package com.hl.test.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.common.domain.R;

import com.hl.test.Utils.Lib;
import com.hl.test.Utils.OracleHelper;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.*;
import com.hl.test.domain.req.JqbqDataStatisticsTableReq;
import com.hl.test.domain.req.LabelDictReq;
import com.hl.test.domain.req.SpBatchReq;
import com.hl.test.domain.vo.LabelDictDto;
import com.hl.test.mapper.*;
import com.hl.test.service.DictService;
import com.hl.test.service.JqbzService;
import com.hl.test.service.WjJqLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.set.ListOrderedSet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "测试")
@RequestMapping("/test")
@SuppressWarnings("unchecked")
@RequiredArgsConstructor
public class TestController {

//    private final JdbcTemplate jdbcTemplate;
//
//
//    public TestController(JdbcTemplate jdbcTemplate) {
//
//        this.jdbcTemplate = jdbcTemplate;
//
//    }

    private final WjscJqCjxxMapper cjxxMapper;
    private final WjscJqSjxxMapper sjxxMapper;

    private final JqbzService jqbzService;
    private final DictService dictService;
    private final JdbcTemplate jdbcTemplate;
    private final WjJqLabelService wjJqLabelService;
    private final WjJqLabelMapper wjJqLabelMapper;
    private final AddressDmMapper addressDmMapper;

    private final WjscJqCleanMapper wjscJqCleanMapper;

    private final ExecutorService executorService = Executors.newFixedThreadPool(1);

    @PostMapping("/clearWjscJqCjxx")
    public R<?> clearWjscJqCjxx() {
        List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(Wrappers.<WjscJqCjxx>lambdaQuery()
                .isNotNull(WjscJqCjxx::getBzTime));
        log.info("打标警情数量--- {}", wjscJqCjxxes.size());

        List<TmpDictId> tmpDictIds = cjxxMapper.queryTmpDictId();
        Map<String, String> o2n = tmpDictIds.stream().collect(Collectors.toMap(TmpDictId::getOldId, TmpDictId::getNewId));

        List<WjscJqCjxx> needCleanList = wjscJqCjxxes.stream()
                .filter(x -> o2n.keySet().stream().anyMatch(keyword -> x.getAddressm().contains(keyword))).collect(Collectors.toList());

        //List<WjscJqCjxx> test1 = needCleanList.stream().limit(10).collect(Collectors.toList());
        // log.info("改变之前的数据----》 {}", test1.stream().map(WjscJqCjxx::getAddressm).collect(Collectors.toList()));

        List<WjscJqCjxx> cleanList = needCleanList.stream().peek(y -> {
                    Optional<Map.Entry<String, String>> first = o2n.entrySet().stream().filter(entry -> y.getAddressm().contains(entry.getKey())).findFirst();
                    Map.Entry<String, String> s = first.get();
                    y.setAddressm(y.getAddressm().replace(s.getKey(), s.getValue()));
                    y.setJqbz(y.getJqbz().replace(s.getKey(), s.getValue()));
                })
                .collect(Collectors.toList());


        log.info("需要清洗警情数量--- {}", cleanList.size());

        cleanList.forEach(x ->
                cjxxMapper.update(null, new LambdaUpdateWrapper<WjscJqCjxx>()
                        .eq(WjscJqCjxx::getJjbh, x.getJjbh())
                        .eq(WjscJqCjxx::getUuid, x.getUuid())
                        .set(WjscJqCjxx::getAddressm, x.getAddressm())
                        .set(WjscJqCjxx::getJqbz, x.getJqbz()))
        );

        //int count = cjxxMapper.updateBatch(cleanList);

        //log.info("改变之后的数据----》 {}", cleanList.stream().map(WjscJqCjxx::getUuid).collect(Collectors.toList()));
        return R.ok();
    }

    @PostMapping("/clearWjscJqCjxx2")
    public R<?> clearWjscJqCjxx2() {
        List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(Wrappers.<WjscJqCjxx>lambdaQuery()
                .isNotNull(WjscJqCjxx::getBzTime));
        log.info("打标警情数量--- {}", wjscJqCjxxes.size());

        List<TmpDictId> tmpDictIds = cjxxMapper.queryTmpDictId();
        Map<String, String> o2n = tmpDictIds.stream().collect(Collectors.toMap(TmpDictId::getOldId, TmpDictId::getNewId));

        Set<String> values = o2n.values().stream().collect(Collectors.toSet());

        List<WjscJqCjxx> needCleanList = wjscJqCjxxes.stream()
                .filter(x -> values.stream().anyMatch(keyword -> x.getAddressm().contains(keyword))).collect(Collectors.toList());

        //List<WjscJqCjxx> test1 = needCleanList.stream().limit(10).collect(Collectors.toList());
        // log.info("改变之前的数据----》 {}", test1.stream().map(WjscJqCjxx::getAddressm).collect(Collectors.toList()));

        String tmp = "320400312211-CF18424945724C16BC38746AF8DA096D5";
        int temLength = tmp.length();

        List<WjscJqCjxx> newList = new ArrayList<>();
        needCleanList.forEach(y -> {
            HashMap hashMap = RIUtil.StringToList(y.getAddressm());
            Set<String> addremmSet = hashMap.keySet();
            Optional<String> first1 = addremmSet.stream().filter(x -> x.length() == temLength).findFirst();
            first1.ifPresent(x -> {
                y.setAddressm(y.getAddressm().replace(x, x.substring(0, x.length()-1)));
                y.setJqbz(y.getJqbz().replace(x, x.substring(0, x.length()-1)));
                newList.add(y);
            });
//                    Optional<Map.Entry<String, String>> first = o2n.entrySet().stream().filter(entry -> y.getAddressm().contains(entry.getKey())).findFirst();
//                    Map.Entry<String, String> s = first.get();
//                    y.setAddressm(y.getAddressm().replace(s.getKey(), s.getValue()));
//                    y.setJqbz(y.getJqbz().replace(s.getKey(), s.getValue()));
        });


        log.info("需要清洗警情数量--- {}", newList.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList()));

//        newList.forEach(x ->
//                cjxxMapper.update(null, new LambdaUpdateWrapper<WjscJqCjxx>()
//                        .eq(WjscJqCjxx::getJjbh, x.getJjbh())
//                        .eq(WjscJqCjxx::getUuid, x.getUuid())
//                        .set(WjscJqCjxx::getAddressm, x.getAddressm())
//                        .set(WjscJqCjxx::getJqbz, x.getJqbz()))
//        );

        //int count = cjxxMapper.updateBatch(cleanList);

        //log.info("改变之后的数据----》 {}", cleanList.stream().map(WjscJqCjxx::getUuid).collect(Collectors.toList()));
        return R.ok();
    }

    private boolean checkTime(String time) {
        if (time == null || time.length() != 14)
            return false;
        try {
            LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    @PostMapping("/spBatch")
    public R<?> spBatch(@RequestBody SpBatchReq req) {
        LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(WjscJqCjxx::getJjbh)
                .likeRight(WjscJqCjxx::getCjdw, "32041254")
                .ne(WjscJqCjxx::getBzzt, 0)
                .eq(WjscJqCjxx::getSpjg, 0);
        if (checkTime(req.getStartTime())) {
            wrapper.ge(WjscJqCjxx::getDjsjTime, req.getStartTime());
        }
        if (checkTime(req.getEndTime())) {
            wrapper.le(WjscJqCjxx::getDjsjTime, req.getEndTime());
        }
        List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(wrapper);
        log.info("审批通过--- {}", wjscJqCjxxes.size());
        List<String> jjbhList = wjscJqCjxxes.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList());

        List<List<String>> partition = Lists.partition(jjbhList, 1000);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (List<String> batch : partition) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> clean(batch),
                    executorService
            );
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return R.ok();
    }

    private void clean(List<String> jjbhs) {
        cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                .set(WjscJqCjxx::getBzzt, 1)
                .set(WjscJqCjxx::getMark, 1)
                .set(WjscJqCjxx::getSpjg, 1)
                .in(WjscJqCjxx::getJjbh, jjbhs));
        List<String> ids = cjxxMapper.selectJqbz(jjbhs);
        log.info(ids.toString(), "< ----- ids");
        jqbzService.update(Wrappers.<JqBz>lambdaUpdate()
                .set(JqBz::getBzzt, 1)
                .set(JqBz::getMark, 1)
                .in(JqBz::getId, ids));
    }

    @PostMapping("/BatchCleanDictId")
    public void BatchCleanDictId(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqCjxx::getJjbh, WjscJqCjxx::getUuid, WjscJqCjxx::getAddressm, WjscJqCjxx::getJqbz, WjscJqCjxx::getJgbh, WjscJqCjxx::getDwmc, WjscJqCjxx::getDwdz, WjscJqCjxx::getXq)
                    .likeRight(WjscJqCjxx::getCjdw, "320412")
                    .ne(WjscJqCjxx::getBzzt, 0)
                    .like(WjscJqCjxx::getAddressm, ":");
            if (StringUtils.isNotEmpty(req.getJjbh())) {
                wrapper.eq(WjscJqCjxx::getJjbh, req.getJjbh());
            }
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqCjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqCjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(wrapper);

            List<List<WjscJqCjxx>> partition = Lists.partition(wjscJqCjxxes, 1000);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (List<WjscJqCjxx> batch : partition) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        () -> cleanDictId(batch),
                        executorService
                );
                futures.add(future);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }


    @PostMapping("/BatchCleanDictIdPerson")
    public void BatchCleanDictIdPerson(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqSjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqSjxx::getJjbh, WjscJqSjxx::getUuid, WjscJqSjxx::getPersonM, WjscJqSjxx::getPersonMs)
                    .ne(WjscJqSjxx::getPersonM, "")
                    .isNotNull(WjscJqSjxx::getPersonM)
                    .like(WjscJqSjxx::getPersonM, ":");
            if (StringUtils.isNotEmpty(req.getJjbh())) {
                wrapper.eq(WjscJqSjxx::getJjbh, req.getJjbh());
            }
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqSjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqSjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqSjxx> wjscJqSjxxes = sjxxMapper.selectList(wrapper);

            List<List<WjscJqSjxx>> partition = Lists.partition(wjscJqSjxxes, 1000);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (List<WjscJqSjxx> batch : partition) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        () -> cleanDictIdPerson(batch),
                        executorService
                );
                futures.add(future);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }



    @PostMapping("/BatchCleanDictIdPerson2")
    public void BatchCleanDictIdPerson2(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqSjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqSjxx::getJjbh, WjscJqSjxx::getUuid, WjscJqSjxx::getPersonM, WjscJqSjxx::getPersonMs)
                    .ne(WjscJqSjxx::getPersonM, "")
                    .isNotNull(WjscJqSjxx::getPersonM);
            wrapper.eq(StringUtils.isNotEmpty(req.getJjbh()), WjscJqSjxx::getJjbh, req.getJjbh());
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqSjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqSjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqSjxx> wjscJqSjxxes = sjxxMapper.selectList(wrapper);
            List<WjscJqSjxx> needUpdatePersonMs = new ArrayList<>();

            for (WjscJqSjxx wjscJqSjxx : wjscJqSjxxes) {
                String personM = wjscJqSjxx.getPersonM().replaceAll("[\\[\\]\\s\"]", "");
                Set<String> collect = Arrays.stream(personM.split(",")).collect(Collectors.toSet());
                if (collect.isEmpty()) continue;
                String bzs = "";
                Set<String> personMList = new ListOrderedSet<>();
                List<String> newpersonMList = new LinkedList<>();
                for (String id : collect) {
                    Set<String> personMPart = new ListOrderedSet<>();
                    JSONObject jsonObject1 = RIUtil.jqbqs.get(id);
                    if (jsonObject1 == null) continue;
                    String memo = jsonObject1.getString("memo");
                    Integer staticIndex = jsonObject1.getInteger("static_index");
                    String finalId = id;
                    if (memo.contains("人员职业") && staticIndex == 3 ) {
                        Dict dict = dictService.getOne(Wrappers.<Dict>lambdaQuery()
                                .eq(Dict::getFatherId, id)
                                .eq(Dict::getIsdelete, 1)
                                .last("limit 1"));
                        finalId = dict.getId();
                        newpersonMList.add(dict.getId());
                    } else {
                        newpersonMList.add(id);
                    }

                    while (finalId.length() > 0) {
                        try {
                            JSONObject jsonObject = RIUtil.jqbqs.get(finalId);
                            if(jsonObject == null) {
                                personMPart = new ListOrderedSet<>();
                                break;
                            }
                            personMPart.add(finalId);
                            String fid = jsonObject.getString("father_id");
                            if("-1".equals(fid)) break;
                            finalId = fid;
                        } catch (Exception ex) {
                            finalId = "";
                        }
                    }
                    personMList.addAll(personMPart);
                }
                String personMStr = newpersonMList.stream().collect(Collectors.joining(", ", "[", "]"));
                String personMs = personMList.stream().collect(Collectors.joining(" "));
                log.info("jjbh:{} 新的personM------ {}", wjscJqSjxx.getJjbh()+ " " + wjscJqSjxx.getUuid(), personMStr);
                log.info("jjbh:{} 新的personMs------ {}", wjscJqSjxx.getJjbh()+ " " + wjscJqSjxx.getUuid(), personMs);

                Set<String> oldPersonM = Arrays.stream(wjscJqSjxx.getPersonM().replaceAll("[\\[\\]\\s\"]", "").split(",")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toSet());
                Set<String> oldPersonMs = Arrays.stream(wjscJqSjxx.getPersonMs().split(" ")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toSet());
                log.info("旧personM 是否匹配:{} jjbh:{}", oldPersonM.containsAll(newpersonMList) && oldPersonM.size() == newpersonMList.size() ? "是" : "否", wjscJqSjxx.getJjbh()+ " " + wjscJqSjxx.getUuid());
                log.info("旧personMs 是否匹配:{} jjbh:{}", oldPersonMs.containsAll(personMList) && oldPersonMs.size() == personMList.size() ? "是" : "否", wjscJqSjxx.getJjbh()+ " " + wjscJqSjxx.getUuid());
                if(!(oldPersonM.containsAll(newpersonMList) && oldPersonM.size() == newpersonMList.size())) {
                    needUpdatePersonMs.add(WjscJqSjxx.builder()
                            .jjbh(wjscJqSjxx.getJjbh())
                            .uuid(wjscJqSjxx.getUuid())
                            .personM(personMStr)
                            .personMs(personMs)
                            .build());
                }
            }
            log.info("总条数:{}  不匹配的条数:{}  jjbhList:{}", wjscJqSjxxes.size(), needUpdatePersonMs.size(), needUpdatePersonMs.stream().map(WjscJqSjxx::getUuid).collect(Collectors.toList()));
            List<String> total = new ArrayList<>();
            for (WjscJqSjxx needUpdatePersonM : needUpdatePersonMs) {
                int count = sjxxMapper.update(Wrappers.<WjscJqSjxx>lambdaUpdate()
                        .eq(WjscJqSjxx::getJjbh, needUpdatePersonM.getJjbh())
                        .eq(WjscJqSjxx::getUuid, needUpdatePersonM.getUuid())
                        .set(WjscJqSjxx::getPersonM, needUpdatePersonM.getPersonM())
                        .set(WjscJqSjxx::getPersonMs, needUpdatePersonM.getPersonMs()));
                if(count > 0) total.add(needUpdatePersonM.getUuid());
            }
            log.info("修改成功 {} 条数, 还有{}条未更新，uuid为{}", total.size(), needUpdatePersonMs.size() - total.size(), needUpdatePersonMs.stream().map(WjscJqSjxx::getUuid).filter(x -> !total.contains(x)).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }

    private Set<String> getLabels(String labelStr) {
        if (StringUtils.isBlank(labelStr)) return new HashSet<>();
        String labelStr1 = labelStr.replaceAll("[\\[\\]\\s\"]", "");
        return Arrays.stream(labelStr1.split(",")).collect(Collectors.toSet());
    }

    @PostMapping("/BatchCleanReasonM")
    public void BatchCleanReasonM(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqCjxx::getJjbh, WjscJqCjxx::getJqbz, WjscJqCjxx::getReasonm)
                    .ne(WjscJqCjxx::getReasonm, "")
                    .isNotNull(WjscJqCjxx::getReasonm);
            wrapper.eq(StringUtils.isNotEmpty(req.getJjbh()), WjscJqCjxx::getJjbh, req.getJjbh());
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqCjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqCjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(wrapper);
            List<WjscJqClean> wjscJqCleans = wjscJqCleanMapper.selectList(null);
            Map<String, String> old2New = wjscJqCleans.stream().collect(Collectors.toMap(WjscJqClean::getOldId, WjscJqClean::getNewId));

            Set<WjscJqCjxx> needUpdateJqbz = new HashSet<>();
            for (WjscJqCjxx wjscJqCjxx : wjscJqCjxxes) {
                Set<String> labels4Reason = getLabels(wjscJqCjxx.getReasonm());
                Set<String> newLabels4Reason = labels4Reason.stream().map(x -> old2New.getOrDefault(x, x)).collect(Collectors.toSet());

                log.info("jjbh:{} 新的reasonm------ {}", wjscJqCjxx.getJjbh(), newLabels4Reason);
                log.info("jjbh:{} 老的reasonm------ {}", wjscJqCjxx.getJjbh(), labels4Reason);

                if (labels4Reason.containsAll(newLabels4Reason) && newLabels4Reason.containsAll(labels4Reason)) {
                    log.info("jjbh:{}， 新老匹配匹配", wjscJqCjxx.getJjbh());
                    continue;
                }
                String reasonMStr = newLabels4Reason.stream().map(x -> "\"" + x + "\"").collect(Collectors.joining(",", "[", "]"));
                needUpdateJqbz.add(WjscJqCjxx.builder()
                        .jjbh(wjscJqCjxx.getJjbh())
                        .reasonm(reasonMStr)
                        .build());
            }
            log.info("总条数:{}  不匹配的条数:{}  jjbhList:{}", wjscJqCjxxes.size(), needUpdateJqbz.size(), needUpdateJqbz.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList()));
            List<String> total = new ArrayList<>();
            for (WjscJqCjxx e : needUpdateJqbz) {
                int count = cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                        .eq(WjscJqCjxx::getJjbh, e.getJjbh())
                        .set(WjscJqCjxx::getReasonm, e.getReasonm()));
                if(count > 0) total.add(e.getJjbh());
            }
            log.info("修改成功 {} 条数, 还有{}条未更新，jjbh为{}", total.size(), needUpdateJqbz.size() - total.size(), needUpdateJqbz.stream().map(WjscJqCjxx::getJjbh).filter(x -> !total.contains(x)).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }


    @PostMapping("/BatchCleanJqbzField")
    public void BatchCleanJqbzField(@RequestBody SpBatchReq req) {
        List<Dict> labelInfos = dictService.lambdaQuery()
                .select(Dict::getId, Dict::getType, Dict::getIsdelete)
                .in(Dict::getType, Arrays.asList(8, 9))
                .list();
        List<String> needUpdateLabelIds = labelInfos.stream().map(Dict::getId).collect(Collectors.toList());
        try {
            LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqCjxx::getJjbh, WjscJqCjxx::getJqbz, WjscJqCjxx::getToolm, WjscJqCjxx::getReasonm)
                    .ne(WjscJqCjxx::getJqbz, "")
                    .isNotNull(WjscJqCjxx::getJqbz);
            wrapper.eq(StringUtils.isNotEmpty(req.getJjbh()), WjscJqCjxx::getJjbh, req.getJjbh());
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqCjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqCjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(wrapper);
            Set<WjscJqCjxx> needUpdateJqbz = new HashSet<>();
            for (WjscJqCjxx wjscJqCjxx : wjscJqCjxxes) {
                Set<String> newJqbz = new HashSet<>();

                Set<String> labels4Tool = getLabels(wjscJqCjxx.getToolm());
                Set<String> labels4Reason = getLabels(wjscJqCjxx.getReasonm());

                String jqbz = wjscJqCjxx.getJqbz();
                Set<String> oldJqbzList = Arrays.stream(jqbz.split("[,\\s]+"))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());

                Map<Boolean, Set<String>> needUpate = oldJqbzList.stream().collect(Collectors.partitioningBy(needUpdateLabelIds::contains,  Collectors.toSet()));
                Set<String> need = needUpate.get(true);
                Set<String> noNeed = needUpate.get(false);

                Set<String> allLabels = new HashSet<>();
                allLabels.addAll(labels4Tool);
                allLabels.addAll(labels4Reason);

                if (allLabels.isEmpty()) continue;

                Set<String> jqbzList = new ListOrderedSet<>();
                for (String id : allLabels) {
                    Set<String> jqbzPart = new ListOrderedSet<>();
                    JSONObject jsonObject1 = RIUtil.jqbqs.get(id);
                    if (jsonObject1 == null) continue;
                    while (id.length() > 0) {
                        try {
                            JSONObject jsonObject = RIUtil.jqbqs.get(id);
                            if(jsonObject == null) {
                                jqbzPart = new ListOrderedSet<>();
                                break;
                            }
                            jqbzPart.add(id);
                            String fid = jsonObject.getString("father_id");
                            if("-1".equals(fid)) break;
                            id = fid;
                        } catch (Exception ex) {
                            id = "";
                        }
                    }
                    jqbzList.addAll(jqbzPart);
                }
                log.info("jjbh:{} 新的jqbz内toolm和resonm------ {}", wjscJqCjxx.getJjbh(), jqbzList);
                log.info("jjbh:{} 老的jqbz内toolm和resonm------ {}", wjscJqCjxx.getJjbh(), need);

                if (jqbzList.containsAll(need) && need.containsAll(jqbzList)) {
                    log.info("jjbh:{}， 新老匹配匹配", wjscJqCjxx.getJjbh());
                    continue;
                }
                newJqbz.addAll(noNeed);
                newJqbz.addAll(jqbzList);

                Set<String> oldJqbz = oldJqbzList.stream().filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toSet());
                log.info("jjbh:{} 新的jqbz------ {}", wjscJqCjxx.getJjbh(), newJqbz);
                log.info("jjbh:{} 老的jqbz------ {}", wjscJqCjxx.getJjbh(), oldJqbz);
                needUpdateJqbz.add(WjscJqCjxx.builder()
                        .jjbh(wjscJqCjxx.getJjbh())
                        .jqbz(String.join(" ", newJqbz))
                        .build());
            }
            log.info("总条数:{}  不匹配的条数:{}  jjbhList:{}", wjscJqCjxxes.size(), needUpdateJqbz.size(), needUpdateJqbz.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList()));
            List<String> total = new ArrayList<>();
            for (WjscJqCjxx e : needUpdateJqbz) {
                int count = cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                        .eq(WjscJqCjxx::getJjbh, e.getJjbh())
                        .set(WjscJqCjxx::getJqbz, e.getJqbz()));
                if(count > 0) total.add(e.getJjbh());
            }
            log.info("修改成功 {} 条数, 还有{}条未更新，uuid为{}", total.size(), needUpdateJqbz.size() - total.size(), needUpdateJqbz.stream().map(WjscJqCjxx::getJjbh).filter(x -> !total.contains(x)).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }

    private void cleanDictIdPerson(List<WjscJqSjxx> batch) {
        batch.forEach(x -> {
            String pm = x.getPersonM();
            String pms = x.getPersonMs();
            pm = pm.replaceAll(":", "_");
            pms = pms.replaceAll(":", "_");
            log.info("{}<---addressm,{}<------jqbz",pm, pms);
            sjxxMapper.update(Wrappers.<WjscJqSjxx>lambdaUpdate()
                    .eq(WjscJqSjxx::getJjbh, x.getJjbh())
                    .eq(WjscJqSjxx::getUuid, x.getUuid())
                    .set(WjscJqSjxx::getPersonM, pm)
                    .set(WjscJqSjxx::getPersonMs, pms));
        });

    }

    private void cleanDictId(List<WjscJqCjxx> batch) {
        batch.forEach(x -> {
            String addressm = x.getAddressm();
            String jqbz = x.getJqbz();
            addressm = addressm.replaceAll(":", "_");
            jqbz = jqbz.replaceAll(":", "_");
            log.info("{}<---addressm,{}<------jqbz",addressm, jqbz);
            cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                    .eq(WjscJqCjxx::getJjbh, x.getJjbh())
                    .eq(WjscJqCjxx::getUuid, x.getUuid())
                    .set(WjscJqCjxx::getAddressm, addressm)
                    .set(WjscJqCjxx::getJqbz, jqbz));
        });

    }


    @PostMapping("/tongjiBatchClean")
    public void tongjiBatchClean(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqCjxx::getJjbh, WjscJqCjxx::getUuid, WjscJqCjxx::getAddressm, WjscJqCjxx::getJqbz, WjscJqCjxx::getJgbh, WjscJqCjxx::getDwmc, WjscJqCjxx::getDwdz, WjscJqCjxx::getXq)
                    .likeRight(WjscJqCjxx::getCjdw, "320412")
                    .ne(WjscJqCjxx::getBzzt, 0);
            if (StringUtils.isNotEmpty(req.getJjbh())) {
                wrapper.eq(WjscJqCjxx::getJjbh, req.getJjbh());
            }
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqCjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqCjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(wrapper);

            List<Dict> dict4 = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getMemo)
                    .eq(Dict::getType, 3)
                    .eq(Dict::getStaticIndex, 4)
                    .eq(Dict::getIsdelete, 1)
                    .likeRight(Dict::getMemo, "发生部位")
                    .list();
            Map<String, String> dictLevel4Map = dict4.stream().collect(Collectors.toMap(Dict::getId, Dict::getMemo, (vo, v) -> v));

            List<List<WjscJqCjxx>> partition = Lists.partition(wjscJqCjxxes, 1000);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (List<WjscJqCjxx> batch : partition) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        () -> cleanbz(batch, dictLevel4Map),
                        executorService
                );
                futures.add(future);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }


    @PostMapping("/tongjiPersonBatchClean")
    public void tongjiPersonBatchClean(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqSjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqSjxx::getJjbh, WjscJqSjxx::getUuid, WjscJqSjxx::getPersonM, WjscJqSjxx::getPersonMs, WjscJqSjxx::getJgbh, WjscJqSjxx::getDwmc)
                    .ne(WjscJqSjxx::getPersonM, "")
                    .isNotNull(WjscJqSjxx::getPersonM);
            if (StringUtils.isNotEmpty(req.getJjbh())) {
                wrapper.eq(WjscJqSjxx::getJjbh, req.getJjbh());
            }
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqSjxx::getDjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqSjxx::getDjsjTime, req.getEndTime());
            }
            List<WjscJqSjxx> wjscJqSjxxes = sjxxMapper.selectList(wrapper);

            List<Dict> dict4 = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getMemo)
                    .eq(Dict::getType, 4)
                    .eq(Dict::getStaticIndex, 4)
                    .eq(Dict::getIsdelete, 1)
                    .likeRight(Dict::getMemo, "人员职业")
                    .list();
            Map<String, String> dictLevel4Map = dict4.stream().collect(Collectors.toMap(Dict::getId, Dict::getMemo, (vo, v) -> v));

            List<List<WjscJqSjxx>> partition = Lists.partition(wjscJqSjxxes, 1000);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (List<WjscJqSjxx> batch : partition) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        () -> cleanPersonbz(batch, dictLevel4Map),
                        executorService
                );
                futures.add(future);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }

    private void cleanPersonbz(List<WjscJqSjxx> batch, Map<String, String> dictLevel4Map) {
        List<Dict> list1 = dictService.list(Wrappers.<Dict>lambdaQuery()
                .select(Dict::getId, Dict::getDictName, Dict::getFatherId)
                .eq(Dict::getType, 4)
                .eq(Dict::getIsdelete, 1)
                .in(Dict::getStaticIndex, Arrays.asList(1, 2, 3, 4)));
        Map<String, Dict> collect1 = list1.stream().collect(Collectors.toMap(Dict::getId, x -> x));

        List<Dict> list2 = dictService.list(Wrappers.<Dict>lambdaQuery()
                .select(Dict::getId, Dict::getDictName)
                .eq(Dict::getType, 4)
                .eq(Dict::getIsdelete, 1)
                .in(Dict::getStaticIndex, Arrays.asList(1, 2, 3)));
        List<String> groundIds = list2.stream().map(Dict::getId).collect(Collectors.toList());
        List<String> groundNames = list1.stream().map(Dict::getDictName).collect(Collectors.toList());

        List<WjscJqSjxx> level4Jqxx = new ArrayList<>();
        List<WjscJqSjxx> level5Jqxx = new ArrayList<>();
        List<Dict> level4Dict = new ArrayList<>();
        List<Dict> level5Dict = new ArrayList<>();
//        //insert
//        List<WjJqLabel> level4JqLabel = new ArrayList<>();
//        //update
//        List<WjJqLabel> level5JqLabel = new ArrayList<>();

//        List<String> jjbhList = batch.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList());
//        List<WjJqLabel> labelList = wjJqLabelService.lambdaQuery()
//                .in(WjJqLabel::getJjbh, jjbhList)
//                .list();
        // Map<String, List<WjJqLabel>> jjbh2List = labelList.stream().collect(Collectors.groupingBy(WjJqLabel::getJjbh));

        batch.forEach(x -> {
            // List<WjJqLabel> wjJqLabels = jjbh2List.get(x.getJjbh());
            if (StringUtils.isEmpty(x.getJgbh()) || StringUtils.isEmpty(x.getDwmc())) return;
//            long count = dictService.count(Wrappers.<Dict>lambdaQuery()
//                    .eq(Dict::getId, x.getJgbh())
//                    .eq(Dict::getType, 4)
//                    .eq(Dict::getIsdelete, 1)
//                    .in(Dict::getStaticIndex, Arrays.asList(1, 2, 3)));
            boolean c1 = groundIds.contains(x.getJgbh());
            boolean c2 = groundNames.contains(x.getDwmc());

//            long count2 = dictService.count(Wrappers.<Dict>lambdaQuery()
//                    .eq(Dict::getDictName, x.getDwmc())
//                    .eq(Dict::getType, 4)
//                    .eq(Dict::getIsdelete, 1)
//                    .in(Dict::getStaticIndex, Arrays.asList(1, 2, 3, 4)));
            if (c1 || c2 ) return;
            String personM = x.getPersonM();
            if (StringUtils.isEmpty(personM)) return;
            HashMap hashMap = RIUtil.StringToList(personM);
            Set<String> personSet = hashMap.keySet();
            if (personSet.isEmpty()) return;
            List<Dict> list = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getFatherId, Dict::getMemo, Dict::getStaticIndex, Dict::getDictName)
                    .eq(Dict::getType, 4)
                    .likeRight(Dict::getMemo, "人员职业")
                    .in(Dict::getId, personSet)
                    .list();
            Map<Integer, List<Dict>> collect = list.stream().collect(Collectors.groupingBy(Dict::getStaticIndex));
            List<Dict> level4List = collect.get(4);
            List<Dict> level5List = collect.get(5);
            // Map<String, List<WjJqLabel>> label2List = wjJqLabels.stream().collect(Collectors.groupingBy(WjJqLabel::getLabel));
            if(!CollectionUtils.isEmpty(level4List) && level4List.stream().noneMatch(y -> y.getId().equals(x.getJgbh()) || y.getDictName().equals(x.getDwmc()))) {
                for (Dict four : level4List) {
                    String dwmc = x.getDwmc();
                    String jgbh = x.getJgbh();
                    String dwdz = "";
                    if (dwmc.contains("[")) {
                        dwdz = dwmc.split("\\[")[1];
                        dwmc = dwmc.split("\\[")[0];
                    }

                    String newId = jgbh + ":" + four.getId();


                    String personMRep = x.getPersonM().replace(four.getId(), newId);

                    String personMs = x.getPersonMs();
                    if(personMs.contains(jgbh + " ")) {personMs = personMs.replace(jgbh + " ", "");}
                    String personMSRep =  personMs.replace(four.getId(), newId + " " + four.getId());
                    level4Jqxx.add(WjscJqSjxx.builder()
                            .jjbh(x.getJjbh())
                            .uuid(x.getUuid())
                            .personM(personMRep)
                            .personMs(personMSRep)
                            .build());
                    // todo dict表变动
//                            String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
//                                    "remark,type,up_time)" + " values('" + newId + "','" + dwmc + "','" + id + "'," +
//                                    "'jqbq',"
//                                    + "'5','" + memo + "','" + dwdz + "','3','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "')";



                    Dict dict = Dict.builder()
                            .id(newId)
                            .dictName(dwmc)
                            .fatherId(four.getId())
                            .staticIndex(5)
                            .type(4)
                            .permission("jqbq")
                            .memo(four.getMemo() + "-" + dwmc)
                            .remark(dwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    level4Dict.add(dict);

                    // todo wj_jq_label表变动
//                            List<WjJqLabel> wjJqLabels1 = label2List.get(four.getId());
//                            if (!CollectionUtils.isEmpty(wjJqLabels1)) {
//                                wjJqLabels1.forEach(r -> {
//                                    if (r.getLabel().equals(four.getId())) {
//                                        level4JqLabel.add(WjJqLabel.builder()
//                                                .jjbh(x.getJjbh())
//                                                .label(x.getJgbh() + ":" + four.getId())
//                                                .isDeleted(0)
//                                                .updateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                                .build());
//                                    }
//                                });
//                            }
                };
            }
            if(!CollectionUtils.isEmpty(level5List)) {
                level5List.forEach(five -> {
                    String personMs = x.getPersonMs();
                    if(five.getId().contains(":")) {
                        List<String> list4 = Arrays.asList(personMs.split(" "));
                        int s9 = list4.indexOf(five.getId());
                        int e9 = list4.indexOf("AE278EDE78AC4C17A9612F38F42A804C");
                        if(personMs.contains(" " + five.getFatherId() + " ") && (e9 - s9 + 1) <= 4) return;
                        else {
                            List<String> list3 = new ArrayList<>();
                            String tmpId = five.getFatherId();
                            list3.add(tmpId);
                            while (!tmpId.equals("AE278EDE78AC4C17A9612F38F42A804C")) {
                                Dict dict = collect1.get(tmpId);
                                tmpId = dict.getFatherId();
                                list3.add(tmpId);
                            }
                            log.info("{}<----list3", list3);
                            String s = replacePms(personMs, five.getId(), "AE278EDE78AC4C17A9612F38F42A804C", list3);
                            personMs = s;
                        }
                        //personMs = personMs.replace(five.getId(), five.getId() + " " + five.getFatherId());
                    }
                    String dwmc = x.getDwmc();
                    String jgbh = x.getJgbh();
                    if (StringUtils.isEmpty(jgbh) || StringUtils.isEmpty(dwmc)) return;

                    String dwdz = "";
                    if (dwmc.contains("[")) {
                        dwdz = dwmc.split("\\[")[1];
                        dwmc = dwmc.split("\\[")[0];
                    }

                    String newId = jgbh + ":" + five.getFatherId();
                    String personMRep = x.getPersonM().replace(five.getId(), newId);
                    String personMSRep = personMs.replace(five.getId(), newId);
                    level5Jqxx.add(WjscJqSjxx.builder()
                            .jjbh(x.getJjbh())
                            .uuid(x.getUuid())
                            .personM(personMRep)
                            .personMs(personMSRep)
                            .build());
                    // todo wj_jq_label表变动
//                            List<WjJqLabel> wjJqLabels1 = label2List.get(five.getId());
//                            if (!CollectionUtils.isEmpty(wjJqLabels1)) {
//                                wjJqLabels1.forEach(r -> {
//                                    if (r.getLabel().equals(five.getId())) {
//                                        level5JqLabel.add(WjJqLabel.builder()
//                                                .id(r.getId())
//                                                .jjbh(x.getJjbh())
//                                                .label(five.getId() + ":" + five.getFatherId())
//                                                .build());
//                                    }
//                                });
//                            }

                    Dict dict = Dict.builder()
                            .id(newId)
                            .dictName(dwmc)
                            .fatherId(five.getFatherId())
                            .staticIndex(5)
                            .type(4)
                            .permission("jqbq")
                            .memo(dictLevel4Map.getOrDefault(five.getFatherId(), StringUtils.substringBeforeLast(five.getMemo(), "-")) + "-" + dwmc)
                            .remark(dwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    level5Dict.add(dict);
                });
            }
        });
        // 对于地址标签选到第四层的数据
        //  wjJqLabelService.saveBatch(level4JqLabel);

        // 对于地址标签选到第五层的数据
        //    wjJqLabelMapper.updateBatch(level5JqLabel);

        log.info("{}<------level4Jqxx", level4Jqxx);
        log.info("{}<------level4Dict", level4Dict);

        log.info("{}<------level5Jqxx", level5Jqxx);
        log.info("{}<------level5Dict", level5Dict);

//        for (WjscJqCjxx jqxx : level4Jqxx) {
//            String addressm = jqxx.getAddressm();
//            try {
//                JSONArray objects = JSONArray.parseArray(addressm);
//            } catch (Exception e) {
//                log.error("格式错误-4");
//                log.error(jqxx.toString());
//            }
//        }
//
//
//
//        for (WjscJqCjxx jqxx : level5Jqxx) {
//            String addressm = jqxx.getAddressm();
//            try {
//                JSONArray objects = JSONArray.parseArray(addressm);
//            } catch (Exception e) {
//                log.error("格式错误-5");
//                log.error(jqxx.toString());
//            }
//
//        }

//
//        level4Jqxx.forEach(level4 ->
//                sjxxMapper.update(Wrappers.<WjscJqSjxx>lambdaUpdate()
//                        .eq(WjscJqSjxx::getJjbh, level4.getJjbh())
//                        .eq(WjscJqSjxx::getUuid, level4.getUuid())
//                        .set(WjscJqSjxx::getPersonM, level4.getPersonM())
//                        .set(WjscJqSjxx::getPersonMs, level4.getPersonMs()))
//        );
//
//        // cjxxMapper.updateBatch(level4Jqxx);
//        dictService.saveOrUpdateBatch(level4Dict);
//
//
//
//        level5Jqxx.forEach(level5 ->
//                sjxxMapper.update(Wrappers.<WjscJqSjxx>lambdaUpdate()
//                        .eq(WjscJqSjxx::getJjbh, level5.getJjbh())
//                        .eq(WjscJqSjxx::getUuid, level5.getUuid())
//                        .set(WjscJqSjxx::getPersonM, level5.getPersonM())
//                        .set(WjscJqSjxx::getPersonMs, level5.getPersonMs()))
//        );
//        //cjxxMapper.updateBatch(level5Jqxx);
//        dictService.saveOrUpdateBatch(level5Dict);

    }

    private String replacePms(String oldPms, String start, String end, List<String> newP) {
        List<String> split = Arrays.asList(oldPms.split(" "));
        int startIndex = split.indexOf(start);
        int endIndex = split.indexOf(end);

        if(startIndex == -1 || endIndex == -1 || startIndex>endIndex) {
            return oldPms;
        }
        ArrayList<String> list = new ArrayList<>(split);
        list.subList(startIndex + 1, endIndex + 1).clear();
        list.addAll(startIndex + 1, newP);
        //log.info("{} <----- new pms", String.join(" ", list));
        return String.join(" ", list);
    }

    private void cleanbz(List<WjscJqCjxx> batch, Map<String, String> dictLevel4Map) {
        List<WjscJqCjxx> level4Jqxx = new ArrayList<>();
        List<WjscJqCjxx> level5Jqxx = new ArrayList<>();
        List<Dict> level4Dict = new ArrayList<>();
        List<Dict> level5Dict = new ArrayList<>();
//        //insert
//        List<WjJqLabel> level4JqLabel = new ArrayList<>();
//        //update
//        List<WjJqLabel> level5JqLabel = new ArrayList<>();

//        List<String> jjbhList = batch.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList());
//        List<WjJqLabel> labelList = wjJqLabelService.lambdaQuery()
//                .in(WjJqLabel::getJjbh, jjbhList)
//                .list();
        // Map<String, List<WjJqLabel>> jjbh2List = labelList.stream().collect(Collectors.groupingBy(WjJqLabel::getJjbh));

        batch.forEach(x -> {
            // List<WjJqLabel> wjJqLabels = jjbh2List.get(x.getJjbh());
            String addressm = x.getAddressm();
            if (StringUtils.isEmpty(addressm)) return;
            HashMap hashMap = RIUtil.StringToList(addressm);
            Set<String> addressSet = hashMap.keySet();
            if (addressSet.isEmpty()) return;
            List<Dict> list = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getFatherId, Dict::getMemo, Dict::getStaticIndex)
                    .eq(Dict::getType, 3)
                    .likeRight(Dict::getMemo, "发生部位")
                    .in(Dict::getId, addressSet)
                    .list();
            Map<Integer, List<Dict>> collect = list.stream().collect(Collectors.groupingBy(Dict::getStaticIndex));
            List<Dict> level4List = collect.get(4);
            List<Dict> level5List = collect.get(5);
            // Map<String, List<WjJqLabel>> label2List = wjJqLabels.stream().collect(Collectors.groupingBy(WjJqLabel::getLabel));
            if(!CollectionUtils.isEmpty(level4List)) {
                level4List.forEach(four -> {
                    String dwmc = x.getDwmc();
                    String jgbh = x.getJgbh();
                    String dwdz = x.getDwdz();
                    if (StringUtils.isEmpty(jgbh) || StringUtils.isEmpty(dwmc)) return;


                    String xq = x.getXq();
                    if (xq != null && xq.length() > 2) {
                        String xqmc = Optional.ofNullable(addressDmMapper.selectOne(Wrappers.<AddressDm>lambdaQuery()
                                        .select(AddressDm::getDmmc)
                                        .eq(AddressDm::getDmdm, xq)))
                                .map(AddressDm::getDmmc)
                                .orElse(dwmc);
//                                String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
//                                String xqmc = Optional.ofNullable(jdbcTemplate.queryForObject(xqmcSql, String.class)).orElse(dwmc);
                        if (dwmc.contains("幢") || dwmc.contains("号")) {
                            jgbh = xq;
                            dwmc = xqmc;
                            dwdz = xqmc;
                        }
                    }
                    String newId = jgbh + ":" + four.getId();


                    String addressRep = x.getAddressm().replace(four.getId(), newId);
                    String jqbzRep =  x.getJqbz().replace(four.getId(), newId + " " + four.getId());
                    level4Jqxx.add(WjscJqCjxx.builder()
                            .jjbh(x.getJjbh())
                            .uuid(x.getUuid())
                            .addressm(addressRep)
                            .jqbz(jqbzRep)
                            .build());
                    // todo dict表变动
//                            String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
//                                    "remark,type,up_time)" + " values('" + newId + "','" + dwmc + "','" + id + "'," +
//                                    "'jqbq',"
//                                    + "'5','" + memo + "','" + dwdz + "','3','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "')";



                    Dict dict = Dict.builder()
                            .id(newId)
                            .dictName(dwmc)
                            .fatherId(four.getId())
                            .staticIndex(5)
                            .type(3)
                            .permission("jqbq")
                            .memo(four.getMemo() + "-" + dwmc)
                            .remark(dwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    level4Dict.add(dict);

                    // todo wj_jq_label表变动
//                            List<WjJqLabel> wjJqLabels1 = label2List.get(four.getId());
//                            if (!CollectionUtils.isEmpty(wjJqLabels1)) {
//                                wjJqLabels1.forEach(r -> {
//                                    if (r.getLabel().equals(four.getId())) {
//                                        level4JqLabel.add(WjJqLabel.builder()
//                                                .jjbh(x.getJjbh())
//                                                .label(x.getJgbh() + ":" + four.getId())
//                                                .isDeleted(0)
//                                                .updateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                                .build());
//                                    }
//                                });
//                            }
                });
            }
            if(!CollectionUtils.isEmpty(level5List)) {
                level5List.forEach(five -> {
                    if(five.getId().contains(":")) return;
                    String dwmc = x.getDwmc();
                    String jgbh = x.getJgbh();
                    String dwdz = x.getDwdz();
                    if (StringUtils.isEmpty(jgbh) || StringUtils.isEmpty(dwmc)) return;
                    String xq = x.getXq();
                    if (xq != null && xq.length() > 2) {
                        String xqmc = Optional.ofNullable(addressDmMapper.selectOne(Wrappers.<AddressDm>lambdaQuery()
                                        .select(AddressDm::getDmmc)
                                        .eq(AddressDm::getDmdm, xq)))
                                .map(AddressDm::getDmmc)
                                .orElse(dwmc);
//                                String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
//                                String xqmc = Optional.ofNullable(jdbcTemplate.queryForObject(xqmcSql, String.class)).orElse(dwmc);
                        if (dwmc.contains("幢") || dwmc.contains("号")) {
                            jgbh = xq;
                            dwmc = xqmc;
                            dwdz = xqmc;
                        }
                    }
                    String newId = jgbh + ":" + five.getFatherId();
                    String addressRep = x.getAddressm().replace(five.getId(), newId);
                    String jqbzRep = x.getJqbz().replace(five.getId(), newId);
                    level5Jqxx.add(WjscJqCjxx.builder()
                            .jjbh(x.getJjbh())
                            .uuid(x.getUuid())
                            .addressm(addressRep)
                            .jqbz(jqbzRep)
                            .build());
                    // todo wj_jq_label表变动
//                            List<WjJqLabel> wjJqLabels1 = label2List.get(five.getId());
//                            if (!CollectionUtils.isEmpty(wjJqLabels1)) {
//                                wjJqLabels1.forEach(r -> {
//                                    if (r.getLabel().equals(five.getId())) {
//                                        level5JqLabel.add(WjJqLabel.builder()
//                                                .id(r.getId())
//                                                .jjbh(x.getJjbh())
//                                                .label(five.getId() + ":" + five.getFatherId())
//                                                .build());
//                                    }
//                                });
//                            }

                    Dict dict = Dict.builder()
                            .id(newId)
                            .dictName(dwmc)
                            .fatherId(five.getFatherId())
                            .staticIndex(5)
                            .type(3)
                            .permission("jqbq")
                            .memo(dictLevel4Map.getOrDefault(five.getFatherId(), StringUtils.substringBeforeLast(five.getMemo(), "-")) + "-" + dwmc)
                            .remark(dwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    level5Dict.add(dict);
                });
            }
        });
        // 对于地址标签选到第四层的数据
        //  wjJqLabelService.saveBatch(level4JqLabel);

        // 对于地址标签选到第五层的数据
        //    wjJqLabelMapper.updateBatch(level5JqLabel);

        log.info("{}<------level4Jqxx", level4Jqxx);
        log.info("{}<------level4Dict", level4Dict);

        log.info("{}<------level5Jqxx", level5Jqxx);
        log.info("{}<------level5Dict", level5Dict);

//        for (WjscJqCjxx jqxx : level4Jqxx) {
//            String addressm = jqxx.getAddressm();
//            try {
//                JSONArray objects = JSONArray.parseArray(addressm);
//            } catch (Exception e) {
//                log.error("格式错误-4");
//                log.error(jqxx.toString());
//            }
//        }
//
//
//
//        for (WjscJqCjxx jqxx : level5Jqxx) {
//            String addressm = jqxx.getAddressm();
//            try {
//                JSONArray objects = JSONArray.parseArray(addressm);
//            } catch (Exception e) {
//                log.error("格式错误-5");
//                log.error(jqxx.toString());
//            }
//
//        }


        level4Jqxx.forEach(level4 ->
                cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                        .eq(WjscJqCjxx::getJjbh, level4.getJjbh())
                        .eq(WjscJqCjxx::getUuid, level4.getUuid())
                        .set(WjscJqCjxx::getAddressm, level4.getAddressm())
                        .set(WjscJqCjxx::getJqbz, level4.getJqbz()))
        );

        // cjxxMapper.updateBatch(level4Jqxx);
        dictService.saveOrUpdateBatch(level4Dict);



        level5Jqxx.forEach(level5 ->
                cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                        .eq(WjscJqCjxx::getJjbh, level5.getJjbh())
                        .eq(WjscJqCjxx::getUuid, level5.getUuid())
                        .set(WjscJqCjxx::getAddressm, level5.getAddressm())
                        .set(WjscJqCjxx::getJqbz, level5.getJqbz()))
        );
        //cjxxMapper.updateBatch(level5Jqxx);
        dictService.saveOrUpdateBatch(level5Dict);

    }


    @PostMapping("/cleanAddress0605")
    public void cleanAddress0605(@RequestBody SpBatchReq req) {
        try {
            LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WjscJqCjxx::getJjbh, WjscJqCjxx::getUuid, WjscJqCjxx::getAddressm, WjscJqCjxx::getJqbz, WjscJqCjxx::getJgbh, WjscJqCjxx::getDwmc, WjscJqCjxx::getDwdz, WjscJqCjxx::getXq)
                    .likeRight(WjscJqCjxx::getCjdw, "320412")
                    .ne(WjscJqCjxx::getBzzt, 0);
            if (StringUtils.isNotEmpty(req.getJjbh())) {
                wrapper.eq(WjscJqCjxx::getJjbh, req.getJjbh());
            }
            if (checkTime(req.getStartTime())) {
                wrapper.ge(WjscJqCjxx::getCjsjTime, req.getStartTime());
            }
            if (checkTime(req.getEndTime())) {
                wrapper.le(WjscJqCjxx::getCjsjTime, req.getEndTime());
            }
            List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(wrapper);

            List<Dict> dict4 = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getMemo)
                    .eq(Dict::getType, 3)
                    .eq(Dict::getStaticIndex, 4)
                    .eq(Dict::getIsdelete, 1)
                    .likeRight(Dict::getMemo, "发生部位")
                    .list();
            Map<String, String> dictLevel4Map = dict4.stream().collect(Collectors.toMap(Dict::getId, Dict::getMemo, (vo, v) -> v));

            List<List<WjscJqCjxx>> partition = Lists.partition(wjscJqCjxxes, 1000);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (List<WjscJqCjxx> batch : partition) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        () -> doCleanAddress0605(batch, dictLevel4Map),
                        executorService
                );
                futures.add(future);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("tongjiBatchClean error--- {}", e);
        }
    }

    @PostMapping("/jqbqDataStatisticsTable")
    @ApiOperation(value = "警情标签数据统计表")
    public R<?> jqbqDataStatisticsTable(@RequestBody JqbqDataStatisticsTableReq req){
        return R.ok(jqbzService.jqbqDataStatisticsTable2(req));
    }

    private void doCleanAddress0605(List<WjscJqCjxx> batch, Map<String, String> dictLevel4Map) {
        List<WjscJqCjxx> level4Jqxx = new ArrayList<>();
        List<WjscJqCjxx> level5Jqxx = new ArrayList<>();
        List<Dict> level4Dict = new ArrayList<>();
        List<Dict> level5Dict = new ArrayList<>();
        batch.forEach(x -> {
            // List<WjJqLabel> wjJqLabels = jjbh2List.get(x.getJjbh());
            String addressm = x.getAddressm();
            if (StringUtils.isEmpty(addressm)) return;
            HashMap hashMap = RIUtil.StringToList(addressm);
            Set<String> addressSet = hashMap.keySet();
            if (addressSet.isEmpty()) return;
            addressSet.stream().filter(y -> y.contains(":"))
                    .forEach(z -> {
                                long count = dictService.count(Wrappers.<Dict>lambdaQuery()
                                        .eq(Dict::getType, 3)
                                        .likeRight(Dict::getMemo, "发生部位")
                                        .eq(Dict::getId, z));
                                if (count == 0) {
                                    String dwmc = x.getDwmc();
                                    String jgbh = x.getJgbh();
                                    String dwdz = x.getDwdz();
                                    if (StringUtils.isEmpty(jgbh) || StringUtils.isEmpty(dwmc)) return;
                                    String xq = x.getXq();
                                    if (xq != null && xq.length() > 2) {
                                        String xqmc = Optional.ofNullable(addressDmMapper.selectOne(Wrappers.<AddressDm>lambdaQuery()
                                                        .select(AddressDm::getDmmc)
                                                        .eq(AddressDm::getDmdm, xq)))
                                                .map(AddressDm::getDmmc)
                                                .orElse(dwmc);
//                                String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
//                                String xqmc = Optional.ofNullable(jdbcTemplate.queryForObject(xqmcSql, String.class)).orElse(dwmc);
                                        if (dwmc.contains("幢") || dwmc.contains("号")) {
                                            jgbh = xq;
                                            dwmc = xqmc;
                                            dwdz = xqmc;
                                        }
                                        String[] split = z.split(":");
                                        String fatherId = split[1];
                                        Dict dict = Dict.builder()
                                                .id(z)
                                                .dictName(dwmc)
                                                .fatherId(fatherId)
                                                .staticIndex(5)
                                                .type(3)
                                                .permission("jqbq")
                                                .memo(dictLevel4Map.get(fatherId) + "-" + dwmc)
                                                .remark(dwdz)
                                                .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                                                .build();
                                        log.info("需要插入的 dict ---- {}" , dict);
                                        dictService.saveOrUpdate(dict);
                                    }
                                }
                            }

                    );
            List<Dict> list = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getFatherId, Dict::getMemo, Dict::getStaticIndex)
                    .eq(Dict::getType, 3)
                    .likeRight(Dict::getMemo, "发生部位")
                    .in(Dict::getId, addressSet)
                    .list();
            Map<Integer, List<Dict>> collect = list.stream().collect(Collectors.groupingBy(Dict::getStaticIndex));
            List<Dict> level4List = collect.get(4);
            List<Dict> level5List = collect.get(5);
            // Map<String, List<WjJqLabel>> label2List = wjJqLabels.stream().collect(Collectors.groupingBy(WjJqLabel::getLabel));
            if(!CollectionUtils.isEmpty(level4List)) {
                level4List.forEach(four -> {
                    String dwmc = x.getDwmc();
                    String jgbh = x.getJgbh();
                    String dwdz = x.getDwdz();
                    if (StringUtils.isEmpty(jgbh) || StringUtils.isEmpty(dwmc)) return;


                    String xq = x.getXq();
                    if (xq != null && xq.length() > 2) {
                        String xqmc = Optional.ofNullable(addressDmMapper.selectOne(Wrappers.<AddressDm>lambdaQuery()
                                        .select(AddressDm::getDmmc)
                                        .eq(AddressDm::getDmdm, xq)))
                                .map(AddressDm::getDmmc)
                                .orElse(dwmc);
//                                String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
//                                String xqmc = Optional.ofNullable(jdbcTemplate.queryForObject(xqmcSql, String.class)).orElse(dwmc);
                        if (dwmc.contains("幢") || dwmc.contains("号")) {
                            jgbh = xq;
                            dwmc = xqmc;
                            dwdz = xqmc;
                        }
                    }
                    String newId = jgbh + ":" + four.getId();


                    String addressRep = x.getAddressm().replace(four.getId(), newId);
                    String jqbzRep =  x.getJqbz().replace(four.getId(), newId + " " + four.getId());
                    level4Jqxx.add(WjscJqCjxx.builder()
                            .jjbh(x.getJjbh())
                            .uuid(x.getUuid())
                            .addressm(addressRep)
                            .jqbz(jqbzRep)
                            .build());
                    // todo dict表变动
//                            String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
//                                    "remark,type,up_time)" + " values('" + newId + "','" + dwmc + "','" + id + "'," +
//                                    "'jqbq',"
//                                    + "'5','" + memo + "','" + dwdz + "','3','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "')";



                    Dict dict = Dict.builder()
                            .id(newId)
                            .dictName(dwmc)
                            .fatherId(four.getId())
                            .staticIndex(5)
                            .type(3)
                            .permission("jqbq")
                            .memo(four.getMemo() + "-" + dwmc)
                            .remark(dwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    level4Dict.add(dict);

                    // todo wj_jq_label表变动
//                            List<WjJqLabel> wjJqLabels1 = label2List.get(four.getId());
//                            if (!CollectionUtils.isEmpty(wjJqLabels1)) {
//                                wjJqLabels1.forEach(r -> {
//                                    if (r.getLabel().equals(four.getId())) {
//                                        level4JqLabel.add(WjJqLabel.builder()
//                                                .jjbh(x.getJjbh())
//                                                .label(x.getJgbh() + ":" + four.getId())
//                                                .isDeleted(0)
//                                                .updateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                                .build());
//                                    }
//                                });
//                            }
                });
            }
            if(!CollectionUtils.isEmpty(level5List)) {
                level5List.forEach(five -> {
                    if(five.getId().contains(":")) return;
                    String dwmc = x.getDwmc();
                    String jgbh = x.getJgbh();
                    String dwdz = x.getDwdz();
                    if (StringUtils.isEmpty(jgbh) || StringUtils.isEmpty(dwmc)) return;
                    String xq = x.getXq();
                    if (xq != null && xq.length() > 2) {
                        String xqmc = Optional.ofNullable(addressDmMapper.selectOne(Wrappers.<AddressDm>lambdaQuery()
                                        .select(AddressDm::getDmmc)
                                        .eq(AddressDm::getDmdm, xq)))
                                .map(AddressDm::getDmmc)
                                .orElse(dwmc);
//                                String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
//                                String xqmc = Optional.ofNullable(jdbcTemplate.queryForObject(xqmcSql, String.class)).orElse(dwmc);
                        if (dwmc.contains("幢") || dwmc.contains("号")) {
                            jgbh = xq;
                            dwmc = xqmc;
                            dwdz = xqmc;
                        }
                    }
                    String newId = jgbh + ":" + five.getFatherId();
                    String addressRep = x.getAddressm().replace(five.getId(), newId);
                    String jqbzRep = x.getJqbz().replace(five.getId(), newId);
                    level5Jqxx.add(WjscJqCjxx.builder()
                            .jjbh(x.getJjbh())
                            .uuid(x.getUuid())
                            .addressm(addressRep)
                            .jqbz(jqbzRep)
                            .build());

                    Dict dict = Dict.builder()
                            .id(newId)
                            .dictName(dwmc)
                            .fatherId(five.getFatherId())
                            .staticIndex(5)
                            .type(3)
                            .permission("jqbq")
                            .memo(dictLevel4Map.getOrDefault(five.getFatherId(), StringUtils.substringBeforeLast(five.getMemo(), "-")) + "-" + dwmc)
                            .remark(dwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    level5Dict.add(dict);
                });
            }
        });
        log.info("{}<------level4Jqxx", level4Jqxx);
        log.info("{}<------level4Dict", level4Dict);

        log.info("{}<------level5Jqxx", level5Jqxx);
        log.info("{}<------level5Dict", level5Dict);

        level4Jqxx.forEach(level4 ->
                cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                        .eq(WjscJqCjxx::getJjbh, level4.getJjbh())
                        .eq(WjscJqCjxx::getUuid, level4.getUuid())
                        .set(WjscJqCjxx::getAddressm, level4.getAddressm())
                        .set(WjscJqCjxx::getJqbz, level4.getJqbz()))
        );

        dictService.saveOrUpdateBatch(level4Dict);
        level5Jqxx.forEach(level5 ->
                cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                        .eq(WjscJqCjxx::getJjbh, level5.getJjbh())
                        .eq(WjscJqCjxx::getUuid, level5.getUuid())
                        .set(WjscJqCjxx::getAddressm, level5.getAddressm())
                        .set(WjscJqCjxx::getJqbz, level5.getJqbz()))
        );
        dictService.saveOrUpdateBatch(level5Dict);
    }

    @PostMapping("/exportLabelDict")
    @ApiOperation(value = "导出标签")
    public void exportLabelDict(@RequestBody LabelDictReq req, HttpServletResponse response){
        List<Integer> typeList = CollectionUtils.isEmpty(req.getTypeList()) ? Arrays.asList(3, 6, 4, 9, 8, 7) : req.getTypeList();
//        List<Dict> topRoot4Type = dictService.list(Wrappers.<Dict>lambdaQuery()
//                .select(Dict::getId, Dict::getType)
//                .in(Dict::getType, typeList)
//                .eq(Dict::getIsdelete, 1)
//                .eq(Dict::getFatherId, "-1"));
//        Map<Integer, String> type2TopId = topRoot4Type.stream().collect(Collectors.toMap(Dict::getType, Dict::getId, (vo, v) -> v));


        List<LabelDictDto> finalLabelList = new ArrayList<>();
        for (Integer type : typeList) {
            String id = "-1";
            //if(StringUtils.isBlank(id)) continue;
            List<Dict> dicts = buildTreeRecirsove(id, type);
            List<LabelDictDto> labelDictDtos = flattenTree(dicts);
            finalLabelList.addAll(labelDictDtos);
        }
        AtomicInteger num = new AtomicInteger(1);
        finalLabelList.forEach(x -> x.setNumber(num.getAndIncrement()));

        List<Integer> mergeCols = Arrays.asList(1, 2, 3, 4);
        EasyExcel.write("标签字典.xlsx", LabelDictDto.class)
                .sheet("标签字典")
                .doWrite(finalLabelList);

    }

    public List<Dict> buildTreeRecirsove(String rootFatherId, Integer type) {
        return buildChildren(rootFatherId, 1, type);
    }

    public List<LabelDictDto> flattenTree(List<Dict> dictList) {
        List<LabelDictDto> result = new ArrayList<>();
        for (Dict dict : dictList) {
            traverse1(dict, new ArrayList<>(), result);
        }
        return result;
    }

    private void traverse1(Dict dict, ArrayList<String> path, List<LabelDictDto> result) {
        path.add(dict.getDictName());
        if (dict.getChild() == null || dict.getChild().isEmpty()) {
            LabelDictDto labelDictDto = new LabelDictDto();
            for (int i = 0; i < path.size(); i++) {
                switch (i) {
                    case 0: labelDictDto.setLevel1(path.get(i)); break;
                    case 1: labelDictDto.setLevel2(path.get(i)); break;
                    case 2: labelDictDto.setLevel3(path.get(i)); break;
                    case 3: labelDictDto.setLevel4(path.get(i)); break;
                    case 4: labelDictDto.setLevel5(path.get(i)); break;
                }
            }
            result.add(labelDictDto);
        } else {
            for (Dict child : dict.getChild()) {
                traverse1(child, new ArrayList<>(path), result);
            }
        }
    }


    private List<Dict> buildChildren(String fatherId, int currentLevel, Integer parentType) {
        Integer limit = null;
        if ((parentType == 3 || parentType == 4) && currentLevel == 4) limit = 20;
        List<Dict> children = findChildren(fatherId, limit, parentType);
        for (Dict child : children) {
            List<Dict> childList = buildChildren(child.getId(), child.getStaticIndex(), child.getType());
            child.setChild(childList);
        }
        return children;
    }

    private List<Dict> findChildren(String fatherId, Integer limit, Integer type) {
        LambdaQueryWrapper<Dict> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Dict::getFatherId, fatherId)
                .eq(Dict::getIsdelete, 1)
                .eq(Dict::getType, type)
                .orderByAsc(Dict::getIndexNo);
        if (limit != null) {
            Page<Dict> objectPage = new Page<>(1, limit);
            return dictService.page(objectPage, wrapper).getRecords();
        } else {
            return dictService.list(wrapper);
        }
    }

    @PostMapping("/syncJgbhFromOracle")
    public R<?> syncJgbhFromOracle() {
        List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(Wrappers.<WjscJqCjxx>lambdaQuery()
                .select(WjscJqCjxx::getJjbh, WjscJqCjxx::getDwdz, WjscJqCjxx::getJgbh)
                .likeRight(WjscJqCjxx::getCjdw, "320404")
                .ge(WjscJqCjxx::getCjsjTime, "20250601000000"))
                ;
//                .ne(WjscJqCjxx::getDzid, "")
//                .isNotNull(WjscJqCjxx::getDzid)
//                .nested(w -> w.eq(WjscJqCjxx::getJgbh, "")
//                        .or()
//                        .isNull(WjscJqCjxx::getJgbh)));
        if (wjscJqCjxxes.isEmpty()) {
            return R.ok();
        }

        OracleHelper ora = null;
        try {
            ora = new OracleHelper("");

            List<List<WjscJqCjxx>> partition = Lists.partition(wjscJqCjxxes, 500);
            OracleHelper finalOra = ora;
            partition.forEach(part -> {
                Set<String> jjbhs = part.stream().map(x -> "'" + x.getJjbh() + "'").collect(Collectors.toSet());
                String jjbhConditionStr = String.join(",", jjbhs);
                String sql = "select JJBH, JGBH, TYPE, XQ, DZXX_XXZJBH from hl.dsj_jq where jjbh in (" + jjbhConditionStr + ")";
                log.warn(sql);
                List<JSONObject> query = new ArrayList<>();
                try {
                    query = finalOra.query(sql);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                for (JSONObject jsonObject : query) {
                    String jjbh = jsonObject.getString("JJBH");
                    String jgbh = jsonObject.getString("JGBH");
                    if (StringUtils.isBlank(jjbh) || StringUtils.isBlank("JGBH")) continue;
                    String type = jsonObject.getString("TYPE");
                    String xq = jsonObject.getString("XQ");
                    String dsdz = jsonObject.getString("DZXX_XXZJBQ");
                    cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                            .set(WjscJqCjxx::getJgbh, jgbh)
                            .set(WjscJqCjxx::getDzType, type)
                            .set(WjscJqCjxx::getXq, xq)
                            .set(WjscJqCjxx::getDsdz, dsdz)
                            .eq(WjscJqCjxx::getJjbh, jjbh));
                }
            });
            } catch (Exception ex) {
                log.error(Lib.getTrace(ex));
                return R.fail(Lib.getTrace(ex));

            } finally {
                ora.close();
            };


        return R.ok();
    }

    @PostMapping("/cleanAddressMNoJgbh")
    public R<?> cleanAddressMNoJgbh() {
        List<WjscJqCjxx> wjscJqCjxxes = cjxxMapper.selectList(Wrappers.<WjscJqCjxx>lambdaQuery()
                .select(WjscJqCjxx::getJjbh, WjscJqCjxx::getAddressm, WjscJqCjxx::getXq, WjscJqCjxx::getJqbz, WjscJqCjxx::getDwmc, WjscJqCjxx::getDwdz, WjscJqCjxx::getJgbh)
                .likeRight(WjscJqCjxx::getCjdw, "320404")
                .ne(WjscJqCjxx::getAddressm, "")
                .isNotNull(WjscJqCjxx::getAddressm))
                ;
        if (wjscJqCjxxes.isEmpty()) {
            return R.ok();
        }
        List<WjscJqCjxx> needClean = wjscJqCjxxes.stream().filter(x -> {
            String addressm = x.getAddressm();
            HashMap hashMap = RIUtil.StringToList(addressm);
            Set<String> addressSet = hashMap.keySet();
            return addressSet.stream().anyMatch(y -> y.startsWith("_"));
        }).collect(Collectors.toList());
        if (needClean.isEmpty()) {
            return R.ok();
        }

        needClean.forEach(item -> {
            String jgbh = item.getJgbh();
            String xq = item.getXq();
            if (StringUtils.isBlank(jgbh)) {
                OracleHelper ora = null;
                try {
                    ora = new OracleHelper("");
                    String sql = "select JJBH, JGBH, TYPE, XQ, DZXX_XXZJBH from hl.dsj_jq where jjbh = '" + item.getJjbh() + "'";
                    List<JSONObject> query = ora.query(sql);
                    if (!CollectionUtils.isEmpty(query)) {
                        JSONObject jsonObject = query.get(0);
                        jgbh = jsonObject.getString("JGBH");
                        xq = jsonObject.getString("XQ");
                        String type = jsonObject.getString("TYPE");
                        String dsdz = jsonObject.getString("DZXX_XXZJBQ");
                        cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                                .set(WjscJqCjxx::getJgbh, jgbh)
                                .set(WjscJqCjxx::getDzType, type)
                                .set(WjscJqCjxx::getXq, xq)
                                .set(WjscJqCjxx::getDsdz, dsdz)
                                .eq(WjscJqCjxx::getJjbh, item.getJjbh()));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    ora.close();
                };
            }
            String addressm = item.getAddressm();
            String jqbz = item.getJqbz();
            HashMap hashMap = RIUtil.StringToList(addressm);
            Set<String> addressSet = hashMap.keySet();
            Set<String> newAddressSet = new HashSet<>();
            String finalJgbh = jgbh;
            String dwmc = item.getDwmc();
            String dwdz = item.getDwdz();
            String finalXq = xq;
            String finalDwmc = dwmc;
            String finalDwdz = dwdz;
            String finalJqbz = jqbz;
            for (String x : addressSet) {
                if (x.startsWith("_")) {
                    String oidDictId = x;
                    String newDictId = finalJgbh + x;
                    String newDwmc = finalDwmc;
                    String newDwdz = finalDwdz;
                    if (finalXq != null && finalXq.length() > 2) {
                        String xqmc = Optional.ofNullable(addressDmMapper.selectOne(Wrappers.<AddressDm>lambdaQuery()
                                        .select(AddressDm::getDmmc)
                                        .eq(AddressDm::getDmdm, finalXq)))
                                .map(AddressDm::getDmmc)
                                .orElse(finalDwmc);
//                                String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
//                                String xqmc = Optional.ofNullable(jdbcTemplate.queryForObject(xqmcSql, String.class)).orElse(dwmc);
                        if (finalDwmc.contains("幢") || finalDwmc.contains("号")) {
                            newDictId = finalXq + x;
                            newDwmc = xqmc;
                            newDwdz = xqmc;
                        }
                    }
                    newAddressSet.add(newDictId);
                    finalJqbz = finalJqbz.replace(x, newDictId);

                    Dict dict = Dict.builder()
                            .id(newDictId)
                            .dictName(newDwmc)
                            .fatherId(x.replace("_", ""))
                            .staticIndex(5)
                            .type(3)
                            .permission("jqbq")
                            .memo(RIUtil.dicts.get(x.replace("_", "")).getString("memo") + "-" + newDwmc)
                            .remark(newDwdz)
                            .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .build();
                    dictService.saveOrUpdate(dict);
                    dictService.removeById(oidDictId);
                } else {
                    newAddressSet.add(x);
                }
            };
            String newAddress = JSON.toJSONString(newAddressSet);
            cjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                    .set(WjscJqCjxx::getAddressm, newAddress)
                    .set(WjscJqCjxx::getJqbz, finalJqbz)
                    .eq(WjscJqCjxx::getJjbh, item.getJjbh()));
        });
        return R.ok();
    }


}
