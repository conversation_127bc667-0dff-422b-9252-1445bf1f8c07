package com.hl.test.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;
import com.hl.security.config.sso.cache.SsoCache;

import com.hl.test.Utils.Lib;
import com.hl.test.Utils.OracleHelper;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.Dict;
import com.hl.test.domain.req.*;
import com.hl.test.domain.resp.RespDict;
import com.hl.test.mapper.DictMapper;
import com.hl.test.service.DictService;
import com.mysql.cj.x.protobuf.MysqlxDatatypes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "字典")
@RequestMapping("/dict")
@SuppressWarnings("unchecked")
public class DictController {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DictMapper dictMapper;

    @Autowired
    private DictService dictService;

    @PostMapping("/token")
    @ApiOperation(value = "获取token")

    public R<String> getToken(@Valid @RequestBody ReqJQListOne params) throws Exception {

        String sql = "select det from test where id=1";
        String det = jdbcTemplate.queryForObject(sql, String.class);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        String jjbh = data.getString("jjbh");


        OracleHelper ora = null;
        try {
            ora = new OracleHelper("");
            sql = "select jjbh from hl.dsj_jq where jjbh='" + jjbh + "'";
            String bh = ora.query_one(sql, "JJBH");
            if (bh != null && bh.length() > 2) {
                return R.ok(det);
            } else {
                return R.fail("全警平台暂未有该警情");
            }

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));

        } finally {
            ora.close();
        }

        //return R.ok();
    }


    @PostMapping("/list")
    @ApiOperation(value = "字典-获取字典列表")

    public R<List<RespDict>> list(@Valid @RequestBody ReqDictList params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getDict(data);
        //return R.ok();
    }

    @PostMapping("/tree")
    @ApiOperation(value = "警情字典树")


    public R<JSONArray> tree_cjlb(@Valid @RequestBody ReqDictTree params) throws Exception {
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getTree(data);
        //return R.ok();
    }
    public R<JSONArray> tree_jqbz(@Valid @RequestBody ReqDictTree params) throws Exception {
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getTree(data);
        //return R.ok();
    }

    private R<JSONArray> getTree(JSONObject data) {

        String type = data.getString("type");
        if (type.equals("510")) { //处警
            return getTreeCJLB();
        } else if (type.equals("10")) { //标签
            return getTreeBZ();
        } else if (type.equals("500")) {//接警
            return getTreejjLB();
        } else if (type.equals("21")) {
            return orgTreeNew(StringUtils.isBlank(data.getString("oid")) ? "320400000000" : data.getString("oid"));
        } else {
            if (data.containsKey("noLeave") && data.getBoolean("noLeave") != null && data.getBoolean("noLeave") == true)
                return getTreeBZTypeNoLeave(type);
            else if(type.equals("3")) {
                return R.ok(dictService.getDictTreeFromRedis(3));
            } else {
                return getTreeBZType(type);
            }
        }
    }

    private R<JSONArray> orgTree(String oid) {
        JSONArray t1 = new JSONArray();

        try {
            JSONArray tw1 = new JSONArray();
            JSONObject one = new JSONObject();

            if (StringUtils.isNotBlank(oid)) {
                one = RIUtil.dicts.get(oid);
            } else {
                one.put("id", "320400000000");
                one.put("father_id", "0");
                one.put("dict_name", "常州市公安局");
            }
            tw1.add(one);


            List<JSONObject> tw3 = RIUtil.GetDictByTypeList(23);
            Collections.sort(tw3, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            List<JSONObject> tw4 = RIUtil.GetDictByTypeList(24);
            Collections.sort(tw4, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            List<JSONObject> tw5 = RIUtil.GetDictByTypeList(25);
            Collections.sort(tw5, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });


            for (int i = 0; i < tw1.size(); i++) {
                JSONObject o1 = tw1.getJSONObject(i);

                String id1 = o1.getString("id");

                JSONArray t2 = new JSONArray();

                for (int a = 0; a < tw3.size(); a++) {
                    JSONObject o2 = tw3.get(a);
                    String father_id = o2.getString("father_id");
                    String id2 = o2.getString("id");
                    if (father_id.equals(id1)) {

                        JSONArray t3 = new JSONArray();

                        for (int b = 0; b < tw4.size(); b++) {
                            JSONObject o3 = tw4.get(b);
                            String faid = o3.getString("father_id");
                            if (faid.equals(id2)) {
                                t3.add(o3);
                            }
                        }

                       /* o2.put("dets", t3);
                        t2.add(o2);*/
                        for (int b = 0; b < tw5.size(); b++) {
                            JSONObject o3 = tw5.get(b);
                            String faid = o3.getString("father_id");
                            String id3 = o3.getString("id");

                            if (faid.equals(id2)) {
                                JSONArray t4 = new JSONArray();

                                o3.put("dets", t4);
                                //System.out.println(o3);
                                t3.add(o3);
                            }
                        }
                        o2.put("dets", t3);

                        t2.add(o2);
                    }
                }
                o1.put("dets", t2);
                t1.add(o1);

            }


            log.warn("org_tree-->" + t1.size());
            return R.ok(t1, 0);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));

        }


    }

    private void sortById(List<JSONObject> list) {
        list.sort(Comparator.comparing(o -> o.getInteger("type"), Comparator.nullsLast(Integer::compareTo)));
    }

    private JSONArray build2425(String parentId, List<JSONObject> tw24, List<JSONObject> tw25) {
        JSONArray children = new JSONArray();
        for (JSONObject o24 : tw24) {
            if (parentId.equals(o24.getString("father_id"))) {
                o24.put("dets", new JSONArray());
                children.add(o24);
            }
        }
        for (JSONObject o25 : tw25) {
            if (parentId.equals(o25.getString("father_id"))) {
                o25.put("dets", new JSONArray());
                children.add(o25);
            }
        }
        return children;
    }


    private R<JSONArray> orgTreeNew(String oid) {
        try {
            JSONArray result = new JSONArray();
            JSONObject root = RIUtil.dicts.get(oid);

            if (root == null) return R.fail("未找到对应的根节点" + oid);
            Integer type = root.getInteger("type");

            List<JSONObject> tw1 = RIUtil.GetDictByTypeList(21);
            List<JSONObject> tw3 = RIUtil.GetDictByTypeList(23);
            List<JSONObject> tw4 = RIUtil.GetDictByTypeList(24);
            List<JSONObject> tw5 = RIUtil.GetDictByTypeList(25);

            sortById(tw1);
            sortById(tw3);
            sortById(tw4);
            sortById(tw5);

            if (type == 21) {
                JSONArray level23 = new JSONArray();
                for (JSONObject o23 : tw3) {
                    if (oid.equals(o23.getString("father_id"))) {
                        JSONArray level2425 = build2425(o23.getString("id"), tw4, tw5);
                        o23.put("dets", level2425);
                        level23.add(o23);
                    }
                }
                root.put("dets", level23);
            } else if (type == 23) {
                JSONArray level2425 = build2425(root.getString("id"), tw4, tw5);
                root.put("dets", level2425);
            } else if (type == 24 || type == 25) {
                root.put("dets", new JSONArray());
            }
            result.add(root);

            return R.ok(result, 0);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }


    private R<JSONArray> getTreeBZ() {


        JSONArray tw1 = new JSONArray();

        String[] types = {"3", "4", "6", "7", "8", "9"};
        for (int a = 0; a < types.length; a++) {
            String ty = types[a];
            JSONArray ones = RIUtil.GetDictByTypeFather(Integer.parseInt(ty), "-1");
            String sql = "select static_index from dict where type='" + ty + "' and isdelete=1 order by static_index "
                    + "  desc limit 1";
            int last = jdbcTemplate.queryForObject(sql, Integer.class);
            JSONObject one = ones.getJSONObject(0);

            List<JSONObject> nodes = (List<JSONObject>) getTreeBZTypeInf10(ty, last);
            one.put("dets", nodes);
            tw1.add(one);
        }

        return R.ok(tw1);


    }

    private JSONArray GetSort(JSONArray tw1, String col, int mark) {

        List<JSONObject> list = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.getJSONObject(i);
            list.add(one);
        }

        Collections.sort(list, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            int a = 0;
            int b = 0;

            try {
                a = o1.getInteger(col);
                b = o2.getInteger(col);
            } catch (Exception ex) {

            }

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {
                //降序排列，升序改成a>b
                if (mark == 1) {
                    return 1;
                } else {
                    return -1;
                }
            } else if (a == b) {
                return 0;
            } else {
                if (mark == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });

        tw1 = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            tw1.add(one);
        }

        return tw1;
    }

    private R<JSONArray> getTreeBZInf() {

        JSONArray tw1 = RIUtil.getDictByPermission("jqbq", "-1");
        //back=GetDets(jqbzs,1);

        JSONArray t1 = new JSONArray();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject o1 = tw1.getJSONObject(i);

            String id1 = o1.getString("id");

            JSONArray tw2 = RIUtil.GetDictByFatherbq(id1);
            JSONArray t2 = new JSONArray();

            for (int a = 0; a < tw2.size(); a++) {
                JSONObject o2 = tw2.getJSONObject(a);
                String id2 = o2.getString("id");

                JSONArray tw3 = RIUtil.GetDictByFatherbq(id2);

                JSONArray t3 = new JSONArray();


                for (int b = 0; b < tw3.size(); b++) {
                    JSONObject o3 = tw3.getJSONObject(b);
                    String id3 = o3.getString("id");
                    JSONArray tw4 = RIUtil.GetDictByFatherbq(id3);

                    JSONArray t4 = new JSONArray();
                    for (int c = 0; c < tw4.size(); c++) {
                        JSONObject o4 = tw4.getJSONObject(c);
                        JSONArray tw5 = RIUtil.GetDictByFatherbq(o4.getString("id"));

                        o4.put("dets", new JSONArray());
                        if (tw5.size() > 0) {
                            t4.add(o4);
                        }
                    }

                    o3.put("dets", t4);
                    if (tw4.size() > 0) {
                        t3.add(o3);
                    }
                }

                o2.put("dets", t3);
                if (tw3.size() > 0) {
                    t2.add(o2);
                }
            }

            o1.put("dets", t2);
            t1.add(o1);

        }


        return R.ok(tw1, 0);

    }

    @Getter
    @AllArgsConstructor
    enum BzInfoEnum {
        ADDRESS("3", "地址标签", 4),
        PEOPLE("4", "人员标签", 4);
        private final String type;
        private final String typeName;
        private final Integer needlevel;
        static Integer getLevelByType(String type) {
            Map<String, Integer> type2RootId = Arrays.stream(BzInfoEnum.values()).collect(Collectors.toMap(BzInfoEnum::getType, BzInfoEnum::getNeedlevel));
            return type2RootId.getOrDefault(type, 4);
        }
    }


    private R<JSONArray> getTreeBZTypeNoLeave(String type) {
        Dict topRoot = dictMapper.selectOne(Wrappers.<Dict>lambdaQuery()
                .eq(Dict::getType, type)
                .eq(Dict::getFatherId, "-1")
                .eq(Dict::getIsdelete, "1"));
        if (topRoot == null) return R.ok(new JSONArray());
        Integer level = BzInfoEnum.getLevelByType(type);

        List<Dict> dicts = dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
                .eq(Dict::getType, type)
                .eq(Dict::getIsdelete, "1")
                .le(Dict::getStaticIndex, level)
                .orderByAsc(Dict::getIndexNo));
        buildTreeInMemory(topRoot, dicts, level);
//        log.info("{}<=====topRoot", topRoot);
        return R.ok(JSONArray.parseArray(JSON.toJSONString(topRoot.getChild())));

    }

    private void buildTreeInMemory(Dict parent, List<Dict> dicts, Integer level) {
        if(level < 1) return;
        List<Dict> children = dicts.stream().filter(d -> parent.getId().equals(d.getFatherId())).collect(Collectors.toList());
        parent.setChild(children);
        for (Dict child : children) {
            buildTreeInMemory(child, dicts, level - 1);
        }
    }

    private R<JSONArray> getTreeBZType(String type) {

        String[] types = {"3", "4", "6", "7", "8", "9"};
        String[] ids = {"5F646FAFB4B24D5EAAECFF79AA9E8D1C", "DB21DC0D66864DACBA5CC6A9A655E443",
                "6002C301013241C8AB69D9BFA2E0C9A8", "6E7448F25AD645E999DBBD7022964614",
                "08CC6B6A0EE644D381F149D06A9CB4A6", "8F0164E90B3149B5B33066C173CC1CB9"};
        HashMap<String, String> tpss = new HashMap<>();
        for (int a = 0; a < types.length; a++) {
            String ty = types[a];
            String fid = ids[a];
            tpss.put(ty, fid);
        }
        int t = Integer.parseInt(type);

        JSONArray tw1 = new JSONArray();
        if (type.equals("11")) {

            for (int a = 0; a < types.length; a++) {
                String ty = types[a];

                JSONObject one = RIUtil.dicts.get(ids[a]);
                try {
                    if (ty.equals("9")) {
                      //  System.out.println(9);
                    }
                    List<JSONObject> nodes = (List<JSONObject>) getTreeBZTypeInf(ty, ids[a]);
                    one.put("dets", nodes);
                } catch (Exception ex) {
                    System.out.println(ty + "-->" + ids[a]);
                    System.out.println(Lib.getTrace(ex));
                }
                tw1.add(one);
            }

            return R.ok(tw1);
        } else if (type.equals("4" )) {
            return R.ok(RIUtil.rybqs);
        } else if (type.equals("3")) {
            return R.ok(RIUtil.dzbqs);
        } else {
            String fid = "-1";
            if (Integer.parseInt(type) <= 10) {
                fid = tpss.get(type);
            }
            List<JSONObject> nodes = (List<JSONObject>) getTreeBZTypeInf(type, fid);
            for (JSONObject one : nodes) {
                tw1.add(one);
            }
            return R.ok(tw1);

        }

    }



    private List<JSONObject> getTreeBZTypeInf(String type, String fid) {
        int t = Integer.parseInt(type);

        JSONArray tw1 = new JSONArray();
        if (Integer.parseInt(type) < 11) {

            if (Integer.parseInt(type) == 3) {
                tw1 = RIUtil.GetDictByType_bq(t, RIUtil.dzbq);
            } else if (Integer.parseInt(type) == 4) {
                tw1 = RIUtil.GetDictByType_bq(t, RIUtil.rybq);
            } else {
                tw1 = RIUtil.GetDictByType_bq(t, RIUtil.jqbqs);
            }
        } else if (t > 11 && t < 20) {
            t = t - 10;
            if (Integer.parseInt(type) == 3) {
                tw1 = RIUtil.GetDictByType_bq(t, RIUtil.dzbq);
            } else if (Integer.parseInt(type) == 4) {
                tw1 = RIUtil.GetDictByType_bq(t, RIUtil.rybq);
            } else {
                tw1 = RIUtil.GetDictByType_bq(t, RIUtil.jqbqs);
            }
        } else {

            tw1 = RIUtil.GetDictByType(Integer.parseInt(type));
        }

        List<JSONObject> nodes = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.getJSONObject(i);
            nodes.add(one);
        }

        if (type.equals("9")) {
            //System.out.println(0);
        }
        List<JSONObject> treeNodes = buildTree(nodes, fid);
        if (treeNodes == null) {
            System.out.println(nodes + fid);
        }


        return treeNodes;

    }

    private List<JSONObject> getTreeBZTypeInf10(String type, int last) {
        int t = Integer.parseInt(type);
        String fid = "-1";
        JSONArray tw1 = new JSONArray();

        JSONArray ones = RIUtil.GetDictByTypeFather(Integer.parseInt(type), "-1");
        JSONObject one = ones.getJSONObject(0);
        fid = one.getString("id");
        tw1 = RIUtil.GetDictByType(Integer.parseInt(type));


        List<JSONObject> nodes = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject o = tw1.getJSONObject(i);
            int index = o.getIntValue("static_index");
            if (index != last) {
                nodes.add(o);
            }
        }

        List<JSONObject> treeNodes = buildTree(nodes, fid);


        return treeNodes;

    }

    public static List<JSONObject> buildTree(List<JSONObject> nodeList, String fid) {
        List<JSONObject> tree = new ArrayList<>();
        for (JSONObject node : nodeList) {
            try {
                String id = node.getString("id");
                String f_id = node.getString("father_id");
                if (f_id.equals(fid)) {
                    //使用递归方法构建子节点树
                    List<JSONObject> children = buildTree(nodeList, id);
                    children = RIUtil.GetSort(children, "index_no", 1);


                    //node.setChildren(children);
                    node.put("dets", children);
                    // node.put("dict_name",node.getString("dict_name")+"("+children.size()+")");
                    tree.add(node);
                }
            } catch (Exception ex) {
                log.warn(Lib.getTrace(ex));
                System.out.println(node);

            }
        }
        return tree;
    }


    private R<JSONArray> getTreeSFCS() {

        JSONArray tw1 = RIUtil.GetDictByTypeFather(74, "-1");
        //back=GetDets(jqbzs,1);

        JSONArray t1 = new JSONArray();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject o1 = tw1.getJSONObject(i);

            String id1 = o1.getString("id");

            JSONArray tw2 = RIUtil.GetDictByFather(id1);
            JSONArray t2 = new JSONArray();

            for (int a = 0; a < tw2.size(); a++) {
                JSONObject o2 = tw2.getJSONObject(a);

                t2.add(o2);
            }

            o1.put("dets", t2);
            t1.add(o1);

        }


        return R.ok(tw1, 0);

    }


    private R<JSONArray> getTreeSTBQ() {

        JSONArray tw1 = RIUtil.GetDictByTypeFather(75, "-1");
        //back=GetDets(jqbzs,1);
        List<JSONObject> w1 = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject fif = tw1.getJSONObject(i);
            w1.add(fif);
        }
        JSONArray t1 = new JSONArray();

        Collections.sort(w1, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            int a = 0;
            int b = 0;

            try {
                a = o1.getInteger("index_no");
                b = o2.getInteger("index_no");
            } catch (Exception ex) {

            }

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        for (int i = 0; i < w1.size(); i++) {
            JSONObject o1 = w1.get(i);
            String id1 = o1.getString("id");

            JSONArray tw2 = RIUtil.GetDictByFather(id1);
            JSONArray t2 = new JSONArray();

            for (int a = 0; a < tw2.size(); a++) {
                JSONObject o2 = tw2.getJSONObject(a);

                t2.add(o2);
            }

            o1.put("dets", t2);


            t1.add(o1);

        }


        return R.ok(t1, 0);

    }

    private R<JSONArray> getTreeCJLB() {

        JSONObject back = new JSONObject();

        try {
            JSONArray type50 = RIUtil.GetDictByType(511);
            //System.out.println(type50);
            List<JSONObject> fifty = new ArrayList<>();
            for (int i = 0; i < type50.size(); i++) {
                JSONObject fif = type50.getJSONObject(i);
                fifty.add(fif);
            }

            Collections.sort(fifty, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type521 = RIUtil.GetDictByType(512);
            List<JSONObject> fifty2 = new ArrayList<>();
            for (int i = 0; i < type521.size(); i++) {
                JSONObject fif = type521.getJSONObject(i);
                fifty2.add(fif);
            }

            Collections.sort(fifty2, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type54 = RIUtil.GetDictByType(513);
            List<JSONObject> fifty4 = new ArrayList<>();
            for (int i = 0; i < type54.size(); i++) {
                JSONObject fif = type54.getJSONObject(i);
                fifty4.add(fif);
            }


            JSONArray type56 = RIUtil.GetDictByType(514);

            JSONArray rest = new JSONArray();
            JSONArray t50 = new JSONArray();
            for (int i = 0; i < fifty.size(); i++) {
                JSONObject one50 = fifty.get(i);

                String id = one50.getString("id");

                JSONArray t52 = new JSONArray();
                for (int a = 0; a < fifty2.size(); a++) {
                    JSONObject one52 = fifty2.get(a);
                    if (one52.getString("father_id").equals(id)) {
                        String id52 = one52.getString("id");

                        JSONArray t54 = new JSONArray();

                        for (int b = 0; b < type54.size(); b++) {
                            JSONObject one54 = type54.getJSONObject(b);
                            if (one54.getString("father_id").equals(id52)) {
                                String id54 = one54.getString("id");

                                JSONArray t56 = new JSONArray();

                                for (int c = 0; c < type56.size(); c++) {
                                    JSONObject one56 = type56.getJSONObject(c);
                                    if (one56.getString("father_id").equals(id54)) {

                                        t56.add(one56);
                                    }
                                }

                                one54.put("dets", t56);
                                t54.add(one54);
                            }
                        }

                        one52.put("dets", t54);
                        t52.add(one52);
                    }

                }

                one50.put("dets", t52);
                t50.add(one50);


            }
            rest.add(t50);
            back.put("data", rest);
            return R.ok(t50, 0);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }

    }


    private R<JSONArray> getTreejjLB() {

        JSONObject back = new JSONObject();

        try {
            JSONArray type50 = RIUtil.GetDictByType(501);
            List<JSONObject> fifty = new ArrayList<>();
            for (int i = 0; i < type50.size(); i++) {
                JSONObject fif = type50.getJSONObject(i);
                fifty.add(fif);
            }

            Collections.sort(fifty, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type521 = RIUtil.GetDictByType(502);
            List<JSONObject> fifty2 = new ArrayList<>();
            for (int i = 0; i < type521.size(); i++) {
                JSONObject fif = type521.getJSONObject(i);
                fifty2.add(fif);
            }

            Collections.sort(fifty2, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type54 = RIUtil.GetDictByType(503);
            List<JSONObject> fifty4 = new ArrayList<>();
            for (int i = 0; i < type54.size(); i++) {
                JSONObject fif = type54.getJSONObject(i);
                fifty4.add(fif);
            }

            Collections.sort(fifty4, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });


            JSONArray rest = new JSONArray();
            JSONArray t50 = new JSONArray();
            for (int i = 0; i < fifty.size(); i++) {
                JSONObject one50 = fifty.get(i);

                String id = one50.getString("id");

                JSONArray t52 = new JSONArray();
                for (int a = 0; a < fifty2.size(); a++) {
                    JSONObject one52 = fifty2.get(a);
                    if (one52.getString("father_id").equals(id)) {
                        String id52 = one52.getString("id");

                        JSONArray t54 = new JSONArray();

                        for (int b = 0; b < type54.size(); b++) {
                            JSONObject one54 = type54.getJSONObject(b);
                            if (one54.getString("father_id").equals(id52)) {

                                t54.add(one54);
                            }
                        }

                        one52.put("dets", t54);
                        t52.add(one52);
                    }

                }

                one50.put("dets", t52);
                t50.add(one50);


            }
            rest.add(t50);
            back.put("data", rest);
            return R.ok(t50, 0);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }

    }

    @PostMapping("/create")
    @ApiOperation(value = "字典-创建字典")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<Object> create(@Valid @RequestBody ReqDictCreate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
       // System.out.println(user);

       // System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("create_user", user.getIdCard());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        return createDict(data);
        //return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "字典-更新字典")

    public R<Object> update(@Valid @RequestBody ReqDictUpdate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        return updateDict(data);
    }

    private R<Object> updateDict(JSONObject data) {
        try {

            String sql = "";
            String id = "";
            String father_id = "";
            int type = -1;
            String dict_name = "";
            String color = "";
            int static_index = -1;
            String opt_user = "";
            String fid = "";

            String remark = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                String s = "select father_id from dict where id='" + id + "'";
                fid = jdbcTemplate.queryForObject(s, String.class);

                s = "select dict_name from dict where id='" + id + "'";
                dict_name = jdbcTemplate.queryForObject(s, String.class);

            } else {
                return R.fail("缺少id");
            }
            if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
                father_id = data.getString("father_id");
                String s = "select type from dict where id='" + father_id + "'";
                type = jdbcTemplate.queryForObject(s, Integer.class);
                s = "select static_index from dict where id='" + father_id + "'";
                static_index = jdbcTemplate.queryForObject(s, Integer.class) + 1;
                sql = sql + " father_id='" + father_id + "' ,type='" + type + "',static_index='" + static_index + "', ";
                fid = father_id;
            }

            if (data.containsKey("dict_name") && data.getString("dict_name").length() > 0) {
                dict_name = data.getString("dict_name");
                sql = sql + " dict_name='" + dict_name + "' , ";

            }
            if (data.containsKey("color") && data.getString("color").length() > 0) {
                color = data.getString("color");
                sql = sql + " color='" + color + "' , ";

            }

            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "', ";

            }

            if (data.containsKey("gadm") && data.getString("gadm").length() > 0) {
                String gadm = data.getString("gadm");
                sql = sql + " gadm='" + gadm + "', ";

            }

            String s = "";
            String memo = dict_name;
            while (static_index > 2) {

                s = "select dict_name from dict where id='" + fid + "'";
                String n = jdbcTemplate.queryForObject(s, String.class);
                memo = n + "-" + memo;
                s = "select father_id from dict where id='" + fid + "'";
                fid = jdbcTemplate.queryForObject(s, String.class);
                static_index--;

            }


            String sqls = "update dict set " + sql + " isdelete=1,up_time='" + new SimpleDateFormat("yyyy-MM-dd " +
                    "HH:mm:ss").format(new Date()) + "',memo='"+memo+"'   where id='" + id + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    @PostMapping("/delete")
    @ApiOperation(value = "字典-删除字典")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<Object> delete(@Valid @RequestBody ReqDictDel params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
       // System.out.println(user);

       //System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());

        String id = data.getString("id");
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String sql =
                "update dict set isdelete=2 ,delete_user='" + user.getIdCard() + "',delete_time='" + time + "'," +
                        "up_time='" + time + "' " + "where " + "id='" + id + "'";
        jdbcTemplate.update(sql);

        //return updateDict(data);
        RIUtil.jqbqs.remove(id);
        RIUtil.rybqs.remove(id);
        RIUtil.dzbq.remove(id);


        return R.ok();
    }


    @PostMapping("/delete_new")
    @ApiOperation(value = "字典-删除字典")
    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<Object> deleteNew(@Valid @RequestBody ReqDictDel params) throws Exception {
        User user = UserUtils.getUserNull();
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String id = params.getId();
        if(StringUtils.isBlank(id)) return R.fail("传入标签id为空");
        Dict dict = dictMapper.selectOne(Wrappers.<Dict>lambdaQuery()
                .select(Dict::getId)
                .eq(Dict::getId, id)
                .eq(Dict::getIsdelete, 1));
        if(dict == null) return R.fail("未找到该标签或该标签已删除");
        List<String> idsNeedDelete = new ArrayList<>(Arrays.asList(id));
        List<String> ids = new ArrayList<>(Arrays.asList(id));
        while (!ids.isEmpty()) {
            List<Dict> dicts = dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
                    .select(Dict::getId, Dict::getFatherId)
                    .in(Dict::getFatherId, ids)
                    .eq(Dict::getIsdelete, 1));
            if (dicts.isEmpty()) break;
            List<String> partIdsNeedDelete = dicts.stream().map(Dict::getId).collect(Collectors.toList());
            idsNeedDelete.addAll(partIdsNeedDelete);
            ids = partIdsNeedDelete;
        }
        dictMapper.update(Wrappers.<Dict>lambdaUpdate()
                .in(Dict::getId, idsNeedDelete)
                .eq(Dict::getIsdelete, 1)
                .set(Dict::getIsdelete, 2)
                .set(Dict::getDeleteUser, user.getIdCard())
                .set(Dict::getDeleteTime, time)
                .set(Dict::getUpTime, time));
        RIUtil.jqbqs.remove(id);
        RIUtil.rybqs.remove(id);
        RIUtil.dzbq.remove(id);
        return R.ok();
    }

    private R<Object> createDict(JSONObject data) {

        try {

            String father_id = "";
            Integer type = -1;
            String dict_name = "";
            String color = "";
            String index_no = "0";
            String permission = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            int static_index = -1;
            int sIndex = -1;
            String remark = "";
            String fid = "";
            String gadm = "";
            if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
                father_id = data.getString("father_id");

                String s = "select type from dict where id='" + father_id + "'";
                type = jdbcTemplate.queryForObject(s, Integer.class);
                if (type < 20) {
                    permission = "jqbq";
                }
                s = "select static_index from dict where id='" + father_id + "'";
                static_index = jdbcTemplate.queryForObject(s, Integer.class) + 1;
                sIndex = static_index;
                fid = father_id;
            } else {
                return R.fail("缺少父id");
            }

            if (data.containsKey("dict_name") && data.getString("dict_name").length() > 0) {
                dict_name = data.getString("dict_name");
            } else {
                return R.fail("缺少字典名称：dict_name");
            }
            if (data.containsKey("color") && data.getString("color").length() > 0) {
                color = data.getString("color");

            }

            if (data.containsKey("gadm") && data.getString("gadm").length() > 0) {
                gadm = data.getString("gadm");

            }

            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
            }

            String unit = data.getString("unit");
            String sql = "";
            String memo = dict_name;
            while (static_index > 2) {

                sql = "select dict_name from dict where id='" + fid + "'";
                String n = jdbcTemplate.queryForObject(sql, String.class);
                memo = n + "-" + memo;
                sql = "select father_id from dict where id='" + fid + "'";
                fid = jdbcTemplate.queryForObject(sql, String.class);
                static_index--;

            }


            String id = String.valueOf(UUID.randomUUID());
            String sqls = "insert dict (id,father_id,type," + "dict_name,color,static_index,index_no," + "permission,"
                    + "create_user,create_time,isdelete,remark,memo,unit,up_time,gadm)" + "values('" + id + "','" + father_id + "'," + "'" + type + "'," + "'" + dict_name + "','" + color + "','" + sIndex + "','" + index_no + "'," + "'" + permission + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + remark + "','" + memo + "','" + unit + "','" + create_time + "','" + gadm + "')";
            System.out.println(sqls);

            jdbcTemplate.update(sqls);

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }

        return R.ok();
    }

    private R<List<RespDict>> getDict(JSONObject data) {


        try {

            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String father_id = "";
            int type = -1;


            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
                father_id = data.getString("father_id");
                sql = sql + " father_id='" + father_id + "' and ";
            }
            if (data.containsKey("type") && data.getInteger("type") > 0) {
                type = data.getInteger("type");
                sql = sql + " type in (" + type + ") and ";
            } else {
                sql = sql + " type <20 and ";
            }
            if (data.containsKey("dict_name") && data.getString("dict_name").length() > 0) {
                String dict_name = data.getString("dict_name");
                sql = sql + "(dict_name like '%" + dict_name + "%' or memo like '%" + dict_name + "%') and ";
            }
            if (data.containsKey("create_time_start")&&data.getString("create_time_start").length()>0) {
                String create_time_start = data.getString("create_time_start");
                sql = sql + " create_time>='" + create_time_start + "' and ";
            }

            if (data.containsKey("create_time_end")&&data.getString("create_time_end").length()>0) {
                String create_time_end = data.getString("create_time_end");
                sql = sql + " create_time<='" + create_time_end + "' and ";
            }

            if (data.containsKey("create_user")) {
                String create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                String unit = data.getString("unit");
                List<String> us = RIUtil.StringToArrayList(unit);
                String s = "";
                for (int i = 0; i < us.size(); i++) {
                    s = s + " unit ='" + us.get(i) + "' or";
                }
                if (s.endsWith("or")) {
                    s = s.substring(0, s.length() - 3);
                }
                sql = sql + " (" + s + ") and ";
            }

            if (data.containsKey("gadm")) {
                String gadm = data.getString("gadm");
                sql = sql + " gadm='" + gadm + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from dict where 1=1 and " + sql + " isdelete=1  order by index_no ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespDict> d = RelaInfo(ret);
                sqls = "select count(id) as count from dict where 1=1 and " + sql + " isdelete=1 ";
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private List<RespDict> RelaInfo(JSONArray ret) {

        List<RespDict> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String create_user = one.getString("create_user");
            JSONObject userJson = SsoCache.me.cacheData.getJSONObject("user").getJSONObject(create_user);
            JSONObject org = SsoCache.me.cacheData.getJSONObject("organization").getJSONObject(one.getString("unit"));
            one.put("create_user", userJson);
            one.put("unit", org);

            String fid = one.getString("father_id");
            String fn = "";
            try {
                fn = RIUtil.jqbqs.get(fid).getString("dict_name");
            } catch (Exception ex) {
            }
            one.put("_father_id", fn);

            if (one.containsKey("gadm") && one.getString("gadm") != null && one.getString("gadm").length() > 0) {
                JSONObject gadms = SsoCache.me.cacheData.getJSONObject("organization").getJSONObject(one.getString(
                        "gadm"));
                try {
                    one.put("_gadm", gadms.getString("organization_name"));
                } catch (Exception ex) {
                    log.error(one.getString("gadm"));
                }
            }


            RespDict dd = gson.fromJson(String.valueOf(one), RespDict.class);
            back.add(dd);
        }
        return back;


    }

    @PostMapping("/sort")
    @ApiOperation(value = "字典排序")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<Object> sortDict(@Valid @RequestBody ReqDictSort params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());

        String ids = data.getString("ids");
        String m = ids.replace("[", "").replace("]", "").replace("\"", "");
        //System.out.println("cut->" + m);
        String[] mm = m.split(",");
        if (mm.length > 0) {
            for (int i = 0; i < mm.length; i++) {
                String id = mm[i];
                int index = i + 1;
                String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                String sql = "update dict set index_no='" + index + "',up_time='" + time + "' where id='" + id + "'";
                System.out.println(sql);
                jdbcTemplate.update(sql);
            }
        }


        //return updateDict(data);
        return R.ok();
    }


}
