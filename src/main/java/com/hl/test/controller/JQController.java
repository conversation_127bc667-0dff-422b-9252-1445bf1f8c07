package com.hl.test.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;


import com.hl.test.Utils.BzgsUtil;
import com.hl.test.Utils.Lib;
import com.hl.test.Utils.RIUtil;

import com.hl.test.domain.Dict;
import com.hl.test.domain.WjscJqSjxx;
import com.hl.test.domain.req.ReqJQListOne;
import com.hl.test.domain.req.ReqJQQuery;
import com.hl.test.domain.resp.*;
import com.hl.test.mapper.WjscJqSjxxMapper;
import com.hl.test.service.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "警情查询")
@RequestMapping("/jq")
@SuppressWarnings("unchecked")
public class JQController {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DictService dictService;

    @Autowired
    private WjscJqSjxxMapper wjscJqSjxxMapper;
        // 警情列表
    @PostMapping("/list")
    @ApiOperation(value = "警情查询-列表查询")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<List<RespJQList>> list(@Valid @RequestBody ReqJQQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();

        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        return searchJQList(data);
        //return R.ok();
    }


    @PostMapping("/det")
    @ApiOperation(value = "警情查询-根据jjbh获取明细")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<RespJqDet> det(@Valid @RequestBody ReqJQListOne params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        // System.out.println(user);

        // System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getDet(data);
        //return R.ok();
    }


    private R<RespJqDet> getDet(JSONObject data) {
        RespCJ cjone = new RespCJ();
        RespJJ jjone = new RespJJ();
        List<RespSJRYList> sjrys = new ArrayList<>();
        try {
            String jjbh = data.getString("jjbh");


            String sqls = "select * from wjsc_jq_cjxx where jjbh='" + jjbh + "'";
            // log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 10);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                JSONObject one = ret.getJSONObject(0);
                one = RealJQDet(one);
                cjone = new Gson().fromJson(String.valueOf(one), RespCJ.class);

            }

            sqls = "select * from wjsc_jq_jjxx where jjbh='" + jjbh + "'";
            //log.warn(sqls);

            list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 10);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                JSONObject one = ret.getJSONObject(0);
                one = RealJQDet(one);
                jjone = new Gson().fromJson(String.valueOf(one), RespJJ.class);

            }

            sqls = "select a.*,b.personMs as his from wjsc_jq_sjxx a left join jq_person b on a.gmsfhm=b.gmsfhm " +
                    "where " + "a.jjbh='" + jjbh + "'";
            log.warn(sqls);

            list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 999);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);

                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.getJSONObject(i);
                    one = RealJQDet(one);
                    RespSJRYList sjone = new Gson().fromJson(String.valueOf(one), RespSJRYList.class);
                    sjrys.add(sjone);
                }

            }
            sqls = "select case_no,case_name,case_summary,case_accept_dep_name,case_accept_time,case_type" + "," +
                    "case_sub_type,case_state,value_involved from cz_jz_case_info where case_no='" + jjbh + "'";
            log.warn(sqls);

            RespAJ ajone = new RespAJ();
            list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 10);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                JSONObject one = ret.getJSONObject(0);
                one = RealJQDet(one);
                ajone = new Gson().fromJson(String.valueOf(one), RespAJ.class);

            }

            RespJqDet det = new RespJqDet();
            det.setCjxx(cjone);
            det.setJjxx(jjone);
            det.setSjrys(sjrys);
            det.setAjxx(ajone);
            return R.ok(det);

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }


    }

    private JSONObject RealJQDet(JSONObject one) {
        if (one.containsKey("bjdhsj_time") && one.getString("bjdhsj_time").length() > 0) {
            try {
                if (!one.getString("bjdhsj_time").contains(":")) {
                    one.put("bjdhsj_time", RIUtil.get_Time(one.getString("bjdhsj_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("bjlx") && one.getString("bjlx").length() > 0) {
            try {
                one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("bjxs") && one.getString("bjxs").length() > 0) {
            try {
                one.put("bjxs", RIUtil.dicts.get("45-" + one.getString("bjxs")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("cjbs") && one.getString("cjbs").length() > 0) {
            try {
                one.put("cjbs", RIUtil.dicts.get("??-" + one.getString("cjbs")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("create_time") && one.getString("create_time") != null && one.getString("create_time").length() > 0) {
            try {
                if (!one.getString("create_time").contains(":")) {
                    one.put("create_time", RIUtil.get_Time(one.getString("create_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("djsj_time") && one.getString("djsj_time").length() > 0) {
            try {
                if (!one.getString("djsj_time").contains(":")) {
                    one.put("djsj_time", RIUtil.get_Time(one.getString("djsj_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("jjrqsj") && one.getString("jjrqsj") != null && one.getString("jjrqsj").length() > 0) {
            try {
                one.put("jjrqsj", RIUtil.get_Time(one.getString("jjrqsj")));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("jqdj") && one.getString("jqdj").length() > 0) {
            try {
                one.put("jqdj", RIUtil.dicts.get(one.getString("jqdj")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("xgsj_time") && one.getString("xgsj_time") != null && one.getString("xgsj_time").length() > 0) {
            try {
                if (!one.getString("xgsj_time").contains(":")) {
                    one.put("xgsj_time", RIUtil.get_Time(one.getString("xgsj_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("cjjg") && one.getString("cjjg").length() > 0) {
            try {
                one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("cjjqbq") && one.getString("cjjqbq").length() > 0) {
            try {
                one.put("cjjqbq", RIUtil.dicts.get("75-" + one.getString("cjjqbq")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }

        if (one.containsKey("fsyy") && one.getString("fsyy") != null && one.getString("fsyy").length() > 0) {
            try {
                String fsyy = one.getString("fsyy");
                if (fsyy.startsWith("0")) {
                    fsyy = fsyy.substring(1);
                }

                one.put("fsyy", RIUtil.dicts.get("77-" + fsyy).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("cjlb") && one.getString("cjlb").length() > 0) {
            try {
                String lb = "51-" + one.getString("cjlb");
                if (RIUtil.dicts.containsKey(lb)) {
                    one.put("cjlb", RIUtil.dicts.get(lb).getString("dict_name"));
                } else {
                    lb = "50-" + one.getString("cjlb");
                    if (RIUtil.dicts.containsKey(lb)) {
                        one.put("cjlb", RIUtil.dicts.get(lb).getString("dict_name"));
                    }
                }
                String cjlbs = one.getString("cjlb");

                String id = "";
                String fid = RIUtil.dicts.get(lb).getString("father_id");
                try {
                    while (!fid.equals("-1")) {
                        String n = RIUtil.dicts.get(fid).getString("dict_name");
                        cjlbs = n + "-" + cjlbs;

                        fid = RIUtil.dicts.get(fid).getString("father_id");
                    }
                } catch (Exception ex) {

                }
                System.out.println(cjlbs);

                one.put("cjlbs", cjlbs);

            } catch (Exception ex) {
                System.out.println(Lib.getTrace(ex));
            }
        }
        if (one.containsKey("cjsj_time") && one.getString("cjsj_time") != null && one.getString("cjsj_time").length() > 0) {
            try {
                if (!one.getString("cjsj_time").contains(":")) {
                    one.put("cjsj_time", RIUtil.get_Time(one.getString("cjsj_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("create_time") && one.getString("create_time") != null && one.getString("create_time").length() > 0) {
            try {
                if (!one.getString("create_time").contains(":")) {
                    one.put("create_time", RIUtil.get_Time(one.getString("create_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("djsj_time") && one.getString("djsj_time") != null && one.getString("djsj_time").length() > 0) {
            try {
                if (!one.getString("djsj_time").contains(":")) {
                    one.put("djsj_time", RIUtil.get_Time(one.getString("djsj_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("sfcs") && one.getString("sfcs").length() > 0) {
            try {
                one.put("sfcs", RIUtil.dicts.get("74-" + one.getString("sfcs")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("sfsjsx_time") && one.getString("sfsjsx_time") != null && one.getString("sfsjsx_time").length() > 0) {
            try {
                if (!one.getString("sfsjsx_time").contains(":")) {
                    one.put("sfsjsx_time", RIUtil.get_Time(one.getString("sfsjsx_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("sfsjxx_time") && one.getString("sfsjxx_time") != null && one.getString("sfsjxx_time").length() > 0) {
            try {
                if (one.getString("sfsjxx_time").contains(":")) {
                    one.put("sfsjxx_time", RIUtil.get_Time(one.getString("sfsjxx_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("xgsj_time") && one.getString("xgsj_time") != null && one.getString("xgsj_time").length() > 0) {
            try {
                if (!one.getString("xgsj_time").contains(":")) {
                    one.put("xgsj_time", RIUtil.get_Time(one.getString("xgsj_time")));
                }
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("djsj_time") && one.getString("djsj_time") != null && one.getString("djsj_time").length() > 0) {
            try {
                if (!one.getString("djsj_time").contains(":")) {
                    one.put("djsj_time", RIUtil.get_Time(one.getString("djsj_time")));
                }
            } catch (Exception ex) {
            }
        }

        if (one.containsKey("sjlb") && one.getString("sjlb") != null && one.getString("sjlb").length() > 0) {
            try {
                one.put("sjlb", RIUtil.dicts.get("55-" + one.getString("sjlb")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        String initPersonM = one.getString("personM");
        if (StringUtils.isNotBlank(initPersonM)) {
            try {
                List<String> personM = RIUtil.HashToList(RIUtil.StringToList(initPersonM));
                //删除的标签不用展示吧
                List<String> personMNew = new ArrayList<>(personM.stream().filter(x -> {
                    Integer level;
                    String memo;
                    JSONObject label4Cache = RIUtil.jqbqs.get(x);
                    if (label4Cache != null) {
                        level = label4Cache.getInteger("static_index");
                        memo = label4Cache.getString("memo");
                    } else {
                        Dict dict = dictService.getOne(Wrappers.<Dict>lambdaQuery()
                                .select(Dict::getId, Dict::getMemo, Dict::getStaticIndex)
                                .eq(Dict::getType, 4)
                                .eq(Dict::getId, x)
                                .eq(Dict::getIsdelete, 1));
                        if (dict != null) {
                            level = dict.getStaticIndex();
                            memo = dict.getMemo();
                        } else return false;
                    }
                    if (memo.contains("人员职业")) {
                        return level == 4 || level == 5;
                    }
                    return dictService.count(Wrappers.<Dict>lambdaQuery()
                            .eq(Dict::getFatherId, x)
                            .eq(Dict::getIsdelete, 1)
                            .last("LIMIT 1")) < 1;
                }).collect(Collectors.toList()));
                List<Dict> dict = dictService.list(Wrappers.<Dict>lambdaQuery()
                        .select(Dict::getId, Dict::getFatherId)
                        .eq(Dict::getType, 4)
                        .eq(Dict::getStaticIndex, 5)
                        .likeRight(Dict::getMemo, "人员职业")
                        .in(Dict::getId, personM));
                if (!dict.isEmpty()) {
                    dict.forEach(x -> personMNew.replaceAll(s -> s.equals(x.getId()) ? x.getFatherId() : s));
                }
                one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(personMNew.toString())));
                one.put("personM", personM);
                one.put("personMNew", personMNew);
            } catch (Exception ex) {
                one.put("_personM", new JSONArray());
                one.put("personM", new ArrayList<>());
            }
        } else {
            HashMap _personMInit = RIUtil.StringToList("304EC96312434E0BB8D206C5A6E86F42");
            List<String> personMNewInit = new ArrayList<>(Arrays.asList("304EC96312434E0BB8D206C5A6E86F42"));
            if (one.containsKey("gmsfhm") && one.getString("gmsfhm").length() > 0) {
                String gmsfhm = one.getString("gmsfhm");
                WjscJqSjxx wjscJqSjxx = wjscJqSjxxMapper.selectOne(Wrappers.<WjscJqSjxx>lambdaQuery()
                        .eq(WjscJqSjxx::getGmsfhm, gmsfhm)
                        .isNotNull(WjscJqSjxx::getPersonM)
                        .ne(WjscJqSjxx::getPersonM, "")
                        .ne(WjscJqSjxx::getPersonM, "[]")
                        .orderByDesc(WjscJqSjxx::getDjsjTime)
                        .last("limit 1"));
                if (wjscJqSjxx != null && StringUtils.isNotBlank(wjscJqSjxx.getPersonM())) {
                    String personM = wjscJqSjxx.getPersonM();
                    List<String> list = RIUtil.HashToList(RIUtil.StringToList(personM));
                    List<String> personMLastTime = new ArrayList<>(
                            list.stream().filter(x -> {
                        Integer level;
                        String memo;
                        JSONObject label4Cache = RIUtil.jqbqs.get(x);
                        if (label4Cache != null) {
                            level = label4Cache.getInteger("static_index");
                            memo = label4Cache.getString("memo");
                        } else {
                            Dict dict = dictService.getOne(Wrappers.<Dict>lambdaQuery()
                                    .select(Dict::getId, Dict::getMemo, Dict::getStaticIndex)
                                    .eq(Dict::getType, 4)
                                    .eq(Dict::getId, x)
                                    .eq(Dict::getIsdelete, 1));
                            if (dict != null) {
                                level = dict.getStaticIndex();
                                memo = dict.getMemo();
                            } else return false;
                        }
                        if (memo.contains("人员职业")) {
                            return level == 4 || level == 5;
                        }
                        return dictService.count(Wrappers.<Dict>lambdaQuery()
                        .eq(Dict::getFatherId, x)
                        .eq(Dict::getIsdelete, 1)
                        .last("LIMIT 1")) < 1;
                    }).collect(Collectors.toList()));
                    //处理 打标人员职业 第五层展示问题
                    List<Dict> dict = dictService.list(Wrappers.<Dict>lambdaQuery()
                            .select(Dict::getId, Dict::getFatherId)
                            .eq(Dict::getType, 4)
                            .eq(Dict::getStaticIndex, 5)
                            .likeRight(Dict::getMemo, "人员职业")
                            .in(Dict::getId, personMLastTime));
                    if (!dict.isEmpty()) {
                        dict.forEach(x -> personMLastTime.replaceAll(s -> s.equals(x.getId()) ? x.getFatherId() : s));
                    }
                    personMNewInit = personMLastTime;
                    _personMInit = RIUtil.StringToList(personMLastTime.toString());
                    getAgeFromSfz(gmsfhm, personMNewInit, _personMInit);
                    one.put("personMNew", new HashSet<>(personMNewInit));
                    one.put("personM", new ArrayList<>());
                    one.put("_personM", RIUtil.RealDictNameList(_personMInit));
                } else {
                    getAgeFromSfz(gmsfhm, personMNewInit, _personMInit);
                    one.put("_personM", RIUtil.RealDictNameList(_personMInit));
                    one.put("personM", new ArrayList<>());
                    one.put("personMNew", new HashSet<>(personMNewInit));
                }
            } else {
                one.put("_personM", RIUtil.RealDictNameList(_personMInit));
                one.put("personM", new ArrayList<>());
                one.put("personMNew", _personMInit);
            }

//            if (one.containsKey("his") && one.getString("his") != null && one.getString("his").length() > 2) {
//                try {
//                    one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("his"))));
//                    one.put("personM", RIUtil.HashToList(RIUtil.StringToList(one.getString("his"))));
//                } catch (Exception ex) {
//                    one.put("_personM", new JSONArray());
//                    one.put("personM", new ArrayList<>());
//                }
//            } else {
//                one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList("304EC96312434E0BB8D206C5A6E86F42")));
//                List<String> pM = new ArrayList<>();
//                pM.add("304EC96312434E0BB8D206C5A6E86F42");
//                one.put("personM", pM);
//            }
        }

        if (one.containsKey("case_type") && one.getString("case_type").length() > 0) {
            try {
                one.put("case_type", RIUtil.dicts.get("81-" + one.getString("case_type")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("case_sub_type") && one.getString("case_sub_type").length() > 0) {
            try {
                one.put("case_sub_type", RIUtil.dicts.get("81-" + one.getString("case_sub_type")).getString(
                        "dict_name"));
            } catch (Exception ex) {
            }
        }
        if (one.containsKey("case_state") && one.getString("case_state").length() > 0) {
            try {
                one.put("case_state", RIUtil.dicts.get("80-" + one.getString("case_state")).getString("dict_name"));
            } catch (Exception ex) {
            }
        }
        return one;
    }

    private void getAgeFromSfz(String gmsfzh, List<String> personMNew, HashMap _personM) {
        String birthDateStr;
        String gmsfhm = gmsfzh;
        if (gmsfhm.length() == 18 || gmsfhm.length() == 15 ) {
            if (gmsfhm.length() == 18) {
                birthDateStr = gmsfhm.substring(6, 14);
            } else {
                birthDateStr = "19" + gmsfhm.substring(6, 12);
            }
            LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            int age = Period.between(birthDate, LocalDate.now()).getYears();
            String wcnrDictId = "D9CE0F54B8B44023ABD4A04FEA940FBA";
            if(age < 18) {
                personMNew.add(wcnrDictId);
                _personM.put(wcnrDictId, "");
            }
            List<Dict> ageDict = dictService.lambdaQuery()
                    .select(Dict::getId, Dict::getDictName, Dict::getFatherId)
                    .eq(Dict::getType, 4)
                    .eq(Dict::getIsdelete, 1)
                    .eq(Dict::getStaticIndex, 3)
                    .eq(Dict::getFatherId, "04FBCA6B6EC04EB3AC2EA103078B8016")
                    .orderByAsc(Dict::getIndexNo)
                    .list();
            List<String> ageIds = ageDict.stream().map(Dict::getId).collect(Collectors.toList());
            personMNew.removeIf(s -> ageIds.contains(s));
            _personM.entrySet().removeIf(s -> ageIds.contains(s));

            ageDict.stream().filter(x -> {
                String dictName = x.getDictName();
                Pattern compile = Pattern.compile("^(\\d+)岁以下$|^(\\d+)-(\\d+)岁$|^(\\d+)岁以上$");
                Matcher matcher = compile.matcher(dictName);
                if (matcher.find()) {
                    int min;
                    int max;
                    if (matcher.group(1) != null) {
                        min = 0;
                        max = Integer.parseInt(matcher.group(1)) - 1;
                    } else if (matcher.group(4) != null) {
                        min = Integer.parseInt(matcher.group(4)) + 1;
                        max = Integer.MAX_VALUE;
                    } else {
                        min = Integer.parseInt(matcher.group(2));
                        max = Integer.parseInt(matcher.group(3));
                    }
                    return age >= min && age <= max;
                } else {
                    return false;
                }
            }).map(Dict::getId).findFirst().ifPresent(y -> {
                personMNew.add(y);
                _personM.put(y, "");
            });
        }
    }

    @PostMapping("/det_sjry")
    @ApiOperation(value = "警情查询-根据jjbh获取涉警人员明细")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<List<RespSJRYList>> detSJRY(@Valid @RequestBody ReqJQListOne params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getDetSJRY(data);
        //return R.ok();
    }

    private R<List<RespSJRYList>> getDetSJRY(JSONObject data) {
        //RespSJRY sjry = new RespSJRY();

        List<RespSJRYList> sjrys = new ArrayList<>();
        try {
            String jjbh = data.getString("jjbh");


            String sqls = "select a.*,b.personMs as his from wjsc_jq_sjxx a left join jq_person b on a.gmsfhm=b" +
                    ".gmsfhm" + " " + "where " + "a.jjbh='" + jjbh + "'" + " and a.zxbs = '0'";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 999);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);

                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.getJSONObject(i);
                    one = RealJQDet(one);
                    String jgbh = one.getString("jgbh");
                    String dwmc = one.getString("dwmc");
                    if (jgbh != null && dwmc != null && jgbh.length() > 2) {
                        one.put("jgbh", jgbh + "|" + dwmc);
                    } else {
                        one.put("jgbh", "");
                    }
                    RespSJRYList sjone = new Gson().fromJson(String.valueOf(one), RespSJRYList.class);
                    sjrys.add(sjone);
                }

            }

            return R.ok(sjrys);

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }


    }


    public R<List<RespJQList>> searchJQList(JSONObject data) {
        JSONObject back = new JSONObject();
        String sql = "";
        int page = 1;
        int limit = 20;
        try {


//处警类别
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lbs = data.getString("cjlb");
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cj.cjlb like '" + cjlb.substring(0, 2) + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cj.cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cj.cjlb like '" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cj.cjlb='" + cjlb + "' or ";
                    }
                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

//处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                String jjbh = data.getString("jjbh");
                if (jjbh.endsWith(",")) {
                    jjbh = jjbh.substring(0, jjbh.length() - 1);
                }
                if (jjbh.startsWith("J") && jjbh.length() == 20) {
                    sql = sql + " and cj.jjbh='" + jjbh + "' ";
                } else if (jjbh.length() > 22 && jjbh.contains(",")) {
                    //jjbh = jjbh.substring(0, jjbh.length() - 1);
                    jjbh = jjbh.replace(",", "','");
                    sql = sql + " and cj.jjbh in ('" + jjbh + "') ";
                } else {
                    sql = sql + " and cj.jjbh like '%" + jjbh + "%' ";
                }
            }

//事发场所
            if (data.containsKey("sfcs") && data.getString("sfcs").length() > 0) {

                String lbs = data.getString("sfcs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sfcs = cone.getKey();


                    if (sfcs.contains("-")) {
                        sfcs = sfcs.split("\\-")[1];
                    }
                    if (sfcs.endsWith("00")) {
                        s = s + "  cj.sfcs like '%" + sfcs.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.sfcs='" + sfcs + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //发生原因
            if (data.containsKey("fsyy") && data.getString("fsyy").length() > 0) {

                String lbs = data.getString("fsyy");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fsyy = cone.getKey();


                    if (fsyy.contains("-")) {
                        fsyy = fsyy.split("\\-")[1];
                    }
                    if (fsyy.endsWith("00")) {
                        s = s + "  cj.fsyy like '0" + fsyy.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.fsyy='0" + fsyy + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
//处理结果内容
            if (data.containsKey("cljgnr") && data.getString("cljgnr").length() > 0) {
                String cljgnr = data.getString("cljgnr");
                sql = sql + " and cj.cljgnr like '%" + cljgnr + "%' ";
            }

//处警详址
            if (data.containsKey("cjxz") && data.getString("cjxz").length() > 0) {
                String cjxz = data.getString("cjxz");
                sql = sql + " and cj.cjxz like '%" + cjxz + "%' ";
            }

//处警结果
            if (data.containsKey("cjjg") && data.getString("cjjg").length() > 0) {
                String lbs = data.getString("cjjg");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjg = cone.getKey();


                    if (cjjg.contains("-")) {
                        cjjg = cjjg.split("\\-")[1];
                    }
                    s = s + "  cj.cjjg='" + cjjg + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //事发时间下限开始
            if (data.containsKey("sfsjxx_start") && data.getString("sfsjxx_start").length() > 0) {
                String sfsjxx_start = data.getString("sfsjxx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time>='" + sfsjxx_start + "' ";
            }

            //事发时间下限结束
            if (data.containsKey("sfsjxx_end") && data.getString("sfsjxx_end").length() > 0) {
                String sfsjxx_end = data.getString("sfsjxx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time<='" + sfsjxx_end + "' ";
            }

            //事发时间上限开始
            if (data.containsKey("sfsjsx_start") && data.getString("sfsjsx_start").length() > 0) {
                String sfsjsx_start = data.getString("sfsjsx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time>='" + sfsjsx_start + "' ";
            }

            //事发时间上限结束
            if (data.containsKey("sfsjsx_end") && data.getString("sfsjsx_end").length() > 0) {
                String sfsjsx_end = data.getString("sfsjsx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time<='" + sfsjsx_end + "' ";
            }

            //处警登记时间开始
            if (data.containsKey("djsj_start") && data.getString("djsj_start").length() > 0) {
                String djsj_start = data.getString("djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time>='" + djsj_start + "' ";
            }

            //处警登记时间结束
            if (data.containsKey("djsj_end") && data.getString("djsj_end").length() > 0) {
                String djsj_end = data.getString("djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time<='" + djsj_end + "' ";
            }

            //处警修改时间开始
            if (data.containsKey("cj_xgsj_start") && data.getString("cj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("cj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time>='" + xgsj_start + "' ";
            }

            //处警修改时间结束
            if (data.containsKey("cj_xgsj_end") && data.getString("cj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("cj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time<='" + xgsj_end + "' ";
            }


            //处警标识
            if (data.containsKey("cjbs") && data.getString("cjbs").length() > 0) {
                String lbs = data.getString("cjbs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjbs = cone.getKey();

                    if (cjbs.contains("-")) {
                        cjbs = cjbs.split("\\-")[1];
                    }
                    s = s + " jj.cjbs='" + cjbs + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警报警人
            if (data.containsKey("bjr") && data.getString("bjr").length() > 0) {
                String bjr = data.getString("bjr");
                sql = sql + " and jj.bjr like '%" + bjr + "%' ";
            }

            //报警类型
            if (data.containsKey("bjlx") && data.getString("bjlx").length() > 0) {
                String lbs = data.getString("bjlx");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjlx = cone.getKey();


                    if (bjlx.contains("-")) {
                        bjlx = bjlx.split("\\-")[1];
                    }
                    if (bjlx.endsWith("0000")) {
                        s = s + " jj.bjlx like '" + bjlx.replace("0000", "") + "%' or ";
                    } else if (bjlx.endsWith("00")) {
                        s = s + "  jj.bjlx like '" + bjlx.replace("00", "") + "%' or ";
                    } else {

                        s = s + " jj.bjlx='" + bjlx + "' or ";
                    }

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警单位
            String unit1 = data.getString("unit");
            List<String> jjdws = new ArrayList<>();
            jjdws.add(unit1);
            if (StringUtils.isNotBlank(data.getString("jjdw"))) {
                List<String> jjdwList = JSONArray.parseArray(data.getString("jjdw"), String.class);
                if (CollectionUtils.isNotEmpty(jjdwList)) jjdws = jjdwList;
            }
            if (unit1.startsWith("32041248")) {
                jjdws.removeIf(x -> !x.startsWith("320412"));
                if (jjdws.isEmpty()) return R.ok(Collections.emptyList(), 0);
            }
            String jjdwCondition = jjdws.stream().map(jjdw -> {
                Integer type = RIUtil.dicts.get(jjdw).getInteger("type");
                if (type == 23 || type == 24) {
                    return jjdw.substring(0, 6);
                } else if (type == 26 || type == 25) {
                    return jjdw.substring(0, 8);
                } else if (type == 21 || type == 22 || type == 27) {
                    return "";
                } else {
                    return jjdw;
                }
            }).filter(StringUtils::isNotBlank).map(jjdw -> " jj.jjdw like '" + jjdw + "%'").collect(Collectors.joining(" or "));

            if (StringUtils.isNotBlank(jjdwCondition)) {
                sql = sql + " and (" + jjdwCondition + ") ";
            }

//            String unit = "";
//            String relau = unit1;
//            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
//                unit = data.getString("jjdw");
//
//            } else {
//                unit = unit1;
//
//            }
//            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
//            String sq = "";
//            for (int i = 0; i < us.size(); i++) {
//                String u = us.get(i);
//
//                int type = 23;
//                try {
//                    type = RIUtil.dicts.get(u).getInteger("type");
//                } catch (Exception e) {
//                    log.warn(u);
//                    log.warn("d.size-->" + RIUtil.dicts.size());
//
//                }
//                if (type == 21 || type == 22 || type == 27) {
//                    // sql = sql + " and jj.jjdw like '%" + unit.substring(0, 4) + "%'";
//                } else if (type == 23 || type == 24) {
//                    if (relau.startsWith("32041248")) {
//                        sq = sq + "  jj.jjdw ='320512' or ";
//                    } else {
//                        sq = sq + "  jj.jjdw like '" + u.substring(0, 6) + "%' or ";
//                    }
//                }else if (type == 26||type==25) {
//
//                    sq = sq + "  jj.jjdw like '" + u.substring(0, 8) + "%' or ";
//                } else {
//                    sq = sq + "  jj.jjdw='" + u + "' or ";
//                }
//            }

//            if (sq.endsWith("or ")) {
//                sq = sq.substring(0, sq.length() - 3);
//                sql = sql + " and (" + sq + ") ";
//            }


            //接警报警人联系电话
            if (data.containsKey("lxdh") && data.getString("lxdh").length() > 0) {
                String lxdh = data.getString("lxdh");
                sql = sql + " and jj.lxdh like '%" + lxdh + "%' ";
            }

            //报警内容
            if (data.containsKey("bjnr") && data.getString("bjnr").length() > 0) {
                String bjnr = data.getString("bjnr");
                sql = sql + " and jj.bjnr like '%" + bjnr + "%' ";
            }

            //发生地点
            if (data.containsKey("sfdd") && data.getString("sfdd").length() > 0) {
                String sfdd = data.getString("sfdd");
                sql = sql + " and jj.sfdd like '%" + sfdd + "%' ";
            }

            //报警方式
            if (data.containsKey("bjxs") && data.getString("bjxs").length() > 0) {
                String lbs = data.getString("bjxs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjxs = cone.getKey();


                    if (bjxs.contains("-")) {
                        bjxs = bjxs.split("\\-")[1];
                    }
                    s = s + "  jj.bjxs='" + bjxs + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警登记时间开始
            if (data.containsKey("jj_djsj_start") && data.getString("jj_djsj_start").length() > 0) {
                String djsj_start = data.getString("jj_djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time>='" + djsj_start + "' ";
            }

            //接警登记时间结束
            if (data.containsKey("jj_djsj_end") && data.getString("jj_djsj_end").length() > 0) {
                String djsj_end = data.getString("jj_djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time<='" + djsj_end + "' ";
            }

            //接警修改时间开始
            if (data.containsKey("jj_xgsj_start") && data.getString("jj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("jj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time>='" + xgsj_start + "' ";
            }

            //接警修改时间结束
            if (data.containsKey("jj_xgsj_end") && data.getString("jj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("jj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time<='" + xgsj_end + "' ";
            }

            //接警日期时间开始
            if (data.containsKey("jjrqsj_start") && data.getString("jjrqsj_start").length() > 0) {
                String jjrqsj_start = data.getString("jjrqsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.bjdhsj_time>='" + jjrqsj_start + "' ";
            }

            //接警日期时间结束
            if (data.containsKey("jjrqsj_end") && data.getString("jjrqsj_end").length() > 0) {
                String jjrqsj_end = data.getString("jjrqsj_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and jj.bjdhsj_time<='" + jjrqsj_end + "' ";
            }

            //涉警人员身份证
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                String gmsfhm = data.getString("gmsfhm");

                if (gmsfhm.length() == 18) {
                    sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm = '" + gmsfhm + "') ";
                } else {
                    sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm like '%" + gmsfhm + "%') ";
                }
            }

            //涉警人员姓名
            if (data.containsKey("xm") && data.getString("xm").length() > 0) {
                String xm = data.getString("xm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where xm like '%" + xm + "%') ";
            }

            //涉警类别
            if (data.containsKey("sjlb") && data.getString("sjlb").length() > 0) {
                String lbs = data.getString("sjlb");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sjlb = cone.getKey();
                    if (sjlb.contains("-")) {
                        sjlb = sjlb.split("\\-")[1];
                    }
                    s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where sjlb ='" + sjlb + "') or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //反馈时间

            String fksql = " ";
            if (data.containsKey("fk_time_start") && data.getString("fk_time_start").length() > 0) {
                String fk_time_start = data.getString("fk_time_start");

                fksql = fksql + "and create_time >='" + fk_time_start + "' ";
            }

            if (data.containsKey("fk_time_end") && data.getString("fk_time_end").length() > 0) {
                String fk_time_end = data.getString("fk_time_end");

                fksql = fksql + "and create_time <='" + fk_time_end + "' ";
            }
            if (fksql.length() > 5) {
                fksql = "select jjbh from jq_fk where 1=1 " + fksql;
                sql = sql + " and cj.jjbh in (" + fksql + ")";
            }


            //反馈人
            if (data.containsKey("fk_user") && data.getString("fk_user").length() > 0) {
                String fk_user = data.getString("fk_user");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where fkr  like '%" + fk_user + "%') ";
            }
            //反馈单位
            if (data.containsKey("fk_unit") && data.getString("fk_unit").length() > 0) {
                String fk_unit = data.getString("fk_unit");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where unit ='" + fk_unit + "') ";
            }
            //省厅标签dict:75
            if (data.containsKey("cjjqbq") && data.getString("cjjqbq").length() > 0) {
                String lbs = data.getString("cjjqbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjqbq = cone.getKey();

                    if (cjjqbq.contains("-")) {
                        cjjqbq = cjjqbq.split("\\-")[1];
                    }
                    s = s + "  cjjqbq like '%" + cjjqbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //分局标签dict:76
            if (data.containsKey("fjbq") && data.getString("fjbq").length() > 0) {
                String lbs = data.getString("fjbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fjbq = cone.getKey();

                    if (fjbq.contains("-")) {
                        fjbq = fjbq.split("\\-")[1];
                    }
                    s = s + "  fjbq like '%" + fjbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //标注标签dict:10
            if (data.containsKey("bzbq") && data.getString("bzbq").length() > 3) {
                String lbs = data.getString("bzbq");
                System.out.println(lbs);

                String plbs = "";
                String dlbs = "";

                List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                for (int i = 0; i < lbss.size(); i++) {
                    String lb = lbss.get(i);
                    int lbt = RIUtil.dicts.get(lb).getIntValue("type");
                    if (lbt == 4) {
                        //plbs = plbs + lb + " ";
//                        if (lb.contains(":") || lb.contains("-")){
//                            lb = lb.replaceAll("[:-]", " +");
//                        }
//                        plbs = plbs + "+" + lb + " ";

                        plbs = plbs + "\"" + lb + "\" ";
                    } else {
                        //dlbs = dlbs + lb + " ";
//                        if (lb.contains(":") || lb.contains("-")){
//                            String matchCondition = lb.replaceAll("[:-]", " +");
//                            dlbs = dlbs + "+" + matchCondition + " ";
//                        } else {
//                            dlbs = dlbs + "+" + lb + " ";
//                        }
                        dlbs = dlbs + "\"" + lb + "\" ";
                    }
                }

                String lbsql = "";
                if (dlbs.length() > 2) {
                    lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode))  ";
                }

                if (plbs.length() > 2) {
                    if (lbsql.contains("MATCH")) {
                        //todo
                        lbsql = lbsql + " or ";
                    }
                    lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                            + "AGAINST ('" + plbs + "')" + " ) ";
                }

                if (lbsql.length() > 2) {
                    sql = sql + " and (" + lbsql + ") ";
                }

            }

            //标注公式
            if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
                String bzgs = data.getString("bzgs");
                List<String> gss = RIUtil.StringToArrayList(bzgs);
                String s = BzgsUtil.convertBzCondition(gss);
                // = "";
//                for (int i = 0; i < gss.size(); i++) {
//                    String g = gss.get(i);
//
//
//                    if (g.equals("(") || g.equals(")") || g.equals("AND") || g.equals("NOT") || g.equals("OR")) {
//
//                        s = s + " " + g + " ";
//
//                    } else {
////                        if ((!s.startsWith("AND ") || !s.startsWith(") ") || !s.startsWith("OR ")) && s.length() > 5) {
////                            s = s + " or ";
////                        }
//                        if ((!StringUtils.endsWith(s, "AND ") && !StringUtils.endsWith(s, "OR ")) && s.length() > 5) {
//                            s = s + " AND ";
//                        }
//                        int t = RIUtil.dicts.get(g).getIntValue("type");
////                        if (g.contains(":") || g.contains("-")){
////                            g = g.replaceAll("[:-]", " +");
////                        }
////                        g = "+" + g;
////                        if (t != 4) {
////                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
////                        } else {
////                            s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
////                        }
//                        g = "\"" + g + "\"";
//                        if (t != 4) {
//                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                        } else {
//                            s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
//                        }
//                    }
//                }
                if (s.length() > 3) {
                    s = s.replace("NOT", "!=");
                    System.out.println(s);

                    sql = sql + " and (" + s + ")";
                }

            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            int isExp = 0;
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String sqls =
                    "select cj.jjbh,jj.bjdhsj_time as djsj_time,jjdwmc,bjlx,cjlb,cjjg,'' as fjbq,sfcs,cjjqbq," +
                            "cljgnr  " + "from " + "wjsc_jq_cjxx cj LEFT JOIN" + " wjsc_jq_jjxx jj on cj.jjbh=jj" +
                            ".jjbh" + " where " + "1=1 " + sql + " GROUP BY cj.jjbh order by bjdhsj_time " + "desc ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespJQList> d = RealJQINfoList(ret);
                sqls = "select count( DISTINCT cj.jjbh) as count from wjsc_jq_cjxx cj" + " LEFT JOIN" + " wjsc_jq_jjxx jj on " + "cj" + ".jjbh=jj.jjbh where 1=1 " + sql +" ";
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }


    private List<RespJQList> RealJQINfoList(JSONArray list) {
        List<RespJQList> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.getJSONObject(i);
            // System.out.println(one);

            if (one.containsKey("bjlx") && one.getString("bjlx") != null && one.getString("bjlx").length() > 0) {
                try {
                    one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjlb") && one.getString("cjlb").length() > 0) {
                try {
                    one.put("cjlb", RIUtil.dicts.get("51-" + one.getString("cjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjjg") && one.getString("cjjg").length() > 0) {
                try {
                    one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("fjbq") && one.getString("fjbq").length() > 0) {
                try {
                    one.put("fjbq", RIUtil.dicts.get(one.getString("fjbq")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }

            if (one.containsKey("cjjqbq") && one.getString("cjjqbq").length() > 0) {
                try {
                    one.put("cjjqbq", RIUtil.dicts.get("75-" + one.getString("cjjqbq")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("sfcs") && one.getString("sfcs").length() > 0) {
                try {
                    one.put("sfcs", RIUtil.dicts.get("74-" + one.getString("sfcs")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("djsj_time") && one.getString("djsj_time") != null && one.getString("djsj_time").length() > 0) {
                try {
                    if (!one.getString("djsj_time").contains(":")) {
                        one.put("djsj_time", RIUtil.get_Time(one.getString("djsj_time")));
                    }
                } catch (Exception ex) {
                }
            }


            Gson gson = new Gson();
            RespJQList respJQList = gson.fromJson(String.valueOf(one), RespJQList.class);
            back.add(respJQList);

        }
        return back;
    }

    private JSONArray RelaSjry(JSONArray ret) {
        JSONArray back = new JSONArray();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);

            if (one.containsKey("sjlb") && one.getString("sjlb") != null && one.getString("sjlb").length() > 0) {
                try {
                    one.put("sjlb", RIUtil.dicts.get("55-" + one.getString("sjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("djsj_time") && one.getString("djsj_time").length() > 0) {
                try {
                    one.put("djsj_time", RIUtil.get_Time(one.getString("djsj_time")));
                } catch (Exception ex) {
                }
            }
            RespSJRYList dd = gson.fromJson(String.valueOf(one), RespSJRYList.class);
            back.add(dd);
        }
        return back;
    }


}
