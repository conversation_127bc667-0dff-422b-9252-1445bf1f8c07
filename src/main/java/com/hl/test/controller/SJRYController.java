package com.hl.test.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;

import com.hl.test.Utils.BzgsUtil;
import com.hl.test.Utils.Export.ExportInterface;
import com.hl.test.Utils.Export.ExportXlsxHelper;
import com.hl.test.Utils.Lib;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.req.ReqSJRY;
import com.hl.test.domain.req.ReqSjryQuery;
import com.hl.test.domain.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@Api(tags = "涉警人员查询")
@RequestMapping("/jq_sjry")
@SuppressWarnings("unchecked")
public class SJRYController {


    @Value("${upload_path}")
    private String upload_path;

    @Resource
    private JdbcTemplate jdbcTemplate;

    // 警情列表
    @PostMapping("/list")
    @ApiOperation(value = "涉警人员-列表查询")

    // @LogMysql(title = "警情查询列表", operate = OperateType.SELECT)
    public R<List<RespSJRYList>> list(@Valid @RequestBody ReqSJRY params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        //  System.out.println(user);

        System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return searchList(data);
        //return R.ok();
    }

    private R<List<RespSJRYList>> searchList(JSONObject data) {
        String sql = "";
        int limit = 20;
        int page = 1;
        try {
            //涉警类别 dict:55
            if (data.containsKey("sjlb") && data.getString("sjlb").length() > 0) {
                String sjlb = data.getString("sjlb");
                if (sjlb.contains("-")) {
                    sjlb = sjlb.split("\\-")[1];
                }
                sql = sql + " and sj.sjlb='" + sjlb + "'  ";
            }
            //姓名
            if (data.containsKey("xm") && data.getString("xm").length() > 0) {
                String xm = data.getString("xm");
                sql = sql + " and sj.xm like '%" + xm + "%'  ";
            }
            //身份证
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                String gmsfhm = data.getString("gmsfhm").trim().replace("\n", "").replace("\r", "");
                System.out.println(gmsfhm + "->" + gmsfhm.length());
                if (gmsfhm.length() == 18) {
                    sql = sql + " and  sj.gmsfhm = '" + gmsfhm + "'  ";
                } else {
                    sql = sql + " and  sj.gmsfhm like '%" + gmsfhm + "%'  ";
                }
            }
            //接警报警人
            if (data.containsKey("bjr") && data.getString("bjr").length() > 0) {
                String bjr = data.getString("bjr");
                sql = sql + " and  jj.bjr like '%" + bjr + "%'  ";
            }
            //报警方式 dict:45
            if (data.containsKey("bjxs") && data.getString("bjxs").length() > 0) {
                String bjxs = data.getString("bjxs");
                if (bjxs.contains("-")) {
                    bjxs = bjxs.split("\\-")[1];
                }
                sql = sql + " and  jj.bjxs='" + bjxs + "'  ";
            }
            //报警类型 dict:50/51
            if (data.containsKey("bjlx") && data.getString("bjlx").length() > 0) {
                String bjlx = data.getString("bjlx");
                if (bjlx.contains("-")) {
                    bjlx = bjlx.split("\\-")[1];
                }
                sql = sql + " and jj.bjlx='" + bjlx + "'  ";
            }
            //处警类别 dict：51
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {
                String cjlb = data.getString("cjlb");
                if (cjlb.contains("-")) {
                    cjlb = cjlb.split("\\-")[1];
                }
                sql = sql + "and  cj.cjlb='" + cjlb + "'  ";
            }
            //处警结果 dict:46
            if (data.containsKey("cjjg") && data.getString("cjjg").length() > 0) {
                String cjjg = data.getString("cjjg");
                if (cjjg.contains("-")) {
                    cjjg = cjjg.split("\\-")[1];
                }
                sql = sql + "and  cjjg='" + cjjg + "'  ";
            }
            //接警单位
            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
                String jjdw = data.getString("jjdw");
                List<String> us = RIUtil.HashToList(RIUtil.StringToList(jjdw));
                String sq = "";
                for (int i = 0; i < us.size(); i++) {
                    String u = us.get(i);
                    int type = 23;
                    try {
                        type = RIUtil.dicts.get(u).getInteger("type");
                    } catch (Exception ex) {
                        log.warn(u);
                        try {
                            log.warn(String.valueOf(RIUtil.dicts.get(u)));
                        } catch (Exception e) {

                        }
                    }
                    if (type == 21 || type == 22 || type == 27) {
                        // sql = sql + " and jj.jjdw like '%" + unit.substring(0, 4) + "%'";
                    } else if (type == 23 || type == 24) {
                        //sq = sq + "  jj.jjdw like '" + u.substring(0, 6) + "%' or ";
                    } else if (type == 26 || type == 25) {

                        sq = sq + "  jj.jjdw like '" + u.substring(0, 8) + "%' or ";
                    } else {
                        sq = sq + "  jj.jjdw='" + u + "' or ";
                    }
                }
                if (sq.endsWith("or ")) {
                    sq = sq.substring(0, sq.length() - 3);
                    sql = sql + " and (" + sq + ") ";
                }
            }
            //接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                String jjbh = data.getString("jjbh");
                sql = sql + "and  jj.jjbh like '%" + jjbh + "%'  ";
            }
            //处理结果内容
            if (data.containsKey("cljgnr") && data.getString("cljgnr").length() > 0) {
                String cljgnr = data.getString("cljgnr");
                sql = sql + " and cj.cljgnr like '%" + cljgnr + "%' ";
            }
            //接警报警时间
            if (data.containsKey("bjdhsj_time_start") && data.getString("bjdhsj_time_start").length() > 0) {
                String bjdhsj_time_start =
                        data.getString("bjdhsj_time_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.bjdhsj_time>='" + bjdhsj_time_start + "'  ";
            }
            //接警报警时间
            if (data.containsKey("bjdhsj_time_end") && data.getString("bjdhsj_time_end").length() > 0) {
                String bjdhsj_time_end = data.getString("bjdhsj_time_end").replace("-", "").replace(":", "").replace(
                        " ", "");
                sql = sql + " and jj.bjdhsj_time<='" + bjdhsj_time_end + "'  ";
            }
            //是否未成年
            if (data.containsKey("isWcn") && data.getString("isWcn").length() > 0) {
                String isWcn = data.getString("isWcn");
                if (isWcn.equals("0")) {
                    sql = sql + "and FLOOR(DATEDIFF(CURDATE(),STR_TO_DATE(SUBSTRING(gmsfhm, 7, 8), '%Y%m%d')) / 365" + ".25 )" + " <18  ";
                }
            }
            //省厅标签 dict:??
            if (data.containsKey("cjjgbq") && data.getString("cjjgbq").length() > 0) {
                String stbq = data.getString("cjjgbq");
                if (stbq.contains("-")) {
                    stbq = stbq.split("\\-")[1];
                }
                sql = sql + " and cj.cjjqbq='" + stbq + "' ";
            }
            //分局标签 dict:??
            if (data.containsKey("fjbq") && data.getString("fjbq").length() > 0) {
                String fjbq = data.getString("fjbq");
                if (fjbq.contains("-")) {
                    fjbq = fjbq.split("\\-")[1];
                }
                sql = sql + " and cj.fjbq='" + fjbq + "' ";
            }

            //标注标签dict:10
            if (data.containsKey("_personM") && data.getString("_personM").length() > 3) {
                String lbs = data.getString("_personM");

                sql = sql + "and cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                        + "AGAINST ('" + lbs + "')" + " ) ";


            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select sj.sjlb,xm,gmsfhm,jj.jjbh,jj.bjdhsj_time,jj.bjlx,cj.cjlb,cj.cjjg,sj.personM from " +
                            "wjsc_jq_sjxx sj LEFT JOIN  wjsc_jq_jjxx jj on sj.jjbh=jj.jjbh " + " LEFT JOIN  " +
                            "wjsc_jq_cjxx cj " + "on sj.jjbh=cj.jjbh " + "where sj.zxbs = '0' and cjlb is not null and bjdhsj_time is " + "not null and  1=1  " + sql + " " + "  order by bjdhsj_time " + "desc ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespSJRYList> d = RealSJRYINfoList(ret);
                sqls = "select count(sj.uuid) as count from  wjsc_jq_cjxx cj LEFT JOIN  wjsc_jq_jjxx jj on cj" +
                        ".jjbh=jj" + ".jjbh " + " LEFT JOIN  wjsc_jq_sjxx sj on cj.jjbh=sj.jjbh " + "where 1=1 " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }


    }

    private List<RespSJRYList> RealSJRYINfoList(JSONArray ret) {
        List<RespSJRYList> back = new ArrayList<>();
        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            if (one.containsKey("sjlb") && one.getString("sjlb") != null && one.getString("sjlb").length() > 0) {
                try {
                    one.put("sjlb", RIUtil.dicts.get("55-" + one.getString("sjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjxs") && one.getString("bjxs") != null && one.getString("bjxs").length() > 0) {
                try {
                    one.put("bjxs", RIUtil.dicts.get("45-" + one.getString("bjxs")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjlx") && one.getString("bjlx") != null && one.getString("bjlx").length() > 0) {
                try {
                    one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjlb") && one.getString("cjlb") != null && one.getString("cjlb").length() > 0) {
                try {
                    one.put("cjlb", RIUtil.dicts.get("51-" + one.getString("cjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjjg") && one.getString("cjjg") != null && one.getString("cjjg").length() > 0) {
                try {
                    one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjdhsj_time") && one.getString("bjdhsj_time") != null && one.getString("bjdhsj_time").length() > 0) {
                try {
                    one.put("bjdhsj_time_start", RIUtil.get_Time(one.getString("bjdhsj_time")));
                } catch (Exception ex) {
                    one.put("bjdhsj_time_start", "");
                }
            } else {
                one.put("bjdhsj_time_start", "");
            }
            if (one.containsKey("cjjqbq") && one.getString("cjjqbq") != null && one.getString("cjjqbq").length() > 0) {
                try {
                    one.put("cjjqbq", RIUtil.dicts.get("75-" + one.getString("cjjqbq")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("fjbq") && one.getString("fjbq") != null && one.getString("fjbq").length() > 0) {
                try {
                    one.put("fjbq", RIUtil.dicts.get("76-" + one.getString("fjbq")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }


            if (one.containsKey("personM") && one.getString("personM") != null && one.getString("personM").length() > 0) {
                try {
                    one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("personM"))));
                    one.put("personM", RIUtil.HashToList(RIUtil.StringToList(one.getString("personM"))));
                } catch (Exception ex) {
                    one.put("_personM", new JSONArray());
                    one.put("personM", new ArrayList<>());
                }
            } else {
                one.put("_personM", new JSONArray());
                one.put("personM", new ArrayList<>());
            }


            RespSJRYList det = new Gson().fromJson(String.valueOf(one), RespSJRYList.class);
            back.add(det);

        }


        return back;
    }

    // 警情列表
    @PostMapping("/det")
    @ApiOperation(value = "涉警人员查询-根据gmsfhm获取明细")

    public R<RespSJRY> det(@Valid @RequestBody ReqSjryQuery params) throws Exception {
        //log.warn(params.toString());
        User user = UserUtils.getUserNull();
        //  System.out.println(user);

        //  System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getDet(data);
        //return R.ok();
    }

    private R<RespSJRY> getDet(JSONObject data) {
        try {
            String gmsfhm = data.getString("gmsfhm");
            String sql = "";

            //实有人口数据
            sql = "select * from jq_person where gmsfhm='" + gmsfhm + "'";

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 20);
            RespSJRY rk = new RespSJRY();
            if (list.size() > 0) {
                // doSjry(gmsfhm);
                list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 20);
                JSONArray ret = RIUtil.ListMap2jsa(list);
                JSONObject one = ret.getJSONObject(0);

                rk = relaRKInfo(one);


            } else {
                sql = "select * from wjsc_jq_sjxx where gmsfhm='" + gmsfhm + "'";
                list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 20);

                String jjbhs = "";
                String xm = "";
                if (list.size() > 0) {
                    JSONArray ret = RIUtil.ListMap2jsa(list);
                    for (int i = 0; i < ret.size(); i++) {
                        JSONObject one = ret.getJSONObject(i);
                        String jjbh = one.getString("jjbh");
                        jjbhs = jjbhs + jjbh + "|";
                        xm = one.getString("xm");
                    }

                    sql = "insert into jq_person (id,gmsfhm,xm,jjbhs) values('" + gmsfhm + "','" + gmsfhm + "','" + xm + "','" + jjbhs + "')";
                    jdbcTemplate.update(sql);
                    //  doSjry(gmsfhm);

                    sql = "select * from jq_person where gmsfhm='" + gmsfhm + "'";

                    list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 20);
                    rk = new RespSJRY();
                    if (list.size() > 0) {
                        ret = RIUtil.ListMap2jsa(list);
                        JSONObject one = ret.getJSONObject(0);
                        rk = relaRKInfo(one);
                    }

                }


            }


            log.warn(rk.toString());
            return R.ok(rk);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }

    }

    private String GetJjbhsFromCols(JSONObject data) {
        String sql = "";

        //接警单位
        String unit = "";
        String relau = data.getString("unit");
        if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
            unit = data.getString("jjdw");


        } else {
            unit = data.getString("unit");

        }
        List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
        String sq = "";
        for (int i = 0; i < us.size(); i++) {
            String u = us.get(i);

            int type = 23;
            try {
                type = RIUtil.dicts.get(u).getInteger("type");
            } catch (Exception e) {
                log.warn(u);
                log.warn("d.size-->" + RIUtil.dicts.size());

            }
            if (type == 21 || type == 22 || type == 27) {
                // sql = sql + " and jjdw like '%" + unit.substring(0, 4) + "%'";
            } else if (type == 23 || type == 24) {

                //sq = sq + "  jjdw like '" + u.substring(0, 6) + "%' or ";
            } else if (type == 25) {
                sq = sq + "  jjdw='" + u + "' or ";
            } else if (type == 26) {

                sq = sq + "  jjdw like '" + u.substring(0, 8) + "%' or ";
            } else {
                sq = sq + "  jjdw='" + u + "' or ";
            }
        }
        if (sq.endsWith("or ")) {
            sq = sq.substring(0, sq.length() - 3);
            sql = sql + " and (" + sq + ") ";
        }
        //接警时间开始
        if (data.containsKey("bjdhsj_time_start") && data.getString("bjdhsj_time_start").length() > 0) {
            String bjdhsj_time_start = data.getString("bjdhsj_time_start").replace("-", "").replace(":", "").replace(
                    " ", "");
            ;
            sql = sql + " and bjdhsj_time>='" + bjdhsj_time_start + "' ";
        }
        //接警时间结束
        if (data.containsKey("bjdhsj_time_end") && data.getString("bjdhsj_time_end").length() > 0) {
            String bjdhsj_time_end = data.getString("bjdhsj_time_end").replace("-", "").replace(":", "").replace(" ",
                    "");
            ;
            sql = sql + " and bjdhsj_time<='" + bjdhsj_time_end + "' ";
        }
        //标注时间开始
        if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
            String bz_time_start = data.getString("bz_time_start").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and bz_time>='" + bz_time_start + "' ";
        }
        //标注时间结束
        if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
            String bz_time_end = data.getString("bz_time_end").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and bz_time<='" + bz_time_end + "' ";
        }
        //处警类别
        if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

            String lbs = data.getString("cjlb");
            if (lbs.length() > 3) {
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 2) + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cjlb like '" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cjlb='" + cjlb + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
        }

        String tabId = "";
        if (data.containsKey("tabId") && data.getString("tabId").length() > 0) {
            tabId = data.getString("tabId");
        }

        if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
            String gmsfhm = data.getString("gmsfhm").replace("-", "").replace(":", "").replace(" ", "");
            ;
            sql = sql + " and gmsfhm='" + gmsfhm + "' ";
        }
        //统计层级
        int level = -1;
//        if (data.containsKey("level") && data.getString("level").length() > 0) {
//            level = data.getInteger("level");
//            if (level > 0) {
//                sql = sql + " and level = '" + level + "' ";
//            } else {
//                level = -1;
//            }
//        }
        //警情标签
        if (data.containsKey("jqbz") && data.getString("jqbz").length() > 2) {
            String lbs = data.getString("jqbz");
            System.out.println(lbs);

            String plbs = "";
            String dlbs = "";

            List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
            for (int i = 0; i < lbss.size(); i++) {
                String lb = lbss.get(i);
                int lbt = RIUtil.dicts.get(lb).getIntValue("type");
                if (lbt == 4) {
                    if (lb.contains(":") || lb.contains("-")){
                        lb = lb.replaceAll("[:-]", " +");
                    }
                    plbs = plbs + "+" + lb + " ";
                    //plbs = plbs + lb + " ";
                } else {
                    if (lb.contains(":") || lb.contains("-")){
                        lb = lb.replaceAll("[:-]", " +");
                    }
                    dlbs = dlbs + "+" + lb + " ";
                    //dlbs = dlbs + lb + " ";
                }
//                if (lbt == 4) {
//                    plbs = plbs + lb + " ";
//                } else {
//                    dlbs = dlbs + lb + " ";
//                }
            }

            String lbsql = "";
            if (dlbs.length() > 2) {
                lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode ))  ";
            }


            if (plbs.length() > 2) {
                if (lbsql.contains("MATCH")) {
                    lbsql = lbsql + " or ";
                }
                lbsql = lbsql + " ( MATCH(sj.personMs) AGAINST ('" + plbs + "' in boolean mode ) ) ";
            }

            if (lbsql.length() > 2) {
                sql = sql + " and (" + lbsql + ") ";
            }

        }
        //标注公式
        if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
            String bzgs = data.getString("bzgs");
            List<String> gss = RIUtil.StringToArrayList(bzgs);
            //String s = BzgsUtil.convertBzCondition(gss);
            gss = BzgsUtil.fullBzgsList(gss);

            String s = "";
            for (int i = 0; i < gss.size(); i++) {
                String g = gss.get(i);

                if (g.equals("(") || g.equals(")") || g.equals("AND") || g.equals("NOT") || g.equals("OR")) {

                    s = s + " " + g + " ";

                } else {
                    if (g.contains(":") || g.contains("-")){
                        g = g.replaceAll("[:-]", " +");
                    }
                    g = "+" + g;
                    s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
                }


            }

            if (s.length() > 3) {
                s = s.replace("NOT", "!=");
                System.out.println(s);

                sql = sql + " and (" + s + ")";
            }

        }


        String sqls = "select sj.jjbh from wjsc_jq_sjxx sj LEFT JOIN wjsc_jq_jjxx jj ON sj.jjbh = jj.jjbh  LEFT JOIN wjsc_jq_cjxx cj on sj.jjbh=cj.jjbh where 1=1 " + sql + " " +
                "and gmsfhm!='' and (sj.personM is not null and sj.personM!='') ";
        log.warn(sqls);

        String jjbh = RIUtil.GetStringFListSql(sqls, "jjbh", jdbcTemplate);
        return jjbh;

    }

    private void doSjry(String gmsfhm) {

        log.warn("-----------------doSjry------------------");
        long start = System.currentTimeMillis();

        try {


            String sql = "select jjbh from wjsc_jq_sjxx where gmsfhm='" + gmsfhm + "'";
            String jjbhs = RIUtil.GetStringFListSql(sql, "jjbh", jdbcTemplate);
            System.out.println(jjbhs);

            if (jjbhs.endsWith(",")) {
                jjbhs = jjbhs.substring(0, jjbhs.length() - 1);
            }

            jjbhs = jjbhs.replace("|", ",");
            jjbhs = jjbhs.replace(",", "','");

            sql = "select gmsfhm from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm!='" + gmsfhm + "' ";
            System.out.println(sql);
            String tjrys = RIUtil.GetStringFListSql(sql, "gmsfhm", jdbcTemplate);


            sql = "select sjlb,personM from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm='" + gmsfhm + "'";
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

            JSONArray ret = RIUtil.ListMap2jsa(list);

            String sjlbs = "";
            List<String> personMs = new ArrayList<>();
            for (int a = 0; a < ret.size(); a++) {
                JSONObject one = ret.getJSONObject(a);
                String sjlb = one.getString("sjlb");
                sjlbs = sjlbs + sjlb + ",";
                String personM = one.getString("personM");

                List<String> pms = RIUtil.HashToList(RIUtil.StringToList(personM));
                personMs.addAll(pms);

            }

            String cjlbs = "";
            String cjdws = "";
            String cjjgs = "";
            String bjlxs = "";
            List<String> addressMs = new ArrayList<>();
            List<String> reasonMs = new ArrayList<>();
            List<String> resultMs = new ArrayList<>();
            List<String> toolMs = new ArrayList<>();
            String timeMs = "";

            sql = "select jjbh,cjlb,addressM,reasonM,resultM,toolM,timeM,cjdw,cjjg from wjsc_jq_cjxx where" + " jjbh " +
                    "in" + " ('" + jjbhs + "') ";
            list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

            ret = RIUtil.ListMap2jsa(list);

            for (int a = 0; a < ret.size(); a++) {
                JSONObject one = ret.getJSONObject(a);

                String cjlb = one.getString("cjlb");
                if (cjlb != null) {
                    cjlbs = cjlbs + cjlb + ",";
                }
                String cjjg = one.getString("cjjg");
                if (cjjg != null) {
                    cjjgs = cjjgs + cjjg + ",";
                }

                String cjdw = one.getString("cjdw");
                if (cjdw != null) {
                    cjdws = cjdws + cjdw + ",";
                }
                sql = "select bjlx from wjsc_jq_jjxx where jjbh in ('" + jjbhs + "')";
                bjlxs = RIUtil.GetStringFListSql(sql, "bjlx", jdbcTemplate);

                List<String> adMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("addressM")));
                addressMs.addAll(adMs);

                List<String> reMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("reasonM")));
                reasonMs.addAll(reMs);

                List<String> resMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("resultM")));
                resultMs.addAll(resMs);

                List<String> toMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("toolM")));
                toolMs.addAll(toMs);

                String timeM = one.getString("timeM");

                timeMs = timeMs + timeM + ",";


            }

            jjbhs = jjbhs.replace("'", "");
            sql = "update jq_person set jjbhs='" + jjbhs + "', sjlbs='" + sjlbs + "'," + "personMs='" + personMs +
                    "',addressMs='" + addressMs + "'," + "reasonMs='" + reasonMs + "'," + "resultMs='" + resultMs +
                    "'," + "toolMs='" + toolMs + "'," + "timeMs='" + timeMs + "',cjlbs='" + cjlbs + "',cjdws='" + cjdws + "'," +
                    "cjjgs='" + cjjgs + "',tjrys='" + tjrys + "',bjlxs='" + bjlxs + "'"

                    + " where gmsfhm='" + gmsfhm + "'";
            System.out.println(sql);
            jdbcTemplate.update(sql);

            log.warn(System.currentTimeMillis() - start + "-->");
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
            log.error(Lib.getTrace(e));
        }


    }


    private RespSJRY relaRKInfo(JSONObject one) {

        RespSJRY rk = new Gson().fromJson(String.valueOf(one), RespSJRY.class);
        try {
            String xb = rk.getXbdm();
            if (xb.equals("1")) {
                rk.setXbdm("男");
            } else {
                rk.setXbdm("女");
            }
        } catch (Exception ex) {
        }
        try {
            String rylb = rk.getRkgllbdm();
            String RHYZBS = one.getString("rhyzbs");
            if (rylb.equals("11")) {
                if (RHYZBS.equals("0")) {
                    rk.setRkgllbdm("户籍人口：寄住人口");
                } else {
                    rk.setRkgllbdm("户籍人口：人户一致");
                }

            } else {
                rk.setRkgllbdm("流动人口");
            }
        } catch (Exception ex) {

        }

        String mz = "";
        try {
            mz = rk.getMzdm();
            mz = RIUtil.mzs.get("127-" + mz).getString("dict_name");
        } catch (Exception ex) {
        }
        rk.setMzdm(mz);

        String whcd = rk.getWhcd();
        try {
            whcd = RIUtil.dicts.get("78-" + whcd).getString("dict_name");
        } catch (Exception ex) {
        }
        rk.setWhcd(whcd);

        String hyzk = rk.getHyzk();
        try {
            hyzk = RIUtil.dicts.get("79-" + hyzk).getString("dict_name");
        } catch (Exception ex) {
        }
        rk.setHyzk(hyzk);


        return rk;
    }

    // 涉警人员警情列表
    @PostMapping("/list_jq_sjry")
    @ApiOperation(value = "涉警人员警情列表")

    public R<List<RespSjryJqList>> list(@Valid @RequestBody ReqSjryQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();

        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        return searchJQList(data);
        //return R.ok();
    }

    // 涉警人员警情列表
    @PostMapping("/list_jq_sjry_exp")
    @ApiOperation(value = "涉警人员警情列表")

    public void list(@Valid @RequestBody ReqSjryQuery params, HttpServletResponse resp) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();

        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);


        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("unit", u.getString("organization_id"));
        searchJQListExp(data, resp);
        //return R.ok();
    }

    private void searchJQListExp(JSONObject data, HttpServletResponse resp) {
        R<List<RespSjryJqList>> rets = searchJQList(data);
        List<RespSjryJqList> ret = rets.getData();
        JSONArray dets = new JSONArray();
        Gson gson = new Gson();
        for (int i = 0; i < ret.size(); i++) {
            RespSjryJqList d = ret.get(i);
            JSONObject o = JSONObject.parseObject(gson.toJson(d));
            dets.add(o);

        }
        String heads = "警情编号,涉警人员类别,接警时间,报警类型,处警类别,处警结果,报警人,报警电话,处警单位,简要警情";
        String keys = "jjbh,sjlb,bjdhsj_time,cjlb,cjjg,bjr,lxdh,cjdwmc,cljgnr";


        String fileName = ExportTables(dets, heads, keys, "jq");
        resp = GetResp(fileName, resp);

    }

    private HttpServletResponse GetResp(String fileName, HttpServletResponse response) {
        //fileName = "./files/2025/01/jq_20250109164701.xlsx";
        try {
            File file = new File(fileName);
            System.out.println(file.exists());
            long lens = file.length();
            //  response.setContentType("multipart/form-data");
            //   response.setContentLength(lens);


            String fnames[] = fileName.split("/");
            log.warn(fileName);
            String pfile = fnames[fnames.length - 1];
            log.warn(pfile);


            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
//            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Content-Disposition", "attachment; filename=" + pfile);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");


            InputStream dis = new FileInputStream(fileName);
            // OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            // OutputStreamWriter osw=new OutputStreamWriter(outputStream,"gbk");
            // response.setCharacterEncoding("gbk");
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = dis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            // while((len = dis.read()) != -1) {
            // osw.write(len);
            // }
            dis.close();
            // osw.close();
            outputStream.close();
            log.warn("success-->");
        } catch (Exception e) {
            log.error(Lib.getTrace(e));
        }
        return response;

    }

    private String ExportTables(JSONArray datas, String head, String keys, String name) {

        String path = "";
        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(upload_path + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            exporthelper = new ExportXlsxHelper();
            path = upload_path + filePath + FileName;
            exporthelper.init(path);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            return path;

        } catch (Exception e) {
            log.error(Lib.getTrace(e));
            return "";
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }

    }

    public R<List<RespSjryJqList>> searchJQList(JSONObject data) {
        JSONObject back = new JSONObject();
        String sql = "";
        int page = 1;
        int limit = 20;

        try {

            String gmsfhm = "";
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                gmsfhm = data.getString("gmsfhm");
            }
            if (data.containsKey("sta_cols") && data.getString("sta_cols").length() > 0) {
                JSONObject cosl = data.getJSONObject("sta_cols");
                cosl.put("gmsfhm", gmsfhm);
                String jjbhs = GetJjbhsFromCols(cosl);
                //jjbhs = jjbhs.replace(",", "','");
                data.put("jjbhs", jjbhs + ",");
            } else {
                //涉警人员身份证

                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm = '" + gmsfhm + "') ";

            }
            if (data.containsKey("cjlb") && data.getString("cjlb") != null && data.getString("cjlb").length() > 0) {
                String cjlb = data.getString("cjlb");
                sql = sql + " and cjlb='" + cjlb + "' ";
            }

            if (data.containsKey("jjbhs") && data.getString("jjbhs") != null && data.getString("jjbhs").length() > 0) {
                String jjbhs = data.getString("jjbhs");
                if (jjbhs.length() > 3 && jjbhs.endsWith(",")) {
                    jjbhs = jjbhs.substring(0, jjbhs.length() - 1);
                    jjbhs = jjbhs.replace(",", "','");
                }
                sql = sql + " and cj.jjbh in('" + jjbhs + "') ";
            }
            if (data.containsKey("cjdw") && data.getString("cjdw") != null && data.getString("cjdw").length() > 0) {
                String cjdw = data.getString("cjdw");
                sql = sql + " and cjdw='" + cjdw + "' ";
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }


            String sqls = "select cj.jjbh,bjdhsj_time ,bjlx,cjlb,cjjg,bjr,lxdh,cjdwmc ,cljgnr from " + "wjsc_jq_cjxx " +
                    "cj "
                    + "LEFT JOIN" + " wjsc_jq_jjxx jj on cj.jjbh=jj.jjbh where 1=1 " + sql + " order by bjdhsj_time " + "desc ";
            log.warn(sqls);


            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespSjryJqList> d = RealJQINfoList(ret, gmsfhm);
                sqls = "select count(cj.jjbh) as count from wjsc_jq_cjxx cj" + " LEFT JOIN" + " wjsc_jq_jjxx jj on " + "cj" + ".jjbh=jj.jjbh where 1=1 " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }


    }


    private List<RespSjryJqList> RealJQINfoList(JSONArray ret, String gmsfhm) {
        List<RespSjryJqList> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String jjbh = one.getString("jjbh");
            String sql = "select sjlb from wjsc_jq_sjxx where gmsfhm='" + gmsfhm + "' and jjbh='" + jjbh + "' limit 1 ";
            String sjlb = jdbcTemplate.queryForObject(sql, String.class);
            one.put("sjlb", sjlb);

            if (one.containsKey("sjlb") && one.getString("sjlb") != null && one.getString("sjlb").length() > 0) {
                try {
                    one.put("sjlb", RIUtil.dicts.get("55-" + one.getString("sjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjdhsj_time") && one.getString("bjdhsj_time") != null && one.getString("bjdhsj_time").length() > 0) {
                try {
                    one.put("bjdhsj_time", RIUtil.get_Time(one.getString("bjdhsj_time")));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjlx") && one.getString("bjlx") != null && one.getString("bjlx").length() > 0) {
                try {
                    one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjlb") && one.getString("cjlb") != null && one.getString("cjlb").length() > 0) {
                try {
                    one.put("cjlb", RIUtil.dicts.get("51-" + one.getString("cjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjjg") && one.getString("cjjg") != null && one.getString("cjjg").length() > 0) {
                try {
                    one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            RespSjryJqList dd = gson.fromJson(String.valueOf(one), RespSjryJqList.class);
            back.add(dd);
        }
        return back;
    }


    // 涉警人员标签
    @PostMapping("/list_sjry_bq")
    @ApiOperation(value = "涉警人员标签")

    public R<RespSjryBq> getSjryBq(@Valid @RequestBody ReqSjryQuery params) throws Exception {
        log.warn(params.toString());
        RespSjryBq dd = new RespSjryBq();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        String gmsfhm = data.getString("gmsfhm");

        String sql = "";
        JSONObject one = new JSONObject();
        if (data.containsKey("sta_cols") && data.getString("sta_cols").length() > 0) {
            System.out.println(data.getString("sta_cols"));
            JSONObject cosl = data.getJSONObject("sta_cols");
            if (cosl.containsKey("page")) {

            }
            cosl.put("gmsfhm", gmsfhm);
            String jjbhs = GetJjbhsFromCols(cosl);
            jjbhs = jjbhs.replace(",", "','");
            one = staSjDet(jjbhs, gmsfhm);
        } else {
            //实有人口数据
            doSjry(gmsfhm);
            sql = "select * from jq_person where gmsfhm='" + gmsfhm + "'";

            // String sql = "select * from jq_person where gmsfhm='" + gmsfhm + "'";

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 5);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);

                one = ret.getJSONObject(0);
            }
        }
        JSONObject opt = new JSONObject();
        opt.put("gmsfhm", gmsfhm);
        try {

            JSONArray adms = RIUtil.GetDictCountsOpts(one.getString("addressMs"), "bzbq", 1, opt.toString());


            one.put("_addressM", adms);
        } catch (Exception ex) {
            one.put("_addressM", new JSONArray());
        }

        try {
            //one.put("_resultM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("resultMs"))));
            one.put("_resultM", RIUtil.GetDictCountsOpts(one.getString("resultMs"), "bzbq", 1, opt.toString()));

        } catch (Exception ex) {
            one.put("_resultM", new JSONArray());
        }
        try {
            //one.put("_reasonM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("reasonMs"))));
            one.put("_reasonM", RIUtil.GetDictCountsOpts(one.getString("reasonMs"), "bzbq", 1, opt.toString()));
        } catch (Exception ex) {
            one.put("_reasonM", new JSONArray());
        }
        try {
            //one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("personMs"))));
            one.put("_personM", RIUtil.GetDictCountsOpts(one.getString("personMs"), "bzbq", 1, opt.toString()));
        } catch (Exception ex) {
            one.put("_personM", new JSONArray());
        }
        try {
            // one.put("_toolM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("toolMs"))));
            one.put("_toolM", RIUtil.GetDictCountsOpts(one.getString("toolMs"), "bzbq", 1, opt.toString()));
        } catch (Exception ex) {
            one.put("_toolM", new JSONArray());
        }

        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            one.put("_timeM", RIUtil.GetDictCountsOpts(one.getString("timeMs"), "bzbq", 1, opt.toString()));
        } catch (Exception ex) {
            one.put("_timeM", new JSONArray());
        }

        //处警类别

        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            String ccjjlbs[] = one.getString("cjlbs").split(",");
            String lbs = "";
            for (int i = 0; i < ccjjlbs.length; i++) {
                String lb = ccjjlbs[i];
                if (lb.length() == 6) {
                    lbs = lbs + "50-" + lb + ",";
                } else {
                    lbs = lbs + "51-" + lb + ",";
                }

            }
            opt = new JSONObject();
            opt.put("gmsfhm", gmsfhm);
            one.put("cjlbs", RIUtil.GetDictCountsOpts(lbs, "cjlb", 0, opt.toString()));
        } catch (Exception ex) {
            one.put("cjlbs", new JSONArray());
        }
        //处警单位
        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            String ccjjdwss[] = one.getString("cjdws").split(",");
            String fjs = "";
            String pcss = "";
            for (int i = 0; i < ccjjdwss.length; i++) {
                String lb = ccjjdwss[i];
                String f = lb.substring(0, 6) + "000000";
                String p = lb.substring(0, 8) + "0000";
                fjs = fjs + f + ",";
                pcss = pcss + p + ",";

            }
            opt = new JSONObject();
            opt.put("gmsfhm", gmsfhm);
            one.put("sjfj", RIUtil.GetDictCountsOpts(fjs, "jjdw", 1, opt.toString()));
            one.put("cjdws", RIUtil.GetDictCountsOpts(pcss, "jjdw", 1, opt.toString()));
        } catch (Exception ex) {
            one.put("cjdws", new JSONArray());
            one.put("sjfj", new JSONArray());
        }

        //rylbs	涉警人员类别
        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            String ccjjlbs[] = one.getString("sjlbs").split(",");
            String lbs = "";
            for (int i = 0; i < ccjjlbs.length; i++) {
                String lb = ccjjlbs[i];

                lbs = lbs + "55-" + lb + ",";


            }
            opt = new JSONObject();
            opt.put("gmsfhm", gmsfhm);
            one.put("rylbs", RIUtil.GetDictCountsOpts(lbs, "sjlb", 0, opt.toString()));
            System.out.println(one.get("rylbs"));
        } catch (Exception ex) {
            one.put("rylbs", new JSONArray());
        }
        int week = 0;
        String weekBhs = "";
        int month = 0;
        String mbhs = "";
        int month3 = 0;
        String m3bhs = "";
        int month6 = 0;
        String m6bhs = "";
        int year = 0;
        String ybhs = "";
        int year2 = 0;
        String y2hbs = "";
        int total = 0;
        String thbhs = "";
        int tjrs = 0;


        String jjbhs[] = one.getString("jjbhs").replace("\\|", ",").split(",");
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        int y2 = Integer.parseInt(RIUtil.GetNextDate(today, -365 * 2).replace("-", ""));
        //       System.out.println(y2);
        int y1 = Integer.parseInt(RIUtil.GetNextDate(today, -365).replace("-", ""));
        ;
        //     System.out.println(y1);
        int m6 = Integer.parseInt(RIUtil.GetNextDate(today, -180).replace("-", ""));
        ;
        //     System.out.println(m6);
        int m3 = Integer.parseInt(RIUtil.GetNextDate(today, -90).replace("-", ""));
        ;
        //     System.out.println(m3);
        int m1 = Integer.parseInt(RIUtil.GetNextDate(today, -30).replace("-", ""));
        ;
        //    System.out.println(m1);
        int w = Integer.parseInt(RIUtil.GetNextDate(today, -7).replace("-", ""));
        ;
        //    System.out.println(w);
        for (int i = 0; i < jjbhs.length; i++) {
            String bh = jjbhs[i];
            if (bh.length() > 5) {
                total++;
                thbhs = thbhs + bh + ",";
            }
            //J3204125725022480017
            String jqdate = "20" + bh.substring(9, 15);
            int jd = Integer.parseInt(jqdate);
            //  System.out.println(jqdate);
            if (jd > y2) {
                year2++;
                y2hbs = y2hbs + bh + ",";
            }
            if (jd > y1) {
                year++;
                ybhs = ybhs + bh + ",";
            }
            if (jd > m6) {
                month6++;
                m6bhs = m6bhs + bh + ",";
            }
            if (jd > m3) {
                month3++;
            }
            m3bhs = m3bhs + bh + ",";
            if (jd > m1) {
                month++;
                mbhs = mbhs + bh + ",";
            }
            if (jd > w) {
                week++;
                weekBhs = weekBhs + bh + ",";
            }


        }


        //week	近一周涉警数
        JSONObject d = new JSONObject();
        d.put("count", week);
        opt = new JSONObject();
        opt.put("jjbh", weekBhs);
        d.put("opts", opt);
        one.put("week", d);
//month	近一月涉警数
        d = new JSONObject();
        d.put("count", month);
        opt = new JSONObject();
        opt.put("jjbh", mbhs);
        d.put("opts", opt);
        one.put("month", d);
//month3	近3个月涉警数
        d = new JSONObject();
        d.put("count", month3);
        opt = new JSONObject();
        opt.put("jjbh", m3bhs);
        d.put("opts", opt);
        one.put("month3", d);
//month6	近半年涉警数
        d = new JSONObject();
        d.put("count", month6);
        opt = new JSONObject();
        opt.put("jjbh", m6bhs);
        d.put("opts", opt);
        one.put("month6", d);
//year	近一年涉警数
        d = new JSONObject();
        d.put("count", year);
        opt = new JSONObject();
        opt.put("jjbh", ybhs);
        d.put("opts", opt);
        one.put("year", d);
//year2	近2年涉警数
        d = new JSONObject();
        d.put("count", year2);
        opt = new JSONObject();
        opt.put("jjbh", y2hbs);
        d.put("opts", opt);
        one.put("year2", d);
//total	历史涉警数
        d = new JSONObject();
        d.put("count", total);
        opt = new JSONObject();
        opt.put("jjbh", thbhs);
        d.put("opts", opt);

        one.put("total", d);
        System.out.println(one);
//tjrs	同警人数
        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            String ccjjlbs[] = one.getString("tjrys").split(",");
            one.put("tjrs", ccjjlbs.length);
        } catch (Exception ex) {
            one.put("tjrs", tjrs);
        }

//bjlxs	报警类型
        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            String ccjjlbs[] = one.getString("bjlxs").split(",");
            String lbs = "";
            for (int i = 0; i < ccjjlbs.length; i++) {
                String lb = ccjjlbs[i].substring(0, 4) + "00";

                lbs = lbs + "51-" + lb + ",";


            }
            opt = new JSONObject();
            opt.put("gmsfhm", gmsfhm);
            one.put("bjlxs", RIUtil.GetDictCountsOpts(lbs, "bjlx", 0, opt.toString()));
            System.out.println(one.get("bjlxs"));
        } catch (Exception ex) {
            one.put("bjlxs", new JSONArray());
        }
//cjjgs	处警结果
        try {
            // one.put("_timeM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("timeMs"))));
            String ccjjlbs[] = one.getString("cjjgs").split(",");
            String lbs = "";
            for (int i = 0; i < ccjjlbs.length; i++) {
                String lb = ccjjlbs[i];

                lbs = lbs + "46-" + lb + ",";


            }
            opt = new JSONObject();
            opt.put("gmsfhm", gmsfhm);
            one.put("cjjgs", RIUtil.GetDictCountsOpts(lbs, "cjjg", 0, opt.toString()));
            System.out.println(one.get("cjjgs"));
        } catch (Exception ex) {
            one.put("cjjgs", new JSONArray());
        }


        dd = gson.fromJson(String.valueOf(one), RespSjryBq.class);
        return R.ok(dd);
    }


    private JSONObject staSjDet(String jjbhs, String gmsfhm) {

        JSONObject back = new JSONObject();
        String sql = "select gmsfhm from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm!='" + gmsfhm + "' ";
        System.out.println(sql);
        String tjrys = RIUtil.GetStringFListSql(sql, "gmsfhm", jdbcTemplate);


        sql = "select sjlb,personM from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm='" + gmsfhm + "'";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

        JSONArray ret = RIUtil.ListMap2jsa(list);

        String sjlbs = "";
        List<String> personMs = new ArrayList<>();
        for (int a = 0; a < ret.size(); a++) {
            JSONObject one = ret.getJSONObject(a);
            String sjlb = one.getString("sjlb");
            sjlbs = sjlbs + sjlb + ",";
            String personM = one.getString("personM");

            List<String> pms = RIUtil.HashToList(RIUtil.StringToList(personM));
            personMs.addAll(pms);

        }

        String cjlbs = "";
        String cjdws = "";
        String cjjgs = "";
        String bjlxs = "";
        List<String> addressMs = new ArrayList<>();
        List<String> reasonMs = new ArrayList<>();
        List<String> resultMs = new ArrayList<>();
        List<String> toolMs = new ArrayList<>();
        String timeMs = "";

        sql = "select jjbh,cjlb,addressM,reasonM,resultM,toolM,timeM,cjdw,cjjg from wjsc_jq_cjxx where" + " jjbh " +
                "in" + " ('" + jjbhs + "') ";
        list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

        ret = RIUtil.ListMap2jsa(list);

        for (int a = 0; a < ret.size(); a++) {
            JSONObject one = ret.getJSONObject(a);

            String cjlb = one.getString("cjlb");
            if (cjlb != null) {
                cjlbs = cjlbs + cjlb + ",";
            }
            String cjjg = one.getString("cjjg");
            if (cjjg != null) {
                cjjgs = cjjgs + cjjg + ",";
            }

            String cjdw = one.getString("cjdw");
            if (cjdw != null) {
                cjdws = cjdws + cjdw + ",";
            }
            sql = "select bjlx from wjsc_jq_jjxx where jjbh in ('" + jjbhs + "')";
            bjlxs = RIUtil.GetStringFListSql(sql, "bjlx", jdbcTemplate);

            List<String> adMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("addressM")));
            addressMs.addAll(adMs);

            List<String> reMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("reasonM")));
            reasonMs.addAll(reMs);

            List<String> resMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("resultM")));
            resultMs.addAll(resMs);

            List<String> toMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("toolM")));
            toolMs.addAll(toMs);

            String timeM = one.getString("timeM");

            timeMs = timeMs + timeM + ",";


        }

        jjbhs = jjbhs.replace("'", "");
        sql = "update jq_person set jjbhs='" + jjbhs + "', sjlbs='" + sjlbs + "'," + "personMs='" + personMs +
                "',addressMs='" + addressMs + "'," + "reasonMs='" + reasonMs + "'," + "resultMs='" + resultMs +
                "'," + "toolMs='" + toolMs + "'," + "timeMs='" + timeMs + "',cjlbs='" + cjlbs + "',cjdws='" + cjdws + "'," +
                "cjjgs='" + cjjgs + "',tjrys='" + tjrys + "',bjlxs='" + bjlxs + "'"

                + " where gmsfhm='" + gmsfhm + "'";


        back.put("jjbhs", jjbhs);
        back.put("sjlbs", sjlbs);
        back.put("personMs", personMs);
        back.put("addressMs", addressMs);
        back.put("reasonMs", reasonMs);
        back.put("resultMs", resultMs);
        back.put("toolMs", toolMs);
        back.put("timeMs", timeMs);
        back.put("cjlbs", cjlbs);
        back.put("cjdws", cjdws);
        back.put("cjjgs", cjjgs);
        back.put("tjrys", tjrys);
        back.put("bjlxs", bjlxs);
        back.put("gmsfhm", gmsfhm);
        return back;
    }


}
