package com.hl.test.controller;


import cn.hutool.core.lang.Pair;
import cn.hutool.core.stream.CollectorUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.hl.common.domain.R;
import com.hl.security.config.sso.cache.SsoCache;

import com.hl.test.Utils.*;
import com.hl.test.Utils.Export.ExportInterface;
import com.hl.test.Utils.Export.ExportXlsxHelper;
import com.hl.test.domain.*;
import com.hl.test.domain.req.*;
import com.hl.test.domain.resp.*;
import com.hl.test.domain.vo.BzbqLazyVo;
import com.hl.test.mapper.*;
import com.hl.test.service.DictService;
import com.hl.test.service.JqbzService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hl.security.User;
import com.hl.security.UserUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "警情标注")
@RequestMapping("/jqbz")
@SuppressWarnings("unchecked")
public class JQBZController {


    @Value("${upload_path}")
    private String upload_path;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DictService dictService;

    @Autowired
    private JqbzService jqbzService;

    @Autowired
    private WjscJqSjxxMapper wjscJqSjxxMapper;

    @Autowired
    private WjscJqCjxxMapper wjscJqCjxxMapper;

    @Autowired
    private WjEventLabelMapper wjEventLabelMapper;

    @Autowired
    private WjJqPersonLabelMapper wjJqPersonLabelMapper;

    @PostMapping("/jqbqDataStatisticsTable")
    @ApiOperation(value = "警情标签数据统计表")
    public R<?> jqbqDataStatisticsTable(@Valid @RequestBody JqbqDataStatisticsTableReq req){
        return R.ok(jqbzService.jqbqDataStatisticsTable2(req));
    }

    @PostMapping("/exportJqbqDataStatisticsTable")
    @ApiOperation(value = "导出警情标签数据统计表")
    public void exportJqbqDataStatisticsTable(@Valid @RequestBody JqbqDataStatisticsTableReq req, HttpServletResponse response) throws IOException{
        jqbzService.exportJqbqDataStatisticsTable(req, response);

    }

    @PostMapping("/getSszrqList")
    @ApiOperation(value = "所属责任区列表")
    public R<?> getSszrqList(@Valid @RequestBody SearchStrReq req) {
        return jqbzService.getSszrqList(req);
    }

    @PostMapping("/query_dw")
    @ApiOperation(value = "查询单位")

    public R<List<RespSYDW>> qdw(@Valid @RequestBody ReqSYDW params) throws Exception {

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));

        return queryDw(data);
    }

    public R<List<RespSYDW>> queryDw(JSONObject data) {
        int limit = 20;
        int page = 1;
        String sql = "";

        String jgbh = "";
        int type = 1;
        if (data.containsKey("JGBH") && data.getString("JGBH").length() > 0) {
            jgbh = data.getString("JGBH");
            if (jgbh.startsWith("JG") || jgbh.startsWith("ZY_JG")) {
                if (jgbh.startsWith("ZY_")) {
                    jgbh = jgbh.replace("ZY_", "");
                    jgbh = jgbh.split("\\|")[0];
                }
//                sql = sql + " and jgbh='" + jgbh + "' ";
                //人员标签里面只传单位，编号不传了
                sql = sql + " and jgbh like '%" + jgbh + "' ";
            } else {
//                sql = sql + " and id='" + jgbh + "'";
                //人员标签里面只传单位，编号不传了
                sql = sql + " and jgbh like '%" + jgbh + "'";
            }

        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getInteger("type");
        }
        if (data.containsKey("DWMC") && data.getString("DWMC").length() > 0) {
            String dwmc = data.getString("DWMC");
            if (type == 2) {
                sql = sql + " and  dz like '%" + dwmc + "%' ";
            } else if (type == 99) {
                sql = sql + " and (dzmc like '%" + dwmc + "%' or dzbm like '%" + dwmc + "%') ";
            } else if (type == 1) {
                sql = sql + " and (dwmc like '%" + dwmc + "%' or sjjymc like '%" + dwmc + "%') ";
            } else {
                sql = sql + " and dict_name like '%" + dwmc + "%'";
            }

        }

        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("limit").length() > 0) {
            page = data.getInteger("page");
        }
        String sqls = "";
        if (type != 98) {

            if (type == 2) {
                sqls =
                        "select dzid as jgbh ,dz as dwmc,'' as sjjymc,dz as dwdzmc from czqj_ybds" + ".address_info" +
                                "@qjjc_ybls where 1=1 " + sql + " offset " + (page - 1) * limit + " rows fetch next " + limit + " " + "rows only";
            } else if (type == 99) {
                sqls = "select id as jgbh ,dzmc as dwmc,dzbm as sjjymc,dzmc as dwdzmc from czqj_ybds" +
                        ".address_fb_info" + "@qjjc_ybls where 1=1 " + sql + " offset " + (page - 1) * limit + " rows"
                        + " " + "fetch " + "next " + limit + " rows only";
            } else {
                sqls = "select jgbh,dwmc,sjjymc,dwdzmc from jwry_dba.czjg_jbxx@qjjc_sydw where 1=1  " + sql + " " +
                        "offset " + (page - 1) * limit + " rows  fetch  next " + limit + " rows only";
            }
            OracleHelper ora = null;
            try {
                ora = new OracleHelper("ora_hl");
                log.warn(sqls);
                List<JSONObject> list = ora.query(sqls);
                if (list.size() > 0) {
                    if (type == 2) {
                        sqls =
                                "select count(dzid) as count from czqj_ybds" + ".address_info" + "@qjjc_ybls where " + "1=1" + " " + sql;
                    } else if (type == 99) {
                        sqls =
                                "select count(id) as count from czqj_ybds" + ".address_fb_info" + "@qjjc_ybls where " + "1=1"
                                        + " " + sql;
                    } else {
                        sqls = "select count(jgbh) as count from jwry_dba.czjg_jbxx@qjjc_sydw where 1=1  " + sql;
                    }
                    int count = ora.query_count(sqls);
                    List<RespSYDW> dws = new ArrayList<>();
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);

                        String dwmc =
                                one.getString("DWMC") + "(" + one.getString("SJJYMC") + ")" + "[" + one.getString(
                                        "DWDZMC") + "]";
                        one.put("DWMC", dwmc);

                        String jg = one.getString("JGBH") + "|" + one.getString("DWMC");
                        one.put("JGBH", jg);
                        RespSYDW d = new Gson().fromJson(String.valueOf(one), RespSYDW.class);
                        dws.add(d);
                    }

                    return R.ok(dws, count);

                } else {
                    return R.ok(new ArrayList<>(), 0);
                }
            } catch (Exception ex) {
                log.error(Lib.getTrace(ex));
                return R.fail(Lib.getTrace(ex));

            } finally {
                ora.close();
            }
        } else {

            sqls = "select id,dict_name,remark from dict where 1=1 " + sql;
            log.warn(sqls);
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespSYDW> dws = new ArrayList<>();
                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.getJSONObject(i);


                    one.put("DWMC", one.getString("dict_name"));
                    String jg = one.getString("id") + "|" + one.getString("dict_name");
                    one.put("JGBH", jg);
                    RespSYDW d = new Gson().fromJson(String.valueOf(one), RespSYDW.class);
                    dws.add(d);
                }
                sqls = "select count(id) as count from dict where 1=1  " + sql + " and isdelete=1 ";
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(dws, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        }


    }

    @PostMapping("/update_dw")
    @ApiOperation(value = "更新单位")

    public R<Object> updw(@Valid @RequestBody ReqUpDw params) throws Exception {

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        try {
            String jjbh = data.getString("jjbh");
            String jgbh = data.getString("jgbh");

            String jg = jgbh.split("\\|")[0];
            String mc = jgbh.split("\\|")[1];
            Pair<String, String> splitDwmcAndDwdz = splitDwmcAndDwdz(mc);
            String dwmc = splitDwmcAndDwdz.getKey();
            String dwdz = splitDwmcAndDwdz.getValue();
//            String m[] = mc.split("\\[");
//            String dwmc = m[0];
//            String dwdz = m[1].replace("]", "");

            String sql =
                    "update wjsc_jq_cjxx set jgbh='" + jg + "',dwmc='" + dwmc + "',dwdz='" + dwdz + "' where " +
                            "jjbh='" + jjbh + "'";
            System.out.println(sql);
            jdbcTemplate.update(sql);
            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }


    }


    @PostMapping("/exp_list")
    @ApiOperation(value = "导出列表")
    public void expList(@Valid @RequestBody ReqJQBZExp params, HttpServletResponse resp) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();

        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        data.put("isExp", 1);
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        JSONArray rets = getJQExpListNew(data);
        //  rets=GetBqSplit(rets);
        String tableName = "jq";
        String keys = data.getString("keys");
        keys = keys.replace("[", "").replace("]", "").replace("\"", "");
        String ks[] = keys.split(",");
        String heads = "";
        for (int k = 0; k < ks.length; k++) {
            String kk = ks[k];
            String n = kk;
            try {
                n = RIUtil.dicts.get(kk).getString("dict_name");
            } catch (Exception ex) {
                log.error(kk);
            }
            heads = heads + n + ",";
        }
        String fileName = ExportTables(rets, heads, keys, tableName);
        resp = GetResp(fileName, resp);

        //return R.ok();
    }


    private HttpServletResponse GetResp(String fileName, HttpServletResponse response) {
        //fileName = "./files/2025/01/jq_20250109164701.xlsx";
        try {
            File file = new File(fileName);
            System.out.println(file.exists());
            long lens = file.length();
            //  response.setContentType("multipart/form-data");
            //   response.setContentLength(lens);


            String fnames[] = fileName.split("/");
            log.warn(fileName);
            String pfile = fnames[fnames.length - 1];
            log.warn(pfile);


            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
//            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Content-Disposition", "attachment; filename=" + pfile);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");


            InputStream dis = new FileInputStream(fileName);
            // OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            // OutputStreamWriter osw=new OutputStreamWriter(outputStream,"gbk");
            // response.setCharacterEncoding("gbk");
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = dis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            // while((len = dis.read()) != -1) {
            // osw.write(len);
            // }
            dis.close();
            // osw.close();
            outputStream.close();
            log.warn("success-->");
        } catch (Exception e) {
            log.error(Lib.getTrace(e));
        }
        return response;

    }

    private JSONArray getJQExpListNew(JSONObject data) {
        try {
            String sql = "";
            int page = 1;
            int limit = 9999;
            //处警类别
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lbs = data.getString("cjlb");
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cj.cjlb like '" + cjlb.replace("000000", "") + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cj.cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cj.cjlb like '%" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cj.cjlb='" + cjlb + "' or ";
                    }
                }
                if (s.endsWith("or ")) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

//处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                String jjbh = data.getString("jjbh");
                if (jjbh.endsWith(",")) {
                    jjbh = jjbh.substring(0, jjbh.length() - 1);
                }
                if (jjbh.startsWith("J") && jjbh.length() == 20) {
                    sql = sql + " and cj.jjbh='" + jjbh + "' ";
                } else if (jjbh.length() > 22 && jjbh.contains(",")) {
                    jjbh = jjbh.substring(0, jjbh.length() - 1);
                    jjbh = jjbh.replace(",", "','");
                    sql = sql + " and cj.jjbh in ('" + jjbh + "') ";
                } else {
                    sql = sql + " and cj.jjbh like '%" + jjbh + "%' ";
                }
            }

//事发场所
            if (data.containsKey("sfcs") && data.getString("sfcs").length() > 0) {

                String lbs = data.getString("sfcs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sfcs = cone.getKey();


                    if (sfcs.contains("-")) {
                        sfcs = sfcs.split("\\-")[1];
                    }
                    if (sfcs.endsWith("00")) {
                        s = s + "  cj.sfcs like '" + sfcs.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.sfcs='" + sfcs + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //发生原因
            if (data.containsKey("fsyy") && data.getString("fsyy").length() > 0) {

                String lbs = data.getString("fsyy");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fsyy = cone.getKey();


                    if (fsyy.contains("-")) {
                        fsyy = fsyy.split("\\-")[1];
                    }
                    if (fsyy.endsWith("00")) {
                        s = s + "  cj.fsyy like '0" + fsyy.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.fsyy='0" + fsyy + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            if (data.containsKey("sszrq") && data.getString("sszrq").length() > 0) {
                String sszrq = data.getString("sszrq");
                int type = 23;
                type = RIUtil.dicts.get(sszrq).getInteger("type");
                if (type == 23) {
                    sql = sql + " and cj.sszrq like '" + sszrq.substring(0, 6) + "%'  ";
                } else if (type == 25 || type == 24) {
                    sql = sql + " and cj.sszrq like '" + sszrq.substring(0, 8) + "%'  ";
                } else {
                    sql = sql + " and cj.sszrq = '" + sszrq + "' ";
                }
            }

            String eventId = data.getString("eventId");
            if (StringUtils.isNotBlank(eventId)) {
                sql = sql + " and cj.eventId = '" + eventId + "' ";
            }
//处理结果内容
            if (data.containsKey("cljgnr") && data.getString("cljgnr").length() > 0) {
                String cljgnr = data.getString("cljgnr");
                sql = sql + " and cj.cljgnr like '%" + cljgnr + "%' ";
            }

//处警详址
            if (data.containsKey("cjxz") && data.getString("cjxz").length() > 0) {
                String cjxz = data.getString("cjxz");
                sql = sql + " and cj.cjxz like '%" + cjxz + "%' ";
            }

//处警结果
            if (data.containsKey("cjjg") && data.getString("cjjg").length() > 0) {
                String lbs = data.getString("cjjg");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjg = cone.getKey();


                    if (cjjg.contains("-")) {
                        cjjg = cjjg.split("\\-")[1];
                    }
                    s = s + "  cj.cjjg='" + cjjg + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //事发时间下限开始
            if (data.containsKey("sfsjxx_start") && data.getString("sfsjxx_start").length() > 0) {
                String sfsjxx_start = data.getString("sfsjxx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time>='" + sfsjxx_start + "' ";
            }

            //事发时间下限结束
            if (data.containsKey("sfsjxx_end") && data.getString("sfsjxx_end").length() > 0) {
                String sfsjxx_end = data.getString("sfsjxx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time<='" + sfsjxx_end + "' ";
            }

            //事发时间上限开始
            if (data.containsKey("sfsjsx_start") && data.getString("sfsjsx_start").length() > 0) {
                String sfsjsx_start = data.getString("sfsjsx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time>='" + sfsjsx_start + "' ";
            }

            //事发时间上限结束
            if (data.containsKey("sfsjsx_end") && data.getString("sfsjsx_end").length() > 0) {
                String sfsjsx_end = data.getString("sfsjsx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time<='" + sfsjsx_end + "' ";
            }

            //处警登记时间开始
            if (data.containsKey("djsj_start") && data.getString("djsj_start").length() > 0) {
                String djsj_start = data.getString("djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time>='" + djsj_start + "' ";
            }

            //处警登记时间结束
            if (data.containsKey("djsj_end") && data.getString("djsj_end").length() > 0) {
                String djsj_end = data.getString("djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time<='" + djsj_end + "' ";
            }

            //处警修改时间开始
            if (data.containsKey("cj_xgsj_start") && data.getString("cj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("cj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time>='" + xgsj_start + "' ";
            }

            //处警修改时间结束
            if (data.containsKey("cj_xgsj_end") && data.getString("cj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("cj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time<='" + xgsj_end + "' ";
            }


            //处警标识
            if (data.containsKey("cjbs") && data.getString("cjbs").length() > 0) {
                String lbs = data.getString("cjbs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjbs = cone.getKey();

                    if (cjbs.contains("-")) {
                        cjbs = cjbs.split("\\-")[1];
                    }
                    s = s + " jj.cjbs='" + cjbs + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警报警人
            if (data.containsKey("bjr") && data.getString("bjr").length() > 0) {
                String bjr = data.getString("bjr");
                sql = sql + " and jj.bjr like '%" + bjr + "%' ";
            }

            //报警类型
            if (data.containsKey("bjlx") && data.getString("bjlx").length() > 0) {
                String lbs = data.getString("bjlx");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjlx = cone.getKey();


                    if (bjlx.contains("-")) {
                        bjlx = bjlx.split("\\-")[1];
                    }
                    if (bjlx.endsWith("0000")) {
                        s = s + " jj.bjlx like '" + bjlx.replace("0000", "") + "%' or ";
                    } else if (bjlx.endsWith("00")) {
                        s = s + "  jj.bjlx like '" + bjlx.replace("00", "") + "%' or ";
                    } else {

                        s = s + " jj.bjlx='" + bjlx + "' or ";
                    }

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警单位
            String unit = "";
            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
                unit = data.getString("jjdw");

            } else {
                unit = data.getString("unit");

            }
            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
            String sq = "";
            for (int i = 0; i < us.size(); i++) {
                String u = us.get(i);
                //todo
                int type = 23;
                try {
                    type = RIUtil.dicts.get(u).getInteger("type");
                } catch (Exception ex) {
                    log.warn(u);
                    try {
                        log.warn(String.valueOf(RIUtil.dicts.get(u)));
                    } catch (Exception e) {

                    }
                }
                if (type == 21 || type == 22 || type == 27) {
                    // sql = sql + " and jj.jjdw like '%" + unit.substring(0, 4) + "%'";
                } else if (type == 23 || type == 24) {
                    //sq = sq + "  jj.jjdw like '" + u.substring(0, 6) + "%' or ";
                } else if (type == 26 || type == 25) {

                    sq = sq + "  jj.jjdw like '" + u.substring(0, 8) + "%' or ";
                } else {
                    sq = sq + "  jj.jjdw='" + u + "' or ";
                }
            }
            if (sq.endsWith("or ")) {
                sq = sq.substring(0, sq.length() - 3);
                sql = sql + " and (" + sq + ") ";
            }


            //接警报警人联系电话
            if (data.containsKey("lxdh") && data.getString("lxdh").length() > 0) {
                String lxdh = data.getString("lxdh");
                sql = sql + " and jj.lxdh like '%" + lxdh + "%' ";
            }

            //报警内容
            if (data.containsKey("bjnr") && data.getString("bjnr").length() > 0) {
                String bjnr = data.getString("bjnr");
                sql = sql + " and jj.bjnr like '%" + bjnr + "%' ";
            }

            //发生地点
            if (data.containsKey("sfdd") && data.getString("sfdd").length() > 0) {
                String sfdd = data.getString("sfdd");
                sql = sql + " and jj.sfdd like '%" + sfdd + "%' ";
            }

            //报警方式
            if (data.containsKey("bjxs") && data.getString("bjxs").length() > 0) {
                String lbs = data.getString("bjxs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjxs = cone.getKey();


                    if (bjxs.contains("-")) {
                        bjxs = bjxs.split("\\-")[1];
                    }
                    s = s + "  jj.bjxs='" + bjxs + "' or ";

                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

            //接警登记时间开始
            if (data.containsKey("jj_djsj_start") && data.getString("jj_djsj_start").length() > 0) {
                String djsj_start = data.getString("jj_djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time>='" + djsj_start + "' ";
            }

            //接警登记时间结束
            if (data.containsKey("jj_djsj_end") && data.getString("jj_djsj_end").length() > 0) {
                String djsj_end = data.getString("jj_djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time<='" + djsj_end + "' ";
            }

            //接警修改时间开始
            if (data.containsKey("jj_xgsj_start") && data.getString("jj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("jj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time>='" + xgsj_start + "' ";
            }

            //接警修改时间结束
            if (data.containsKey("jj_xgsj_end") && data.getString("jj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("jj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time<='" + xgsj_end + "' ";
            }

            //接警日期时间开始
            if (data.containsKey("jjrqsj_start") && data.getString("jjrqsj_start").length() > 0) {
                String jjrqsj_start = data.getString("jjrqsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.bjdhsj_time>='" + jjrqsj_start + "' ";
            }

            //接警日期时间结束
            if (data.containsKey("jjrqsj_end") && data.getString("jjrqsj_end").length() > 0) {
                String jjrqsj_end = data.getString("jjrqsj_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and jj.bjdhsj_time<='" + jjrqsj_end + "' ";
            }

            //涉警人员身份证
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                String gmsfhm = data.getString("gmsfhm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm like '%" + gmsfhm + "%') ";
            }

            //涉警人员姓名
            if (data.containsKey("xm") && data.getString("xm").length() > 0) {
                String xm = data.getString("xm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where xm like '%" + xm + "%') ";
            }

            //涉警类别
            if (data.containsKey("sjlb") && data.getString("sjlb").length() > 0) {
                String lbs = data.getString("sjlb");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sjlb = cone.getKey();
                    if (sjlb.contains("-")) {
                        sjlb = sjlb.split("\\-")[1];
                    }
                    s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where sjlb ='" + sjlb + "') or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //反馈时间

            String fksql = " ";
            if (data.containsKey("fk_time_start") && data.getString("fk_time_start").length() > 0) {
                String fk_time_start = data.getString("fk_time_start");

                fksql = fksql + "and create_time >='" + fk_time_start + "' ";
            }

            if (data.containsKey("fk_time_end") && data.getString("fk_time_end").length() > 0) {
                String fk_time_end = data.getString("fk_time_end");

                fksql = fksql + "and create_time <='" + fk_time_end + "' ";
            }
            if (fksql.length() > 5) {
                fksql = "select jjbh from jq_fk where 1=1 " + fksql;
                sql = sql + " and cj.jjbh in (" + fksql + ")";
            }


            //反馈人
            if (data.containsKey("fk_user") && data.getString("fk_user").length() > 0) {
                String fk_user = data.getString("fk_user");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where fkr ='" + fk_user + "') ";
            }
            //反馈单位
            if (data.containsKey("fk_unit") && data.getString("fk_unit").length() > 0) {
                String fk_unit = data.getString("fk_unit");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where unit ='" + fk_unit + "') ";
            }
            //省厅标签dict:75
            if (data.containsKey("cjjqbq") && data.getString("cjjqbq").length() > 0) {
                String lbs = data.getString("cjjqbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjqbq = cone.getKey();

                    if (cjjqbq.contains("-")) {
                        cjjqbq = cjjqbq.split("\\-")[1];
                    }
                    s = s + "  cjjqbq like '%" + cjjqbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //分局标签dict:76
            if (data.containsKey("fjbq") && data.getString("fjbq").length() > 0) {
                String lbs = data.getString("fjbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fjbq = cone.getKey();

                    if (fjbq.contains("-")) {
                        fjbq = fjbq.split("\\-")[1];
                    }
                    s = s + "  fjbq like '%" + fjbq + "%' or ";
                }

                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            if (data.containsKey("currentSearch") && data.getString("currentSearch").length() > 3) {
                String bzgs = data.getString("currentSearch");
                List<String> gss = RIUtil.StringToArrayList(bzgs);
                String s = BzgsUtil.convertBzCondition(gss);
                if (s.length() > 3) {
                    s = s.replace("NOT", "!=");
                    System.out.println(s);
                    sql = sql + " and (" + s + ")";
                }
            }

            //标注标签dict:10

            if (data.containsKey("bzbq4Sta") && data.getString("bzbq4Sta") != null && data.getString("bzbq4Sta").length() > 3) {
                String lbs = data.getString("bzbq4Sta");
                System.out.println(lbs);
                String tmp = "";
                String plbs = "";
                String dlbs = "";
                String personMConditonSql = "";

                List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                Set<String> personMSet = new HashSet<>();
                int personMCount = 0;
                for (int i = 0; i < lbss.size(); i++) {
                    String lb = lbss.get(i);
                    JSONObject dict = RIUtil.dicts.get(lb);
                    if (dict == null) continue;
                    int lbt = dict.getIntValue("type");
                    if (lbt == 4) {
                        //版本3
                        personMSet.add(lb.trim());
                        personMCount ++;
                        //版本1
//                         if (lb.contains(":") || lb.contains("-")){
//                             lb = lb.replaceAll("[:-]", " +");
//                         }
//                         plbs = plbs + "+" + lb + " ";
                        plbs = plbs + "+\"" + lb + "\" ";
                    } else {
//                         if (lb.contains(":") || lb.contains("-")){
//                             String matchCondition = lb.replaceAll("[:-]", " +");
//                             dlbs = dlbs + "+" + matchCondition + " ";
//                         } else {
//                             dlbs = dlbs + "+" + lb + " ";
//                         }
                        dlbs = dlbs + "+\"" + lb + "\" ";
                    }
                }

                String lbsql = "";
                if (dlbs.length() > 2) {
                    lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode))  ";
                }

                //版本3
                if (personMCount == 1 && plbs.length() > 2) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " and ";
                    }
                    lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                            + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";
                } else if (personMCount > 1) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " and ";
                    }
                    //优化
                    String sqlCondition = " SELECT\n" +
                            "\t\t\tjjbh \n" +
                            "\t\tFROM\n" +
                            "\t\t\t( SELECT jjbh, JSON_ARRAYAGG( label ) AS jsonLabels FROM `wj_jq_person_label` WHERE is_deleted = 0 GROUP BY jjbh ) AS t \n" +
                            "\t\tWHERE ";
                    String labelCondition = personMSet.stream().filter(StringUtils::isNotBlank).map(personLabel -> " JSON_CONTAINS( jsonLabels, '\"" + personLabel + "\"' ) ").collect(Collectors.joining(" and "));
                    lbsql = lbsql + " cj.jjbh in ( " + sqlCondition + labelCondition + " )";
                }

                if (lbsql.length() > 2) {
                    sql = sql + " and (" + lbsql + ") ";
                }

            } else {
                if (data.containsKey("bzbq") && data.getString("bzbq").length() > 3) {
                    String lbs = data.getString("bzbq");
                    System.out.println(lbs);
                    String tmp = "";
                    String plbs = "";
                    String dlbs = "";
                    String personMConditonSql = "";

                    List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                    Set<String> personMSet = new HashSet<>();
                    int personMCount = 0;
                    for (int i = 0; i < lbss.size(); i++) {
                        String lb = lbss.get(i);
                        JSONObject dict = RIUtil.dicts.get(lb);
                        if (dict == null) continue;
                        int lbt = dict.getIntValue("type");
                        if (lbt == 4) {
                            //版本3
                            personMSet.add(lb.trim());
                            personMCount ++;
                            //版本1
//                         if (lb.contains(":") || lb.contains("-")){
//                             lb = lb.replaceAll("[:-]", " +");
//                         }
//                         plbs = plbs + "+" + lb + " ";
                            plbs = plbs + "\"" + lb + "\" ";
                        } else {
//                         if (lb.contains(":") || lb.contains("-")){
//                             String matchCondition = lb.replaceAll("[:-]", " +");
//                             dlbs = dlbs + "+" + matchCondition + " ";
//                         } else {
//                             dlbs = dlbs + "+" + lb + " ";
//                         }
                            dlbs = dlbs + "\"" + lb + "\" ";
                        }
                    }

                    String lbsql = "";
                    if (dlbs.length() > 2) {
                        lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode))  ";
                    }

                    //版本3
                    if (personMCount == 1 && plbs.length() > 2) {
                        if (lbsql.contains("MATCH")) {
                            lbsql = lbsql + " or ";
                        }
                        lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                                + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";
                    } else if (personMCount > 1) {
                        if (lbsql.contains("MATCH")) {
                            lbsql = lbsql + " or ";
                        }
                        //优化
                        String sqlCondition = " SELECT\n" +
                                "\t\t\tjjbh \n" +
                                "\t\tFROM\n" +
                                "\t\t\t( SELECT jjbh, JSON_ARRAYAGG( label ) AS jsonLabels FROM `wj_jq_person_label` WHERE is_deleted = 0 GROUP BY jjbh ) AS t \n" +
                                "\t\tWHERE ";
                        String labelCondition = personMSet.stream().filter(StringUtils::isNotBlank).map(personLabel -> " JSON_CONTAINS( jsonLabels, '\"" + personLabel + "\"' ) ").collect(Collectors.joining(" and "));
                        lbsql = lbsql + "cj.jjbh in ( " + sqlCondition + labelCondition + " )";
                    }

                    if (lbsql.length() > 2) {
                        sql = sql + " and (" + lbsql + ") ";
                    }

                }
            }

            //标注公式
            if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
                String bzgs = data.getString("bzgs");
                List<String> gss = RIUtil.StringToArrayList(bzgs);
//                gss = BzgsUtil.fullBzgsList(gss);
                String s = BzgsUtil.convertBzCondition(gss);
                //StringBuilder s = new StringBuilder("");
//                String s = "";
//                for (int i = 0; i < gss.size(); i++) {
//                    String g = gss.get(i);
//
//                    if (g.equals("(") || g.equals(")") || g.equals("AND") || g.equals("NOT") || g.equals("OR")) {
//
//                        //s.append(" ").append(g).append(" ");
//                        s = s + " " + g + " ";
//
//                    } else {
////                        if ((!s.startsWith("AND ") || !s.startsWith(") ") || !s.startsWith("OR ")) && s.length() > 5) {
////                            s = s + " or ";
////                        }
//
//                        //if ((!s.endsWith("AND ") || !s.endsWith(") ") || !s.endsWith("OR ")) && s.length() > 5) {
//                        if ((!StringUtils.endsWith(s, "AND ") && !StringUtils.endsWith(s, "OR ")) && s.length() > 5) {
//                            s = s + " AND ";
//                        }
//                        int t = RIUtil.dicts.get(g).getIntValue("type");
////                        if (g.contains(":") || g.contains("-")){
////                            g = g.replaceAll("[:-]", " +");
////                        }
////                        g = "+" + g;
////                        if (t != 4) {
////                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
////                        } else {
////                            s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
////                        }
//                        g = "\"" + g + "\"";
//                        if (t != 4) {
//                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                        } else {
//                            s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
//                        }
//                    }
//
//
//                }

                if (s.length() > 3) {
                    s = s.replace("NOT", "!=");
                    System.out.println(s);

                    sql = sql + " and (" + s + ")";
                }

            }
            //标注状态
            if (data.containsKey("bzzt") && data.getString("bzzt").length() > 0) {
                String is_mark = data.getString("bzzt");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //是否标注
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                String is_mark = data.getString("mark");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
                String bz_time_start = data.getString("bz_time_start");
                sql = sql + " and bz_time>='" + bz_time_start + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
                String bz_time_end = data.getString("bz_time_end");
                sql = sql + " and bz_time<='" + bz_time_end + "'  ";
            }
            //标注人
            if (data.containsKey("bzr") && data.getString("bzr").length() > 0) {
                String bzr = data.getString("bzr");
                sql = sql + " and bzrxm like '%" + bzr + "%'  ";
            }


            //是否关注
            if (data.containsKey("is_gz") && data.getString("is_gz").length() > 0) {
                String is_gz = data.getString("is_gz");
                if (is_gz.equals("1")) {
                    sql =
                            sql + " and jj.jjbh in (select jjbh from jq_gz where isdelete=0 and create_user='" + data.getString("opt_user") + "')";
                }

            }


            //审批人
            if (data.containsKey("spr") && data.getString("spr").length() > 0) {
                String spr = data.getString("spr");
                sql = sql + " and sprxm like  '%" + spr + "%'  ";
            }
            //审批结果
            if (data.containsKey("spjg") && data.getString("spjg").length() > 0) {
                String spjg = data.getString("spjg");
                sql = sql + " and spjg =  '" + spjg + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_start") && data.getString("spsj_start").length() > 0) {
                String spsj_start = data.getString("spsj_start");
                sql = sql + " and sp_time>='" + spsj_start + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_end") && data.getString("spsj_end").length() > 0) {
                String spsj_end = data.getString("spsj_end");
                sql = sql + " and sp_time<='" + spsj_end + "'  ";
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getInteger("page") != null && data.getInteger("page") > 0) {
                page = data.getInteger("page");
            }
            int isExp = 0;
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String sqls =
                    "select cj.jjbh,cjbs,bjr,bjlx,jjdwmc,bjdhsj_time,lxdh,bjnr,djdwmc,sfdd,bjxs,jj.djsj_time," +
                            "cjsj_time,"
                            + "cjlb,cjdw,sfcs,ssxxqk,bccljg,sfxq,cljgnr,jqsx,tqqk,cjbh,cjxz,cjdwmc,cjjg,ssxq," +
                            "cjr," + "cjjqbq," + "bzdzmc,qylb,cjxxdd,addressM,personM,resultM,timeM,toolM,reasonM," + "spjg,sp_time," + "spnr," + "bz_time," + "mark,bzrxm,sprxm,fsyy,sszrq from v_jq_cjxx_bz cj " + "LEFT JOIN  " + "wjsc_jq_jjxx jj on cj" + ".jjbh=jj.jjbh  " + "where " + "1=1  " + sql + "  " + "order by " + "bjdhsj_time desc ";
            log.warn(sqls);


            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                JSONArray d = RelaInfoListExp(ret, data.getString("opt_user"), isExp);

                return d;

            } else {
                return new JSONArray();
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return new JSONArray();
        }
    }


    //todo_exp
    private JSONArray getJQExpList(JSONObject data) {
        try {
            String sql = "";
            int page = 1;
            int limit = 9999;
            //处警类别
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lbs = data.getString("cjlb");
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cj.cjlb like '%" + cjlb.replace("000000", "") + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cj.cjlb like '%" + cjlb.replace("0000", "") + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cj.cjlb like '%" + cjlb.replace("00", "") + "%' or ";
                    } else {

                        s = s + "  cj.cjlb='" + cjlb + "' or ";
                    }
                }
                if (s.endsWith("or ")) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

//处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                String jjbh = data.getString("jjbh");
                if (jjbh.startsWith("J") && jjbh.length() == 20) {
                    sql = sql + " and cj.jjbh='" + jjbh + "' ";
                } else {
                    sql = sql + " and cj.jjbh like '%" + jjbh + "%' ";
                }
            }

//事发场所
            if (data.containsKey("sfcs") && data.getString("sfcs").length() > 0) {

                String lbs = data.getString("sfcs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sfcs = cone.getKey();


                    if (sfcs.contains("-")) {
                        sfcs = sfcs.split("\\-")[1];
                    }
                    if (sfcs.endsWith("00")) {
                        s = s + "  cj.sfcs like '%" + sfcs.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.sfcs='" + sfcs + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

//处理结果内容
            if (data.containsKey("cljgnr") && data.getString("cljgnr").length() > 0) {
                String cljgnr = data.getString("cljgnr");
                sql = sql + " and cj.cljgnr like '%" + cljgnr + "%' ";
            }

//处警详址
            if (data.containsKey("cjxz") && data.getString("cjxz").length() > 0) {
                String cjxz = data.getString("cjxz");
                sql = sql + " and cj.cjxz like '%" + cjxz + "%' ";
            }

//处警结果
            if (data.containsKey("cjjg") && data.getString("cjjg").length() > 0) {
                String lbs = data.getString("cjjg");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjg = cone.getKey();


                    if (cjjg.contains("-")) {
                        cjjg = cjjg.split("\\-")[1];
                    }
                    s = s + "  cj.cjjg='" + cjjg + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //事发时间下限开始
            if (data.containsKey("sfsjxx_start") && data.getString("sfsjxx_start").length() > 0) {
                String sfsjxx_start = data.getString("sfsjxx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time>='" + sfsjxx_start + "' ";
            }

            //事发时间下限结束
            if (data.containsKey("sfsjxx_end") && data.getString("sfsjxx_end").length() > 0) {
                String sfsjxx_end = data.getString("sfsjxx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time<='" + sfsjxx_end + "' ";
            }

            //事发时间上限开始
            if (data.containsKey("sfsjsx_start") && data.getString("sfsjsx_start").length() > 0) {
                String sfsjsx_start = data.getString("sfsjsx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time>='" + sfsjsx_start + "' ";
            }

            //事发时间上限结束
            if (data.containsKey("sfsjsx_end") && data.getString("sfsjsx_end").length() > 0) {
                String sfsjsx_end = data.getString("sfsjsx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time<='" + sfsjsx_end + "' ";
            }

            //处警登记时间开始
            if (data.containsKey("djsj_start") && data.getString("djsj_start").length() > 0) {
                String djsj_start = data.getString("djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time>='" + djsj_start + "' ";
            }

            //处警登记时间结束
            if (data.containsKey("djsj_end") && data.getString("djsj_end").length() > 0) {
                String djsj_end = data.getString("djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time<='" + djsj_end + "' ";
            }

            //处警修改时间开始
            if (data.containsKey("cj_xgsj_start") && data.getString("cj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("cj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time>='" + xgsj_start + "' ";
            }

            //处警修改时间结束
            if (data.containsKey("cj_xgsj_end") && data.getString("cj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("cj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time<='" + xgsj_end + "' ";
            }


            //处警标识
            if (data.containsKey("cjbs") && data.getString("cjbs").length() > 0) {
                String lbs = data.getString("cjbs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjbs = cone.getKey();

                    if (cjbs.contains("-")) {
                        cjbs = cjbs.split("\\-")[1];
                    }
                    s = s + " jj.cjbs='" + cjbs + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警报警人
            if (data.containsKey("bjr") && data.getString("bjr").length() > 0) {
                String bjr = data.getString("bjr");
                sql = sql + " and jj.bjr like '%" + bjr + "%' ";
            }

            //报警类型
            if (data.containsKey("bjlx") && data.getString("bjlx").length() > 0) {
                String lbs = data.getString("bjlx");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjlx = cone.getKey();


                    if (bjlx.contains("-")) {
                        bjlx = bjlx.split("\\-")[1];
                    }
                    if (bjlx.endsWith("0000")) {
                        s = s + " jj.bjlx like '%" + bjlx.replace("0000", "") + "%' or ";
                    } else if (bjlx.endsWith("00")) {
                        s = s + "  jj.bjlx like '%" + bjlx.replace("00", "") + "%' or ";
                    } else {

                        s = s + " jj.bjlx='" + bjlx + "' or ";
                    }

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警单位
            String unit = "";
            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
                unit = data.getString("jjdw");

            } else {
                unit = data.getString("unit");

            }
            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
            String sq = "";
            for (int i = 0; i < us.size(); i++) {
                String u = us.get(i);
                int type = 23;
                try {
                    type = RIUtil.dicts.get(u).getInteger("type");
                } catch (Exception ex) {
                    log.warn(u);
                    try {
                        log.warn(String.valueOf(RIUtil.dicts.get(u)));
                    } catch (Exception e) {

                    }
                }
                if (type == 23 || type == 24) {
                    sql = sql + " and jj.jjdw like '%" + u.substring(0, 6) + "%'";
                } else if (type == 25) {
                    sql = sql + " and jj.jjdw='" + u + "' ";
                } else if (type == 26) {

                    sql = sql + " and jj.jjdw like '%" + u.substring(0, 8) + "%'";
                } else {
                    sql = sql + " and jj.jjdw='" + u + "' ";
                }
            }
            //接警报警人联系电话
            if (data.containsKey("lxdh") && data.getString("lxdh").length() > 0) {
                String lxdh = data.getString("lxdh");
                sql = sql + " and jj.lxdh like '%" + lxdh + "%' ";
            }

            //报警内容
            if (data.containsKey("bjnr") && data.getString("bjnr").length() > 0) {
                String bjnr = data.getString("bjnr");
                sql = sql + " and jj.bjnr like '%" + bjnr + "%' ";
            }

            //发生地点
            if (data.containsKey("sfdd") && data.getString("sfdd").length() > 0) {
                String sfdd = data.getString("sfdd");
                sql = sql + " and jj.sfdd like '%" + sfdd + "%' ";
            }

            //报警方式
            if (data.containsKey("bjxs") && data.getString("bjxs").length() > 0) {
                String lbs = data.getString("bjxs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjxs = cone.getKey();


                    if (bjxs.contains("-")) {
                        bjxs = bjxs.split("\\-")[1];
                    }
                    s = s + "  jj.bjxs='" + bjxs + "' or ";

                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

            //接警登记时间开始
            if (data.containsKey("jj_djsj_start") && data.getString("jj_djsj_start").length() > 0) {
                String djsj_start = data.getString("jj_djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time>='" + djsj_start + "' ";
            }

            //接警登记时间结束
            if (data.containsKey("jj_djsj_end") && data.getString("jj_djsj_end").length() > 0) {
                String djsj_end = data.getString("jj_djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time<='" + djsj_end + "' ";
            }

            //接警修改时间开始
            if (data.containsKey("jj_xgsj_start") && data.getString("jj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("jj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time>='" + xgsj_start + "' ";
            }

            //接警修改时间结束
            if (data.containsKey("jj_xgsj_end") && data.getString("jj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("jj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time<='" + xgsj_end + "' ";
            }

            //接警日期时间开始
            if (data.containsKey("jjrqsj_start") && data.getString("jjrqsj_start").length() > 0) {
                String jjrqsj_start = data.getString("jjrqsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.bjdhsj_time>='" + jjrqsj_start + "' ";
            }

            //接警日期时间结束
            if (data.containsKey("jjrqsj_end") && data.getString("jjrqsj_end").length() > 0) {
                String jjrqsj_end = data.getString("jjrqsj_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and jj.bjdhsj_time<='" + jjrqsj_end + "' ";
            }

            //涉警人员身份证
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                String gmsfhm = data.getString("gmsfhm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm like '%" + gmsfhm + "%') ";
            }

            //涉警人员姓名
            if (data.containsKey("xm") && data.getString("xm").length() > 0) {
                String xm = data.getString("xm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where xm like '%" + xm + "%') ";
            }

            //涉警类别
            if (data.containsKey("sjlb") && data.getString("sjlb").length() > 0) {
                String lbs = data.getString("sjlb");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sjlb = cone.getKey();
                    if (sjlb.contains("-")) {
                        sjlb = sjlb.split("\\-")[1];
                    }
                    s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where sjlb ='" + sjlb + "') or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //反馈时间

            String fksql = " ";
            if (data.containsKey("fk_time_start") && data.getString("fk_time_start").length() > 0) {
                String fk_time_start = data.getString("fk_time_start");

                fksql = fksql + "and create_time >='" + fk_time_start + "' ";
            }

            if (data.containsKey("fk_time_end") && data.getString("fk_time_end").length() > 0) {
                String fk_time_end = data.getString("fk_time_end");

                fksql = fksql + "and create_time <='" + fk_time_end + "' ";
            }
            if (fksql.length() > 5) {
                fksql = "select jjbh from jq_fk where 1=1 " + fksql;
                sql = sql + " and cj.jjbh in (" + fksql + ")";
            }


            //反馈人
            if (data.containsKey("fk_user") && data.getString("fk_user").length() > 0) {
                String fk_user = data.getString("fk_user");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where fkr ='" + fk_user + "') ";
            }
            //反馈单位
            if (data.containsKey("fk_unit") && data.getString("fk_unit").length() > 0) {
                String fk_unit = data.getString("fk_unit");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where unit ='" + fk_unit + "') ";
            }
            //省厅标签dict:75
            if (data.containsKey("cjjqbq") && data.getString("cjjqbq").length() > 0) {
                String lbs = data.getString("cjjqbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjqbq = cone.getKey();

                    if (cjjqbq.contains("-")) {
                        cjjqbq = cjjqbq.split("\\-")[1];
                    }
                    s = s + "  cjjqbq like '%" + cjjqbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //分局标签dict:76
            if (data.containsKey("fjbq") && data.getString("fjbq").length() > 0) {
                String lbs = data.getString("fjbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fjbq = cone.getKey();

                    if (fjbq.contains("-")) {
                        fjbq = fjbq.split("\\-")[1];
                    }
                    s = s + "  fjbq like '%" + fjbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //标注标签dict:10
            if (data.containsKey("bzbq") && data.getString("bzbq").length() > 0) {
                String lbs = data.getString("bzbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                String ps = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bzbq = cone.getKey();

                    if (bzbq.contains("-")) {
                        bzbq = bzbq.split("\\-")[1];
                    }
                    int t = RIUtil.dicts.get(bzbq).getInteger("type");
                    if (t != 4) {
                        s = s + "  jqbz like '%" + bzbq + "%' or ";
                    } else {
                        ps = ps + "  personMs like '%" + bzbq + "%' or ";
                    }
                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
                if (ps.length() > 3) {
                    ps = ps.substring(0, ps.length() - 3);
                    sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where  " + ps + ")";
                }
            }

            //标注状态
            if (data.containsKey("bzzt") && data.getString("bzzt").length() > 0) {
                String is_mark = data.getString("bzzt");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //是否标注
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                String is_mark = data.getString("mark");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
                String bz_time_start = data.getString("bz_time_start");
                sql = sql + " and bz_time>='" + bz_time_start + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
                String bz_time_end = data.getString("bz_time_end");
                sql = sql + " and bz_time<='" + bz_time_end + "'  ";
            }
            //标注人
            if (data.containsKey("bzr") && data.getString("bzr").length() > 0) {
                String bzr = data.getString("bzr");
                sql = sql + " and bzrxm  like '%" + bzr + "%'  ";
            }


            //是否关注
            if (data.containsKey("is_gz") && data.getString("is_gz").length() > 0) {
                String is_gz = data.getString("is_gz");
                if (is_gz.equals("1")) {
                    sql =
                            sql + " and jj.jjbh in (select jjbh from jq_gz where isdelete=0 and create_user='" + data.getString("opt_user") + "')";
                }

            }


            //审批人
            if (data.containsKey("spr") && data.getString("spr").length() > 0) {
                String spr = data.getString("spr");
                sql = sql + " and sprxm like  '%" + spr + "%'  ";
            }
            //审批结果
            if (data.containsKey("spjg") && data.getString("spjg").length() > 0) {
                String spjg = data.getString("spjg");
                sql = sql + " and spjg =  '" + spjg + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_start") && data.getString("spsj_start").length() > 0) {
                String spsj_start = data.getString("spsj_start");
                sql = sql + " and sp_time>='" + spsj_start + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_end") && data.getString("spsj_end").length() > 0) {
                String spsj_end = data.getString("spsj_end");
                sql = sql + " and sp_time<='" + spsj_end + "'  ";
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            int isExp = 0;
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String sqls =
                    "select cj.jjbh,cjbs,bjr,bjlx,jjdwmc,bjdhsj_time,lxdh,bjnr,djdwmc,sfdd,bjxs,jj.djsj_time," +
                            "cjsj_time,"
                            + "cjlb,cjdw,sfcs,ssxxqk,bccljg,sfxq,cljgnr,jqsx,tqqk,cjbh,cjxz,cjdwmc,cjjg,ssxq," +
                            "cjr," + "cjjqbq," + "bzdzmc,qylb,cjxxdd,addressM,personM,resultM,timeM,toolM,reasonM," + "spjg,sp_time," + "spnr," + "bz_time," + "mark,bzrxm,sprxm,fsyy from v_jq_cjxx_bz cj " + "LEFT JOIN  " + "wjsc_jq_jjxx jj on cj" + ".jjbh=jj.jjbh  " + "where " + "1=1  " + sql + "  " + "order by " + "bjdhsj_time desc ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                JSONArray d = RelaInfoListExp(ret, data.getString("opt_user"), isExp);

                return d;

            } else {
                return new JSONArray();
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return new JSONArray();
        }
    }


    //---------------------------update----------------------------
    @PostMapping("/update")
    @ApiOperation(value = "更新警情标注")

    public R<Object> update(@Valid @RequestBody ReqJQBZUpdate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        data.put("name", user.getName());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        return updateJQBZ(data);
    }

    @PostMapping("/saveEventLabel")
    @ApiOperation(value = "保存事件标签")
    public R<?> saveEventLabel(@Valid @RequestBody EventLableReq req) {
        int count = wjEventLabelMapper.insert(EventLableReq.convert(req));
        return count == 0 ? R.fail("保存失败") : R.ok("保存成功");
    }

    @PostMapping("/FuzzyQueryEventLabel")
    @ApiOperation(value = "模糊查询事件标签")
    public R<?> FuzzyQueryEventLabel(@Valid @RequestBody EventLableReq req) {
        String eventName = req.getEventName();
        String eventId =req.getEventId();
        if(StringUtils.isEmpty(eventName) && StringUtils.isEmpty(eventId)) return R.ok(Collections.emptyList(), 0);
        Page<WjEventLabel> page = new Page<>(req.getPage(), req.getLimit());
        LambdaQueryWrapper<WjEventLabel> wrapper = new LambdaQueryWrapper<WjEventLabel>()
                .eq(WjEventLabel::getStatus, 1);
        if(StringUtils.isNotEmpty(eventId)) {
            wrapper.eq(WjEventLabel::getId, eventId);
        } else if (StringUtils.isNotEmpty(eventName)) {
            wrapper.like(WjEventLabel::getEventName, eventName);
        }
        Page<WjEventLabel> list = wjEventLabelMapper.selectPage(page, wrapper);
        return R.ok(list.getRecords(), (int)list.getTotal());
    }

    @PostMapping("/updateNew")
    @ApiOperation(value = "更新警情标注")
    public R<?> updateNew(@Valid @RequestBody ReqJQBZUpdate params) throws Exception {
      //  return jqbzService.updateJQBZ(req);
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        data.put("name", user.getName());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        return updateJQBZ1(data);
    }

    @PostMapping("/FuzzyJqbq4Lazy")
    @ApiOperation(value = "对懒加载模糊警情标签")
    public R<?> FuzzyJqbq4Lazy(@Valid @RequestBody FuzzyJqbq4LazyReq req) {
        return jqbzService.FuzzyJqbq4Lazy(req);
    }


    @PostMapping("/updateJQBZPersonNew")
    @ApiOperation(value = "更新人员标注")
    public R<?> updateJQBZPersonNew(@Valid @RequestBody ReqJQBZPerson req) throws Exception {
        return jqbzService.updateJQBZPerson(req);
    }



    private R<Object> updateJQBZ1(JSONObject data) {
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";

            if (data.containsKey("sszrq") && data.getString("sszrq").length() > 0) {
                String sszrq = data.getString("sszrq");
                sql = sql + " sszrq='" + sszrq + "' ,";
            }

            //事件标签
            if (data.containsKey("eventId") && data.getString("eventId").length() > 0) {
                String eventId = data.getString("eventId");
                WjEventLabel wjEventLabel = wjEventLabelMapper.selectOne(Wrappers.<WjEventLabel>lambdaQuery()
                        .nested(el -> el.eq(WjEventLabel::getId, eventId)
                                        .or()
                                        .eq(WjEventLabel::getEventName, eventId)
                        )
                        .eq(WjEventLabel::getStatus, 1));
                if (wjEventLabel != null) sql = sql + " eventId='" + wjEventLabel.getId() + "' , ";
                else {
                    UUID uuid = UUID.randomUUID();
                    wjEventLabelMapper.insert(WjEventLabel
                            .builder()
                            .id(uuid.toString())
                            .eventName(eventId)
                            .status(1)
                            .build()
                    );
                    sql = sql + " eventId='" + uuid + "' , ";
                }
            }
            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return R.fail("缺少jjbh");
            }

            List<WjscJqSjxx> wjscJqSjxxes = wjscJqSjxxMapper.selectList(Wrappers.<WjscJqSjxx>lambdaQuery()
                    .select(WjscJqSjxx::getGmsfhm, WjscJqSjxx::getXm, WjscJqSjxx::getPersonM)
                    .eq(WjscJqSjxx::getJjbh, jjbh)
                    .eq(WjscJqSjxx::getZxbs, 0));
            List<String> unLabelPerson = wjscJqSjxxes.stream().filter(x -> StringUtils.isBlank(x.getPersonM())).map(WjscJqSjxx::getXm).collect(Collectors.toList());
            if (!unLabelPerson.isEmpty()) return R.fail(unLabelPerson.stream().collect(Collectors.joining("、")) + " 人员标签未标注");
            //地址标签
            if (data.containsKey("addressM") && data.getString("addressM").length() > 3) {
                String addressM = data.getString("addressM");


                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(addressM));
                String checkS = checkLevel2Address(addss);
                // addss=removeLsame(addss);
                if (checkS.length() == 0) {

                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        JSONObject det = RIUtil.dicts.get(id);
                        String memo = det.getString("memo");
                        String level = det.getString("static_index");
                        String ffdd = det.getString("father_id");
                        String dict_name = det.getString("dict_name");
                        String remark = det.getString("remark");
                        if (memo.contains("发生部位")) {
                            if(!level.equals("4")) return R.fail("发生部位未选到最后一层");
                            //todo
                            String s = "select dwmc,dwdz,dzid,jgbh,xq from wjsc_jq_cjxx where jjbh='" + jjbh + "'";
                            JSONObject dzs = RIUtil.queryOne(s, jdbcTemplate);
                            if (dzs.containsKey("dwmc") && dzs.getString("dwmc").length() > 0) {
                                String dwmc = dzs.getString("dwmc");
                                String dwdz = dzs.getString("dwdz");
                                String jgbh = dzs.getString("jgbh");
                                String xq = dzs.getString("xq");

                                if (StringUtils.isBlank(jgbh)) {
                                    OracleHelper ora = null;
                                    try {
                                        ora = new OracleHelper("");
                                        String sql4Ora = "select JJBH, JGBH, TYPE, XQ, DZXX_XXZJBH from hl.dsj_jq where jjbh = '" + jjbh + "'";
                                        List<JSONObject> query = ora.query(sql4Ora);
                                        if (!CollectionUtils.isEmpty(query)) {
                                            JSONObject jsonObject = query.get(0);
                                            jgbh = jsonObject.getString("JGBH");
                                            xq = jsonObject.getString("XQ");
                                            String type = jsonObject.getString("TYPE");
                                            String dsdz = jsonObject.getString("DZXX_XXZJBQ");
                                            wjscJqCjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                                                    .set(WjscJqCjxx::getJgbh, jgbh)
                                                    .set(WjscJqCjxx::getDzType, type)
                                                    .set(WjscJqCjxx::getXq, xq)
                                                    .set(WjscJqCjxx::getDsdz, dsdz)
                                                    .eq(WjscJqCjxx::getJjbh, jjbh));
                                        }
                                    } catch (Exception ex) {
                                        log.error(Lib.getTrace(ex));
                                        return R.fail(Lib.getTrace(ex));
                                    } finally {
                                        ora.close();
                                    };
                                }

                                if (xq != null && xq.length() > 2) {
                                    String xqmcSql = "select dmmc from address_dm where dmdm='" + xq + "'";
                                    List<String> results = jdbcTemplate.queryForList(xqmcSql, String.class);
                                    String xqmc = results.isEmpty() ? dwmc:results.get(0);
                                    if (dwmc.contains("幢") || dwmc.contains("号")) {
                                        jgbh = xq;
                                        dwmc = xqmc;
                                        dwdz = xqmc;
                                    }
//                                    if (dwmc.contains("幢")) {
//                                        String[] bh = GetXqld(jgbh, dwmc, xq).split("\\|");
//                                        jgbh = bh[0];
//                                        dwmc = bh[1];
//                                        dwdz = bh[1];
//                                    }
                                }

                                // 这一步把addressM 的编号换了
//                                bzs = bzs + newId + " ";
//
//                                addressM = addressM.replace(id, jgbh);
//                                memo = memo + "-" + dwmc;
//                                String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
//                                        "remark,type,up_time)" + " values('" + jgbh + "','" + dwmc + "','" + id + "'," +
//                                        "'jqbq',"
//                                        + "'5','" + memo + "','" + dwdz + "','3','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "')";
//                                log.warn(ss);
//                                jdbcTemplate.update(ss);

                                String newId = jgbh + "_" + id;
                                bzs = bzs + newId + " ";
                                addressM = addressM.replace(id, newId);
                                memo = memo + "-" + dwmc;
                                String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
                                            "remark,type,up_time)" + " values('" + newId + "','" + dwmc + "','" + id + "'," +
                                        "'jqbq',"
                                        + "'5','" + memo + "','" + dwdz + "','3','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "')";
                                log.warn(ss);
                                jdbcTemplate.update(ss);
                            } else {
                                return R.fail("全警未标注");
                            }
                        } else {
                            JSONArray nexts = RIUtil.GetDictByFather(id);
                            if (nexts.size() > 1) {
                                return R.fail("地址-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                            }
                        }
                        bzs = bzs + id + " ";
                        while (id.length() > 0) {
                            String fid = RIUtil.dicts.get(id).getString("father_id");

                            try {
                                id = RIUtil.dicts.get(fid).getString("id");
                                bzs = bzs + id + " ";
                            } catch (Exception ex) {
                                id = "";
                            }
                        }
                    }
                    System.out.println(bzs);
                    sql = sql + " addressM='" + addressM + "' , ";

                    vals = vals + "'" + addressM + "',";
                } else {
                    return R.fail("\"" + checkS + "\"未标注");
                }
            } else {
                return R.fail("地址标签必选");
            }
            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 0) {
                String personM = data.getString("personM");
                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                String checkS = checkLevel2Person(addss);
                if (checkS.length() == 0) {
                    sql = sql + " personM='" + personM + "' , ";
                    vals = vals + "'" + personM + "',";


                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        bzs = bzs + id + " ";
                        while (id.length() > 0) {
                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");
                                id = RIUtil.jqbqs.get(fid).getString("id");
                                bzs = bzs + id + " ";
                            } catch (Exception ex) {
                                id = "";
                            }

                        }

                    }
                    System.out.println(bzs);
                } else {
                    return R.fail("\"" + checkS + "\"未标注");
                }
            }
            //结果标签
            if (data.containsKey("resultM") && data.getString("resultM").length() > 3) {
                String resultM = data.getString("resultM");
                sql = sql + " resultM='" + resultM + "' , ";
                vals = vals + "'" + resultM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(resultM));
                String checkS = checkLevel2Result(addss);
                if (checkS.length() == 0) {
                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        JSONArray nexts = RIUtil.GetDictByFather(id);
                        if (nexts.size() > 1) {
                            return R.fail("结果-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                        }
                        bzs = bzs + id + " ";
                        while (id.length() > 0) {

                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");
                                id = RIUtil.jqbqs.get(fid).getString("id");
                                bzs = bzs + id + " ";
                            } catch (Exception ex) {
                                id = "";
                            }

                        }

                    }
                    System.out.println(bzs);
                } else {
                    return R.fail(checkS);
                }
            } else {
                return R.fail("结果标签必选");
            }
            //时间标签
            if (data.containsKey("timeM") && data.getString("timeM").length() > 3) {
                String timeM = data.getString("timeM");
                sql = sql + " timeM='" + timeM + "' , ";
                vals = vals + "'" + timeM + "',";
                bzs = bzs + timeM + " ";
            } else {
                vals = vals + "'',";

            }
            //手段标签
            if (data.containsKey("toolM") && data.getString("toolM").length() > 3) {
                String toolM = data.getString("toolM");
                sql = sql + " toolM='" + toolM + "' , ";
                vals = vals + "'" + toolM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(toolM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return R.fail("手段-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层");
                    }
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {


                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            } else {
                return R.fail("手段标签必选");
            }
            //原因标签
            if (data.containsKey("reasonM") && data.getString("reasonM").length() > 3) {
                String reasonM = data.getString("reasonM");
                sql = sql + " reasonM='" + reasonM + "' , ";
                vals = vals + "'" + reasonM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(reasonM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return R.fail("原因-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层");
                    }
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            } else {
                return R.fail("原因标签必选");
            }
            initDict();
// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3
            String sqls =
                    "update wjsc_jq_cjxx set " + sql + " bzzt=2,mark=1,spjg=0,unit='" + data.getString("unit") + "'," + "bzr"
                            + "='" + data.getString("opt_user") + "',bz_time='" + new SimpleDateFormat("yyyy-MM-dd " + "HH:mm:ss").format(new Date()) + "',bzrxm='" + data.getString("name") + "',jqbz='" + bzs + "' where jjbh='" + jjbh + "'";
            log.warn(sqls);
            jdbcTemplate.update(sqls);

            if (vals.length() > 2) {
                vals = vals.substring(0, vals.length() - 1);
            }
            sqls = "insert into jq_bz(bzr,bz_time,his_type,jjbh,addressM,resultM,timeM,toolM," + "reasonM) " +
                    "values('" + data.getString("opt_user") + "'," + "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','1','" + jjbh + "'," + vals + ")";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            sqls = "select gmsfhm from wjsc_jq_sjxx where jjbh='" + jjbh + "'";
            String sfzs = RIUtil.GetStringFListSql(sqls, "gmsfhm", jdbcTemplate);
            RIUtil.sjryxg = RIUtil.sjryxg + sfzs + ",";


            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private R<Object> updateJQBZ(JSONObject data) {
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";

            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return R.fail("缺少jjbh");
            }
            //地址标签
            if (data.containsKey("addressM") && data.getString("addressM").length() > 3) {
                String addressM = data.getString("addressM");


                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(addressM));
                String checkS = checkLevel2Address(addss);
                // addss=removeLsame(addss);
                if (checkS.length() == 0) {

                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        JSONObject det = RIUtil.dicts.get(id);
                        String memo = det.getString("memo");
                        String level = det.getString("static_index");
                        String ffdd = det.getString("father_id");
                        String dict_name = det.getString("dict_name");
                        String remark = det.getString("remark");
                        if (memo.contains("发生部位") && level.equals("5")) {


                            //第五层存到警情里
                            sql = sql + "jgbh='" + id + "',dwmc='" + dict_name + "',dwdz='" + remark + "',";
                            //第五层更换father_id
                            String s = "update dict set father_id='" + ffdd + "' where id='" + id + "'";
                            log.warn(s);
                            jdbcTemplate.update(s);
                        } else if (memo.contains("发生部位") && level.equals("4")) {

                            String s = "select dwmc,dwdz,dzid,jgbh,xq from wjsc_jq_cjxx where jjbh='" + jjbh + "'";
                            JSONObject dzs = RIUtil.queryOne(s, jdbcTemplate);
                            if (dzs.containsKey("dwmc") && dzs.getString("dwmc").length() > 0) {
                                String dwmc = dzs.getString("dwmc");
                                String dwdz = dzs.getString("dwdz");
                                String jgbh = dzs.getString("jgbh");
                                String xq = dzs.getString("xq");
                                if (xq != null && xq.length() > 2) {

                                    if (dwmc.contains("幢")) {
                                        String[] bh = GetXqld(jgbh, dwmc, xq).split("\\|");
                                        jgbh = bh[0];
                                        dwmc = bh[1];
                                        dwdz = bh[1];
                                    }
                                }

                                // 这一步把addressM 的编号换了
                                bzs = bzs + jgbh + " ";
                                if (data.containsKey("is_sp") && "1".equals(data.getString("is_sp"))) {
                                    addressM = addressM.replace(id, jgbh);
                                    memo = memo + "-" + dwmc;
                                    String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
                                            "remark,type,up_time)" + " values('" + jgbh + "','" + dwmc + "','" + id + "'," +
                                            "'jqbq',"
                                            + "'5','" + memo + "','" + dwdz + "','3','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "')";
                                    log.warn(ss);
                                    jdbcTemplate.update(ss);
                                }
                            } else {
                                return R.fail("发生部位未选到最后一层");
                            }
                        } else {
                            JSONArray nexts = RIUtil.GetDictByFather(id);
                            if (nexts.size() > 1) {
                                return R.fail("地址-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                            }
                        }

                        bzs = bzs + id + " ";
                        while (id.length() > 0) {
                            String fid = RIUtil.dicts.get(id).getString("father_id");

                            try {
                                id = RIUtil.dicts.get(fid).getString("id");
                                bzs = bzs + id + " ";
                            } catch (Exception ex) {
                                id = "";
                            }

                        }

                    }
                    System.out.println(bzs);
                    sql = sql + " addressM='" + addressM + "' , ";

                    vals = vals + "'" + addressM + "',";
                } else {
                    return R.fail("\"" + checkS + "\"未标注");
                }
            } else {
                return R.fail("地址标签必选");
            }

            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 0) {
                String personM = data.getString("personM");
                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                String checkS = checkLevel2Person(addss);
                if (checkS.length() == 0) {
                    sql = sql + " personM='" + personM + "' , ";
                    vals = vals + "'" + personM + "',";


                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        bzs = bzs + id + " ";
                        while (id.length() > 0) {
                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");
                                id = RIUtil.jqbqs.get(fid).getString("id");
                                bzs = bzs + id + " ";
                            } catch (Exception ex) {
                                id = "";
                            }
                        }
                    }
                    System.out.println(bzs);
                } else {
                    return R.fail("\"" + checkS + "\"未标注");
                }
            }
            //结果标签
            if (data.containsKey("resultM") && data.getString("resultM").length() > 3) {
                String resultM = data.getString("resultM");
                sql = sql + " resultM='" + resultM + "' , ";
                vals = vals + "'" + resultM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(resultM));
                String checkS = checkLevel2Result(addss);
                if (checkS.length() == 0) {
                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        JSONArray nexts = RIUtil.GetDictByFather(id);
                        if (nexts.size() > 1) {
                            return R.fail("结果-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                        }
                        bzs = bzs + id + " ";
                        while (id.length() > 0) {
                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");
                                id = RIUtil.jqbqs.get(fid).getString("id");
                                bzs = bzs + id + " ";
                            } catch (Exception ex) {
                                id = "";
                            }

                        }

                    }
                    System.out.println(bzs);
                } else {
                    return R.fail(checkS);
                }
            } else {
                return R.fail("结果标签必选");
            }
            //时间标签
            if (data.containsKey("timeM") && data.getString("timeM").length() > 3) {
                String timeM = data.getString("timeM");
                sql = sql + " timeM='" + timeM + "' , ";
                vals = vals + "'" + timeM + "',";
                bzs = bzs + timeM + " ";
            } else {
                vals = vals + "'',";

            }
            //手段标签
            if (data.containsKey("toolM") && data.getString("toolM").length() > 3) {
                String toolM = data.getString("toolM");
                sql = sql + " toolM='" + toolM + "' , ";
                vals = vals + "'" + toolM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(toolM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return R.fail("手段-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层");
                    }
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {


                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            } else {
                return R.fail("手段标签必选");
            }
            //原因标签
            if (data.containsKey("reasonM") && data.getString("reasonM").length() > 3) {
                String reasonM = data.getString("reasonM");
                sql = sql + " reasonM='" + reasonM + "' , ";
                vals = vals + "'" + reasonM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(reasonM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return R.fail("原因-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层");
                    }
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            } else {
                return R.fail("原因标签必选");
            }
            initDict();
// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3
            String sqls =
                    "update wjsc_jq_cjxx set " + sql + " bzzt=2,mark=1,spjg=0,unit='" + data.getString("unit") + "'," + "bzr"
                            + "='" + data.getString("opt_user") + "',bz_time='" + new SimpleDateFormat("yyyy-MM-dd " + "HH:mm:ss").format(new Date()) + "',bzrxm='" + data.getString("name") + "',jqbz='" + bzs + "' where jjbh='" + jjbh + "'";
            log.warn(sqls);
            jdbcTemplate.update(sqls);

            if (vals.length() > 2) {
                vals = vals.substring(0, vals.length() - 1);
            }
            sqls = "insert into jq_bz(bzr,bz_time,his_type,jjbh,addressM,resultM,timeM,toolM," + "reasonM) " +
                    "values('" + data.getString("opt_user") + "'," + "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','1','" + jjbh + "'," + vals + ")";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            sqls = "select gmsfhm from wjsc_jq_sjxx where jjbh='" + jjbh + "'";
            String sfzs = RIUtil.GetStringFListSql(sqls, "gmsfhm", jdbcTemplate);
            RIUtil.sjryxg = RIUtil.sjryxg + sfzs + ",";


            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private String GetXqldNew(String jgbh, String dwmc, String xq) {
        String xqmc = "";
        String sql = "select dmmc from address_dm where dmdm='" + xq + "'";
        xqmc = jdbcTemplate.queryForObject(sql, String.class);

        if (dwmc.contains("幢")) {

            return xq + "|" + xqmc + "幢";
        } else {
            return jgbh + "|" + xqmc;
        }
    }

    private String GetXqld(String jgbh, String dwmc, String xq) {
        String xqmc = "";
        String sql = "select dmmc from address_dm where dmdm='" + xq + "'";
        xqmc = jdbcTemplate.queryForObject(sql, String.class);

        if (dwmc.contains("幢")) {
            String xqld = dwmc.split("\\幢")[0];
            String ld = xqld.replace(xqmc, "");

            String id = xq + "-" + ld;
            return id + "|" + xqld + "幢";
        } else {
            return jgbh + "|" + dwmc;
        }
    }

    @PostMapping("/check_doub")
    @ApiOperation(value = "标签去重（父级）")

    public R<JSONArray> CheckDoubF(@Valid @RequestBody ReqJQBZDou params) throws Exception {
        List<String> addss = params.getBq();
        JSONArray back = removeLsame(addss);
        /*if (back.size() == addss.size()) {
            return R.ok(new JSONArray());
        } else {
            return R.ok(back);
        }*/
        return R.ok(new JSONArray());
    }

    private JSONArray removeLsame(List<String> addss) {
        List<String> back = new ArrayList<>();
        HashMap<String, String> l2 = new HashMap<>();
        for (int i = 0; i < addss.size(); i++) {

            String id = addss.get(i);
            String memo = RIUtil.dicts.get(id).getString("memo");
            int lev = RIUtil.dicts.get(id).getIntValue("static_index");
            if (l2.containsKey(memo)) {
                String[] s = l2.get(memo).split("\\|");
                String level = s[1];
                int l = Integer.parseInt(level);
                String idid = s[0];
                if (lev > l) {
                    String ss = id + "|" + lev;
                    l2.put(memo, ss);
                } else if (lev == l) {

                }
            } else {
                String ss = id + "|" + lev;
                l2.put(memo, ss);

            }

        }
        System.out.println(l2);
        for (Map.Entry<String, String> o : l2.entrySet()) {
            String value[] = o.getValue().split("\\|");
            back.add(value[0]);

        }
        System.out.println(back);
        JSONArray aMs = new JSONArray();
        for (int a = 0; a < back.size(); a++) {
            String id = back.get(a);
            JSONObject aone = RIUtil.jqbqs.get(id);
            String memo = aone.getString("memo");

            aone.remove("dets");

            aMs.add(aone);


        }
        return aMs;
    }

    private String checkLevel2Address(List<String> addss) {
        String checkStr = "发生部位、室内室外、所属商圈、工业园区、";
        int markF = 0;
        for (int i = 0; i < addss.size(); i++) {
            String id = addss.get(i);
            try {
                String memos = RIUtil.dicts.get(id).getString("memo");
                if (memos.contains("发生部位")) {
                    checkStr = checkStr.replace("发生部位、", "");
                }
                if (memos.contains("室内室外")) {
                    checkStr = checkStr.replace("室内室外、", "");
                }
                if (memos.contains("所属商圈")) {
                    checkStr = checkStr.replace("所属商圈、", "");
                }
                if (memos.contains("工业园区")) {
                    checkStr = checkStr.replace("工业园区、", "");
                }
            } catch (Exception ex) {
                log.error(id);
            }
        }
        return checkStr;

    }

    private String checkLevel2Person(List<String> addss) {
        String checkStr = "是否涉酒、人员职业、人员类别、人员年龄、";
        int markF = 0;
        for (int i = 0; i < addss.size(); i++) {
            String id = addss.get(i);

            String memos = RIUtil.dicts.get(id).getString("memo");
            if (memos.contains("是否涉酒")) {
                checkStr = checkStr.replace("是否涉酒、", "");
            }
            if (memos.contains("人员职业")) {
                checkStr = checkStr.replace("人员职业、", "");
            }
            if (memos.contains("人员类别")) {
                checkStr = checkStr.replace("人员类别、", "");
            }
            if (memos.contains("人员年龄")) {
                checkStr = checkStr.replace("人员年龄、", "");
            }
        }
        return checkStr;

    }

    private String checkLevel2Result(List<String> addss) {
        String checkStr = "参与人数、财物损失、人员伤亡、是否调解、警情分流、";
        int markF = 0;
        for (int i = 0; i < addss.size(); i++) {
            String id = addss.get(i);

            String memos = RIUtil.dicts.get(id).getString("memo");
            if (memos.contains("参与人数")) {
                checkStr = checkStr.replace("参与人数、", "");
            }
            if (memos.contains("财物损失")) {
                checkStr = checkStr.replace("财物损失、", "");
            }
            if (memos.contains("人员伤亡")) {
                checkStr = checkStr.replace("人员伤亡、", "");
            }

            if (memos.contains("是否调解")) {
                checkStr = checkStr.replace("是否调解、", "");
            }
            if (memos.contains("警情分流")) {
                checkStr = checkStr.replace("警情分流、", "");
            }
        }
        return checkStr;

    }

    @PostMapping("/update_sp")
    @ApiOperation(value = "更新警情标注审批")

    public R<Object> updatesp(@Valid @RequestBody ReqJQBZSPUpdate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        data.put("name", user.getName());
        return updateJQBZSP(data);
    }

    private R<Object> updateJQBZSP(JSONObject data) {
        try {
            String sql = "";
            String jjbh = "";
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");


            } else {
                return R.fail("缺少jjbh");
            }
            //审批结果:-1未标注 0已标注待审批 1同意 2退回
            // - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3

            int bzzt = 2;
            int mark = 1;
            String spjg = "";
            if (data.containsKey("spjg") && data.getString("spjg").length() > 0) {
                spjg = data.getString("spjg");

                sql = sql + " spjg='" + spjg + "' , ";
                if (spjg.equals("2")) {
                    bzzt = 3;
                    mark = 0;
                } else {
                    bzzt = 1;
                }

            }
            //审批人
            if (data.containsKey("spr") && data.getString("spr").length() > 0) {
                String spr = data.getString("spr");
                sql = sql + " spr='" + spr + "' , ";

            }
            //审批时间

            String sp_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            sql = sql + " sp_time='" + sp_time + "' , ";

            //审批内容
            if (data.containsKey("spnr") && data.getString("spnr").length() > 0) {
                String spnr = data.getString("spnr");
                sql = sql + " spnr='" + spnr + "' , ";

            }

            String sqls =
                    "update wjsc_jq_cjxx set " + sql + " bzzt=" + bzzt + ",mark='" + mark + "',spr='" + data.getString(
                            "opt_user") + "'," + "sp_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "',sprxm='" + data.getString("name") + "' where " + "jjbh='" + jjbh + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            sqls = "select id from jq_bz where jjbh='" + jjbh + "' and his_type!=3 order by bz_time desc limit 1";
            String id = jdbcTemplate.queryForObject(sqls, String.class);

            sqls = "update jq_bz set " + sql + " bzzt=" + bzzt + ",mark='" + mark + "',spr='" + data.getString(
                    "opt_user") + "'," + "sp_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where " + "id='" + id + "'";

            jdbcTemplate.update(sqls);
            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }


    @PostMapping("/update_his")
    @ApiOperation(value = "更新警情标注历史")

    public R<Object> updateHis(@Valid @RequestBody ReqJQBZUpdate_his params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));

        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        return updateJQBZ_his(data);
    }

    private R<Object> updateJQBZ_his(JSONObject data) {
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";

            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return R.fail("缺少jjbh");
            }
            //地址标签
            if (data.containsKey("addressM") && data.getString("addressM").length() > 0) {
                String addressM = data.getString("addressM");
                sql = sql + " addressM='" + addressM + "' , ";

                vals = vals + "'" + addressM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(addressM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }
                    }
                }
                System.out.println(bzs);

            }
            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 0) {
                String personM = data.getString("personM");
                sql = sql + " personM='" + personM + "' , ";
                vals = vals + "'" + personM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }
                }
                System.out.println(bzs);
            }
            //结果标签
            if (data.containsKey("resultM") && data.getString("resultM").length() > 0) {
                String resultM = data.getString("resultM");
                sql = sql + " resultM='" + resultM + "' , ";
                vals = vals + "'" + resultM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(resultM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);
            }
            //时间标签
            if (data.containsKey("timeM") && data.getString("timeM").length() > 0) {
                String timeM = data.getString("timeM");
                sql = sql + " timeM='" + timeM + "' , ";
                vals = vals + "'" + timeM + "',";
                bzs = bzs + timeM + " ";
            } else {
                vals = vals + "'',";

            }
            //手段标签
            if (data.containsKey("toolM") && data.getString("toolM").length() > 0) {
                String toolM = data.getString("toolM");
                sql = sql + " toolM='" + toolM + "' , ";
                vals = vals + "'" + toolM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(toolM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }
            //原因标签
            if (data.containsKey("reasonM") && data.getString("reasonM").length() > 0) {
                String reasonM = data.getString("reasonM");
                sql = sql + " reasonM='" + reasonM + "' , ";
                vals = vals + "'" + reasonM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(reasonM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    bzs = bzs + id + " ";
                    while (id.length() > 0) {
                        try {
                            String fid = RIUtil.jqbqs.get(id).getString("father_id");
                            id = RIUtil.jqbqs.get(fid).getString("id");
                            bzs = bzs + id + " ";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }
// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3
            String create_user = data.getString("create_user");
            String create_time = data.getString("create_time");
            String sqls =
                    "update wjsc_jq_cjxx set " + sql + " bzzt=2,mark=1,spjg=0,unit='" + data.getString("unit") + "'," + "bzr"
                            + "='" + data.getString("create_user") + "',bz_time='" + create_time + "',bzrxm='" + data.getString(
                            "name") + "',jqbz='" + bzs + "' where jjbh='" + jjbh + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);

            if (vals.length() > 2) {
                vals = vals.substring(0, vals.length() - 1);
            }
            sqls =
                    "insert into jq_bz(bzr,bz_time,his_type,jjbh,addressM,personM,resultM,timeM,toolM," + "reasonM) " +
                            "values('" + data.getString("create_user") + "'," + "'" + create_time + "','1','" + jjbh + "'," + vals + ")";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);


            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    @PostMapping("/update_person")
    @ApiOperation(value = "警情标注人员标签")

    public R<Object> updatePerson(@Valid @RequestBody ReqJQBZPerson params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        data.put("name", user.getName());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        data.put("create_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        return updateJQBZ_person(data);
    }

    @PostMapping("/update_person_his")
    @ApiOperation(value = "警情标注人员标签历史")

    public R<Object> updatePersonHis(@Valid @RequestBody ReqJQBZPersonHis params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        log.warn(data.toString());


        return updateJQBZ_person(data);
    }



    @PostMapping("/update_person_new")
    @ApiOperation(value = "警情标注人员标签新")

    public R<Object> updatePersonNew(@Valid @RequestBody ReqJQBZPerson params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        data.put("name", user.getName());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        data.put("create_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        return updateJQBZ_person_new(data);
    }

    private R<Object> updateJQBZ_person_new(JSONObject data) {
        //boolean hasSfz = true;
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";
            String gmsfhm = "";
            String uuid = "";
            String personM = "";
            String jgbh = "";
            String dwmc = "";
            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return R.fail("缺少jjbh");
            }
            if (data.containsKey("uuid") && data.getString("uuid").length() > 0) {
                uuid = data.getString("uuid");
            } else {
                return R.fail("缺少uuid");
            }

            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                String[] ids = data.getString("jgbh").split("\\|");
                if (!ids[0].startsWith("ZY")) {
                    jgbh = "ZY_" + ids[0];
                } else {
                    jgbh = ids[0];
                }
                try {
                    dwmc = ids[1];
                } catch (Exception ex) {
                    log.error(data.getString("jgbh"));
                }
            }
            List<String> pms = new ArrayList<>();
            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 3) {
                personM = data.getString("personM");
                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                String checks = checkLevel2Person(addss);
                if (checks.length() == 0) {
                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        String memo = RIUtil.dicts.get(id).getString("memo");
                        int level = RIUtil.dicts.get(id).getInteger("static_index");
                        if (memo.contains("人员职业")) {
                            if (level != 4) return R.fail("人员-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                            String newId = id;
                            if (dwmc == null || dwmc.length() == 0) {
//                                dwmc = RIUtil.dicts.get(id).getString("dict_name");
//                                jgbh = id;
                            } else if (!dwmc.equals(RIUtil.dicts.get(id).getString("dict_name"))) {
                                Pair<String, String> splitDwmcAndDwdz = splitDwmcAndDwdz(dwmc);
                                dwmc = splitDwmcAndDwdz.getKey();
                                String dwdz = splitDwmcAndDwdz.getValue();
//                                if (dwmc.contains("[")) {
//                                    dwdz = dwmc.split("\\[")[1];
//                                    dwmc = dwmc.split("\\[")[0];
//                                }
                                newId = jgbh + "_" + id;
                                bzs = bzs + newId + " ";
                                memo = memo + "-" + dwmc;

                                String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
                                        "remark,type,up_time)" + " values('" + newId + "','" + dwmc + "','" + id + "'," +
                                        "'jqbq',"
                                        + "'5','" + memo + "','" + dwdz + "','4','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm" + ":ss").format(new Date()) + "')";
                                log.warn(ss);
                                jdbcTemplate.update(ss);
                            }
                            pms.add(newId);
                        } else {
                            JSONArray nexts = RIUtil.GetDictByFather(id);
                            if (nexts.size() > 1) {
                                return R.fail("人员-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                            }
                            pms.add(id);
                        }
//                        if (!bzs.contains(id)) {
//                            bzs = bzs + id + " ";
//                        }
                        bzs = bzs + id + " ";
                        while (id.length() > 0) {
                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");
                                id = RIUtil.jqbqs.get(fid).getString("id");
                                if (!bzs.contains(id)) {
                                    bzs = bzs + id + " ";
                                }
                            } catch (Exception ex) {
                                id = "";
                            }
                        }
                    }
                    System.out.println(bzs);
                } else {
                    return R.fail(checks + "标签未标注");
                }
            }



// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3

            String create_user = data.getString("opt_user");
            String create_time = data.getString("create_time");
            String sqls =
                    "update wjsc_jq_sjxx set personMs='" + bzs + "',personM='" + pms + "',jgbh='" + jgbh + "'," +
                            "dwmc='" + dwmc + "' " ;
            WjscJqSjxx wjscJqSjxx = wjscJqSjxxMapper.selectById(uuid);
            if (wjscJqSjxx != null) {
                if (StringUtils.isEmpty(wjscJqSjxx.getGmsfhm()) && StringUtils.isNotEmpty(data.getString("gmsfhm"))) {
                    gmsfhm = data.getString("gmsfhm");
                    sqls = sqls + ",gmsfhm='" + gmsfhm + "' ";
                }
            }
            sqls = sqls + "where " + "jjbh='" + jjbh + "' and uuid='" + uuid + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);

            if (vals.length() > 2) {
                vals = vals.substring(0, vals.length() - 1);
            }
            sqls =
                    "insert into jq_bz(bzr,bz_time,his_type,jjbh,personM) " + "values('" + data.getString("opt_user") + "'," + "'" + create_time + "','3','" + jjbh + "','" + personM + "')";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            RIUtil.sjryxg = RIUtil.sjryxg + gmsfhm + ",";

            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private Pair<String, String> splitDwmcAndDwdz(String full) {
        if (full == null || !full.contains("[") || !full.contains("]")) {
            return Pair.of(full, "");
        }

        int lastOpen = full.lastIndexOf('[');
        int lastClose = full.lastIndexOf(']');

        if (lastClose < lastOpen) {
            return Pair.of(full, "");
        }

        String dwdz = full.substring(lastOpen + 1, lastClose);
        String dwmc = full.substring(0, lastOpen);

        return Pair.of(dwmc, dwdz);
    }

    private R<Object> updateJQBZ_person(JSONObject data) {
        //boolean hasSfz = true;
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";
            String gmsfhm = "";
            String uuid = "";
            String personM = "";
            String jgbh = "";
            String dwmc = "";
            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return R.fail("缺少jjbh");
            }
//            hasSfz = data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0;
//
//            if (hasSfz) {
//                gmsfhm = data.getString("gmsfhm");
//                sql = sql + " gmsfhm='" + gmsfhm + "' , ";
//            }
            if (data.containsKey("uuid") && data.getString("uuid").length() > 0) {
                uuid = data.getString("uuid");
            } else {
                return R.fail("缺少uuid");
            }
            // 外国人
//            else {
//                return R.fail("缺少gmsfhm");
//            }

            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                String[] ids = data.getString("jgbh").split("\\|");
                if (!jgbh.startsWith("ZY")) {
                    jgbh = "ZY_" + ids[0];
                } else {
                    jgbh = ids[0];
                }
                try {
                    dwmc = ids[1];
                } catch (Exception ex) {
                    log.error(data.getString("jgbh"));
                }

            }

            List<String> pms = new ArrayList<>();
            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 3) {
                personM = data.getString("personM");


                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                String checks = checkLevel2Person(addss);
                if (checks.length() == 0) {

                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        String memo = RIUtil.dicts.get(id).getString("memo");
                        int level = RIUtil.dicts.get(id).getInteger("static_index");
//                        if (dwmc == null || dwmc.length() == 0) {
//                            JSONArray nexts = RIUtil.GetDictByFather(id);
//                      /* if (nexts.size() > 1&&!memo.contains("无业")&&!memo.contains("职业不详")) {
//                            return R.fail(memo+"-未选择到最底层标签");
//                        }*/
//
//                            if (memo.contains("人员职业")) {
//                                dwmc = RIUtil.dicts.get(id).getString("dict_name");
//                                jgbh = id;
//                            }
//                            pms.add(id);
//                        } else {
                            if (memo.contains("人员职业")) {
                                if (level == 4) {
                                    String newId = id;
                                    System.out.println(dwmc);
                                    if (dwmc == null || dwmc.length() == 0) {
                                        dwmc = RIUtil.dicts.get(id).getString("dict_name");
                                        jgbh = id;
                                    } else if (dwmc.contains("[")) {
                                        String dwdz = dwmc.split("\\[")[1];
                                        dwmc = dwmc.split("\\[")[0];
                                        newId = jgbh + ":" + id;
                                        bzs = bzs + newId + " ";
                                        memo = memo + "-" + dwmc;
                                        String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
                                                "remark,type,up_time)" + " values('" + newId + "','" + dwmc + "','" + id + "'," +
                                                "'jqbq',"
                                                + "'5','" + memo + "','" + dwdz + "','4','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm" + ":ss").format(new Date()) + "')";
                                        log.warn(ss);
                                        jdbcTemplate.update(ss);
                                      //  pms.add(id);
                                    }
//                                    else {
////                                    dwmc = RIUtil.dicts.get(id).getString("dict_name");
////                                    jgbh = id;
//                                        //pms.add(id);
//                                    }
                                    pms.add(newId);
                                } else {
                                    return R.fail("人员-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                                }
                            } else {
                                JSONArray nexts = RIUtil.GetDictByFather(id);
                                if (nexts.size() > 1) {
                                    return R.fail("人员-" + RIUtil.dicts.get(id).getString("memo") + "：未选择到最底层标签");
                                }
                                pms.add(id);
                            }
                            //pms.add(id);
//                        }
                        if (!bzs.contains(id)) {
                            bzs = bzs + id + " ";
                        }
                        while (id.length() > 0) {
                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");


                                id = RIUtil.jqbqs.get(fid).getString("id");
                                if (!bzs.contains(id)) {
                                    bzs = bzs + id + " ";
                                }
                            } catch (Exception ex) {
                                id = "";
                            }

                        }

                    }
                    System.out.println(bzs);
                } else {
                    return R.fail(checks + "标签未标注");
                }
            }



// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3

            String create_user = data.getString("opt_user");
            String create_time = data.getString("create_time");
            String sqls =
                    "update wjsc_jq_sjxx set personMs='" + bzs + "',personM='" + pms + "',jgbh='" + jgbh + "'," +
                            "dwmc='" + dwmc + "' " ;
            WjscJqSjxx wjscJqSjxx = wjscJqSjxxMapper.selectById(uuid);
            if (wjscJqSjxx != null) {
                if (StringUtils.isEmpty(wjscJqSjxx.getGmsfhm()) && StringUtils.isNotEmpty(data.getString("gmsfhm"))) {
                    gmsfhm = data.getString("gmsfhm");
                    sqls = sqls + ",gmsfhm='" + gmsfhm + "' ";
                }
            }
            sqls = sqls + "where " + "jjbh='" + jjbh + "' and uuid='" + uuid + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);

            if (vals.length() > 2) {
                vals = vals.substring(0, vals.length() - 1);
            }
            sqls =
                    "insert into jq_bz(bzr,bz_time,his_type,jjbh,personM) " + "values('" + data.getString("opt_user") + "'," + "'" + create_time + "','3','" + jjbh + "','" + personM + "')";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            RIUtil.sjryxg = RIUtil.sjryxg + gmsfhm + ",";


            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private R<Object> updateJQBZ_person1(JSONObject data) {
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";
            String gmsfhm = "";
            String personM = "";
            String jgbh = "";
            String dwmc = "";
            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return R.fail("缺少jjbh");
            }

            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                gmsfhm = data.getString("gmsfhm");
                sql = sql + " gmsfhm='" + gmsfhm + "' , ";

            } else {
                return R.fail("缺少gmsfhm");
            }

            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                String[] ids = data.getString("jgbh").split("\\|");
                if (!jgbh.startsWith("ZY")) {
                    jgbh = "ZY_" + ids[0];
                } else {
                    jgbh = ids[0];
                }
                try {
                    dwmc = ids[1];
                } catch (Exception ex) {
                    log.error(data.getString("jgbh"));
                }

            }

            List<String> pms = new ArrayList<>();
            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 3) {
                personM = data.getString("personM");


                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                String checks = checkLevel2Person(addss);
                if (checks.length() == 0) {

                    for (int i = 0; i < addss.size(); i++) {
                        String id = addss.get(i);
                        String memo = RIUtil.dicts.get(id).getString("memo");
                        int level = RIUtil.dicts.get(id).getInteger("static_index");
                        if (dwmc == null || dwmc.length() == 0) {
                            JSONArray nexts = RIUtil.GetDictByFather(id);
                      /* if (nexts.size() > 1&&!memo.contains("无业")&&!memo.contains("职业不详")) {
                            return R.fail(memo+"-未选择到最底层标签");
                        }*/

                            if (memo.contains("人员职业")) {
                                dwmc = RIUtil.dicts.get(id).getString("dict_name");
                                jgbh = id;
                            }
                            pms.add(id);
                        } else {
                            if (memo.contains("人员职业") && level == 4) {
                                System.out.println(dwmc);
                                if (dwmc.contains("[")) {
                                    String dwdz = dwmc.split("\\[")[1];
                                    dwmc = dwmc.split("\\[")[0];
                                    bzs = bzs + jgbh + " ";
                                    memo = memo + "-" + dwmc;
                                    String ss = "replace into  dict (id,dict_name,father_id,permission,static_index,memo," +
                                            "remark,type,up_time)" + " values('" + jgbh + "','" + dwmc + "','" + id + "'," +
                                            "'jqbq',"
                                            + "'5','" + memo + "','" + dwdz + "','4','" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm" + ":ss").format(new Date()) + "')";
                                    log.warn(ss);
                                    jdbcTemplate.update(ss);
                                    pms.add(jgbh);
                                } else {
                                    dwmc = RIUtil.dicts.get(id).getString("dict_name");
                                    jgbh = id;
                                    pms.add(id);
                                }
                            } else {
                                pms.add(id);
                            }
                        }
                        if (!bzs.contains(id)) {
                            bzs = bzs + id + " ";
                        }
                        while (id.length() > 0) {
                            try {
                                String fid = RIUtil.jqbqs.get(id).getString("father_id");


                                id = RIUtil.jqbqs.get(fid).getString("id");
                                if (!bzs.contains(id)) {
                                    bzs = bzs + id + " ";
                                }
                            } catch (Exception ex) {
                                id = "";
                            }

                        }

                    }
                    System.out.println(bzs);
                } else {
                    return R.fail(checks + "标签未标注");
                }
            }

// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3
            String create_user = data.getString("opt_user");
            String create_time = data.getString("create_time");
            String sqls =
                    "update wjsc_jq_sjxx set personMs='" + bzs + "',personM='" + pms + "',jgbh='" + jgbh + "'," +
                            "dwmc='" + dwmc + "' " + "where " + "jjbh='" + jjbh + "' and " + "gmsfhm='" + gmsfhm + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);

            if (vals.length() > 2) {
                vals = vals.substring(0, vals.length() - 1);
            }
            sqls =
                    "insert into jq_bz(bzr,bz_time,his_type,jjbh,personM) " + "values('" + data.getString("opt_user") + "'," + "'" + create_time + "','3','" + jjbh + "','" + personM + "')";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            RIUtil.sjryxg = RIUtil.sjryxg + gmsfhm + ",";


            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }


    //---------------------------query----------------------------
    @PostMapping("/list_his")
    @ApiOperation(value = "警情标注历史列表")

    public R<List<RespJQBZ>> listHis(@Valid @RequestBody ReqJQBZHis params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getJQBZ(data);
        //return R.ok();
    }

    private R<List<RespJQBZ>> getJQBZ(JSONObject data) {
        JSONArray ret;
        try {
            String sql = "";
            int page = 1;
            int limit = 20;
            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                String jjbh = data.getString("jjbh");
                sql = sql + " and jjbh = '" + jjbh + "'  ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from jq_bz where isdelete=0  " + sql + "  order by bz_time desc ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                ret = RIUtil.ListMap2jsa(list);
                List<RespJQBZ> d = RelaInfo(ret);
                sqls = "select count(id) as count from jq_bz where isdelete=0 " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    //---------------------------RELA.INFO----------------------------
    private List<RespJQBZ> RelaInfo(JSONArray ret) {

        List<RespJQBZ> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String create_user = one.getString("bzr");
            try {
                JSONObject userJson = SsoCache.me.cacheData.getJSONObject("user").getJSONObject(create_user);

                one.put("_create_user", userJson);
                one.put("_unit", userJson.getJSONArray("organization").getJSONObject(0));
            } catch (Exception ex) {
                one.put("_create_user", new JSONObject());
                one.put("_unit", new JSONObject());
            }
            String spr = one.getString("spr");
            try {
                JSONObject sprjson = SsoCache.me.cacheData.getJSONObject("user").getJSONObject(spr);
                one.put("_spr", sprjson);

                one.put("_spr_unit", sprjson.getJSONArray("organization").getJSONObject(0));
            } catch (Exception ex) {
                one.put("_spr", new JSONObject());

                one.put("_spr_unit", new JSONObject());
            }
            if (one.getIntValue("mark") == 0 && one.getIntValue("bzzt") == 0) {
                String jjbh = one.getString("jjbh");
                String sql = "select cjlb,cjsj_time,sfcs,fsyy,dwdz,baidu from wjsc_jq_cjxx where jjbh='" + jjbh + "'";
                JSONObject det = RIUtil.queryOne(sql, jdbcTemplate);
                //地址 -》事发场所 74
                List<String> addressMs = new ArrayList<>();
                try {

                    String sfcs = det.getString("sfcs");
                    sfcs = RIUtil.dicts.get("74-" + sfcs).getString("dict_name").replace("其他", "");

                    System.out.println(sfcs);
                    String id = RIUtil.GetDictIdByNameType(3, sfcs);
                    addressMs.add(id);
                } catch (Exception ex) {

                }

                //古方路597-677  712-808-》411c8451-df98-4f0e-a642-285a4cdfa297
                String dwdz = det.getString("dwdz");
                if (dwdz.contains("古方路")) {
                    String dzs = dwdz.split("\\古方路")[1];
                    if (dzs.contains("号")) {
                        try {
                            dzs = dzs.split("\\号")[0];
                            System.out.println(dzs);
                            int dznum = Integer.parseInt(dzs);
                            if ((dznum >= 597 && dznum <= 677) || (dznum >= 712 && dznum <= 808)) {
                                addressMs.add("411c8451-df98-4f0e-a642-285a4cdfa297");
                            }
                        } catch (Exception ex) {
                            System.out.println(dwdz);
                        }
                    }
                }
//发生原因 77
                List<String> reasonM = new ArrayList<>();
                try {

                    String fsyy = det.getString("fsyy");
                    if (fsyy.length() > 0) {
                        if (fsyy.startsWith("0")) {
                            fsyy = fsyy.substring(1);
                        }
                        try {
                            fsyy = RIUtil.dicts.get("77-" + fsyy).getString("dict_name");
                        } catch (Exception ex) {
                            log.error(fsyy);
                        }
                        String id = RIUtil.GetDictIdByNameType(8, fsyy);
                        reasonM.add(id);
                    }
                } catch (Exception ex) {
                    // System.out.println(Lib.getTrace(ex));
                }
//手段514
                List<String> toolM = new ArrayList<>();
                try {

                    String cjlb = det.getString("cjlb");

                    String cjlbN = RIUtil.dicts.get("51-" + cjlb).getString("dict_name").replace("其他", "");
                    log.warn(cjlbN);

                    String id = RIUtil.GetDictIdByNameType(9, cjlbN);
                    if (id == null || id.length() == 0) {
                        String cjfid = RIUtil.dicts.get("51-" + cjlb).getString("father_id");
                        String cjfidName = RIUtil.dicts.get(cjfid).getString("dict_name");
                        log.warn(cjfidName);
                        id = RIUtil.GetDictIdByNameType(9, cjfidName);
                    }
                    toolM.add(id);


                } catch (Exception ex) {
                    //System.out.println(Lib.getTrace(ex));

                }

                //
                String id = "33827D2519694C74B32F06418175E42B";
                try {
                    String cjsj = det.getString("cjsj_time");

                    try {
                        String hh = cjsj.substring(8, 10);
                        int h = Integer.parseInt(hh);

                        if (h < 6) {
                            id = "D8480F9C31754C51B40C6F21A36ADB6F";
                        } else if (h >= 6 && h < 12) {
                            id = "DE5156E24F6F4512AB0D575D2FDB1C99";
                        } else if (h >= 12 && h < 18) {
                            id = "C4F9800366AC41C2BB8D49F80E84FC4C";
                        } else if (h >= 18 && h < 24) {
                            id = "4B820D3577EC42A6BF67DD1E8F52CAD6";
                        } else {
                            id = "33827D2519694C74B32F06418175E42B";
                        }
                    } catch (Exception ex) {

                    }


                } catch (Exception ex) {

                }
                String baidu = det.getString("baidu");
                if (baidu != null && baidu.length() > 3) {
                    JSONObject bds = JSONObject.parseObject(baidu);

                    if (bds.containsKey("reasonM")) {
                        try {
                            String reasonMBd = bds.getString("reasonM");
                            reasonMBd = reasonMBd.replace(")", "").replace("%", "");
                            String[] rmbds = reasonMBd.split("\\(");
                            int p = Integer.parseInt(rmbds[1]);
                            if (p > 100) {
                                String n = rmbds[0].split("\\-")[1];
                                String rmbdid = RIUtil.GetDictIdByNameType(8, n);
                                reasonM.add(rmbdid);
                            }
                        } catch (Exception ex) {
                            log.error(bds.toString());
                        }
                    }

                    if (bds.containsKey("toolM")) {
                        try {
                            String reasonMBd = bds.getString("toolM");
                            reasonMBd = reasonMBd.replace(")", "").replace("%", "");
                            String[] rmbds = reasonMBd.split("\\(");
                            int p = Integer.parseInt(rmbds[1]);
                            if (p > 100) {
                                String n = rmbds[0].split("\\-")[1];
                                String rmbdid = RIUtil.GetDictIdByNameType(9, n);
                                toolM.add(rmbdid);
                            }
                        } catch (Exception ex) {
                            log.error(bds.toString());
                        }
                    }
                }


                //resultM 结果


                one.put("addressM", addressMs);
                one.put("reasonM", reasonM);
                one.put("toolM", toolM);
                one.put("timeM", id);


            }


            if (one.containsKey("addressM") && one.getString("addressM") != null && one.getString("addressM").length() > 0) {
                try {
                    String addMs = one.getString("addressM");
                    List<String> addressM = RIUtil.HashToList(RIUtil.StringToList(one.getString("addressM")));
                    //List<String> addressMNew = new ArrayList<>(addressM);

                    List<String> addressMNew = addressM.stream().filter(x -> {
                        Integer level;
                        String memo;
                        JSONObject label4Cache = RIUtil.jqbqs.get(x);
                        if (label4Cache != null) {
                            level = label4Cache.getInteger("static_index");
                            memo = label4Cache.getString("memo");
                        } else {
                            Dict dict = dictService.getOne(Wrappers.<Dict>lambdaQuery()
                                    .select(Dict::getId, Dict::getMemo, Dict::getStaticIndex)
                                    .eq(Dict::getType, 4)
                                    .eq(Dict::getId, x)
                                    .eq(Dict::getIsdelete, 1));
                            if (dict != null) {
                                level = dict.getStaticIndex();
                                memo = dict.getMemo();
                            } else return false;
                        }
                        if(memo.contains("发生部位") && level != 4 && level != 5) return false;
                        return true;
                    }).collect(Collectors.toList());
                    one.put("_addressM", RIUtil.RealDictNameList(RIUtil.StringToList(addressMNew.toString())));
                    List<Dict> dict = dictService.list(Wrappers.<Dict>lambdaQuery()
                            .select(Dict::getId, Dict::getFatherId)
                            .eq(Dict::getType, 3)
                            .eq(Dict::getStaticIndex, 5)
                            .likeRight(Dict::getMemo, "发生部位")
                            .in(Dict::getId, addressM));
                    if (dict.size() != 0) {
                        dict.forEach(x -> addressMNew.replaceAll(s -> s.equals(x.getId()) ? x.getFatherId() : s));
                    }
                    one.put("addressMNew", addressMNew);
                    one.put("addressM", addressM);
                } catch (Exception ex) {
                    one.put("_addressM", new JSONArray());
                    one.put("addressM", new ArrayList<>());
                }
            } else {
                one.put("_addressM", new JSONArray());
                one.put("addressM", new ArrayList<>());
            }

            if (one.containsKey("resultM") && one.getString("resultM") != null && one.getString("resultM").length() > 0) {
                try {
                    List<String> resultM = RIUtil.HashToList(RIUtil.StringToList(one.getString("resultM")));
                    HashMap<String, String> resultMMap = resultM.stream().filter(x -> RIUtil.GetDictByFather(x).isEmpty()).collect(Collectors.toMap(y -> y, y -> "", (vo, v) -> v, HashMap::new));
                    one.put("_resultM", RIUtil.RealDictNameList(resultMMap));
                    one.put("resultM", resultMMap.keySet());
                } catch (Exception ex) {
                    one.put("_resultM", new JSONArray());
                    one.put("resultM", new ArrayList<>());
                }
            } else {
                one.put("_resultM", new JSONArray());
                one.put("resultM", new ArrayList<>());
            }
            if (one.containsKey("timeM") && one.getString("timeM") != null && one.getString("timeM").length() > 0) {
                try {
                    one.put("_timeM", RIUtil.dicts.get(one.getString("timeM")));

                } catch (Exception ex) {
                    one.put("_timeM", new JSONObject());
                }
            } else {
                one.put("_timeM", new JSONObject());
            }

            if (one.containsKey("toolM") && one.getString("toolM") != null && one.getString("toolM").length() > 0) {
                try {
                    List<String> toolM = RIUtil.HashToList(RIUtil.StringToList(one.getString("toolM")));
                    HashMap<String, String> toolMMap = toolM.stream().filter(x -> RIUtil.GetDictByFather(x).isEmpty()).collect(Collectors.toMap(y -> y, y -> "", (vo, v) -> v, HashMap::new));
                    one.put("_toolM", RIUtil.RealDictNameList(toolMMap));
                    one.put("toolM", toolMMap.keySet());
                } catch (Exception ex) {
                    one.put("_toolM", new JSONArray());
                    one.put("toolM", new ArrayList<>());
                }
            } else {
                one.put("_toolM", new JSONArray());
                one.put("toolM", new ArrayList<>());
            }
            if (one.containsKey("reasonM") && one.getString("reasonM") != null && one.getString("reasonM").length() > 0) {
                try {
                    List<String> reasonM = RIUtil.HashToList(RIUtil.StringToList(one.getString("reasonM")));
                    HashMap<String, String> reasonMMap = reasonM.stream().filter(x -> RIUtil.GetDictByFather(x).isEmpty()).collect(Collectors.toMap(y -> y, y -> "", (vo, v) -> v, HashMap::new));
                    one.put("_reasonM", RIUtil.RealDictNameList(reasonMMap));
                    one.put("reasonM", reasonMMap.keySet());
                } catch (Exception ex) {
                    one.put("_reasonM", new JSONArray());
                    one.put("reasonM", new ArrayList<>());
                }
            } else {
                one.put("_reasonM", new JSONArray());
                one.put("reasonM", new ArrayList<>());
            }

            one.put("create_time", one.getString("bz_time"));
            String sql = "select cjlb,cjxxdd,bzdzmc from wjsc_jq_cjxx where jjbh='" + one.getString("jjbh") + "'";
            JSONObject det = RIUtil.queryOne(sql, jdbcTemplate);
            System.out.println(det);
            String cjlb = det.getString("cjlb");
            if (cjlb != null && cjlb.length() > 0) {
                String lb = "51-" + cjlb;
                try {
                    one.put("cjlb", RIUtil.dicts.get(lb).getString("dict_name"));
                } catch (Exception e) {
                    log.error(lb);
                    lb = "50-" + cjlb;
                    try {
                        one.put("cjlb", RIUtil.dicts.get(lb).getString("dict_name"));
                    } catch (Exception ex) {
                        log.error(lb);
                    }
                }
                String cjlbs = one.getString("cjlb");

                String id = "";
                try {
                    String fid = RIUtil.dicts.get(lb).getString("father_id");

                    while (!fid.equals("-1")) {
                        String n = RIUtil.dicts.get(fid).getString("dict_name");
                        cjlbs = n + "-" + cjlbs;

                        fid = RIUtil.dicts.get(fid).getString("father_id");
                    }
                } catch (Exception ex) {

                }
                System.out.println(cjlbs);

                one.put("cjlbs", cjlbs);
                String fsdd = det.getString("cjxxdd") + " " + det.getString("bzdzmc");
                one.put("fsdd", fsdd);
            } else {
                one.put("cjlbs", "");
            }

            String baidu = one.getString("baidu");
            try {
                baidu = baidu.replace("案事件发生地点-", "").replace("是否重点场所:否;", "").replace("是否重点场所:是;", "");

                JSONObject bd = JSONObject.parseObject(baidu);
                one.put("baidu", bd);
            } catch (Exception ex) {
                // log.error(Lib.getTrace(ex));
                log.error("baidu-->" + baidu);
            }
            String dwmc = one.getString("dwmc");
            if ((dwmc == null || dwmc.length() == 0) && one.getString("jjbh") != null && one.getString("jjbh").length() > 0) {

                one = GetDwmc(one);
            }
//            String dzid = one.getString("dzid");
//            String sszrq = one.getString("sszrq");
//            if (StringUtils.isNotEmpty(dzid) && StringUtils.isEmpty(sszrq)) {
//                one = GetSszrq(one);
//            }

            RespJQBZ dd = gson.fromJson(String.valueOf(one), RespJQBZ.class);
            back.add(dd);
        }
        return back;
    }



    private JSONObject GetSszrq(JSONObject one) {
        String sszrq = "";
        String dzid = one.getString("dzid");
        if (StringUtils.isNotBlank(dzid)) {
            OracleHelper ora = null;
            try {
                ora = new OracleHelper("ora_hl");
                String sql = "select * from czqj_ybds.address_info\n" +
                        "@qjjc_ybls where dzid='" + dzid + "'";
                log.warn(sql);
                List<JSONObject> list = ora.query(sql);
                if (!list.isEmpty() && list.get(0) != null) {
                    JSONObject jsonObject = list.get(0);
                    sszrq = jsonObject.getString("HJZRQ");
                }
                if (StringUtils.isNotBlank(sszrq)) {
                    String sql1 = "update wjsc_jq_cjxx set sszrq='" + sszrq +
                            "'" + " where dzid is not null and dzid='" + dzid+ "' ";//+xq+build_no

                    System.out.println(sql1);
                    jdbcTemplate.update(sql1);
                }
            } catch (Exception e) {

            }
        }
        return one;
    }
    private JSONObject GetDwmc(JSONObject one) {

        String sql4Sszrq = "";
        one.put("opt", "get_jq");
        one.put("opt_user", "342423198903157010");
        one.put("real_opt_user", "342423198903157010");
        one.put("JJBH", one.getString("jjbh"));
        System.out.println(one);

        String ret = GethttpOk(one, "6a4d46f07acbc88b4b476dd793ac1627", "http://50.56.93.250:10363/jq");
        JSONObject rets = JSONObject.parseObject(ret);

        if (rets.containsKey("errno") && rets.getIntValue("errno") == 0) {
            JSONArray data = rets.getJSONArray("data");
            log.info("{}<------- see see", data);

            if (data.size() > 0) {
                JSONObject det = data.getJSONObject(0);
                String dwmc = det.getString("DWMC");
                String dwdz = det.getString("DZMC");
                String dzid = det.getString("DZID");
                one.put("dwmc", dwmc);
                one.put("dwdz", dwdz);
                one.put("dzid", dzid);
                String sszrq = one.getString("sszrq");
                if (StringUtils.isNotBlank(dzid) && StringUtils.isBlank(sszrq)) {
                    try {
                        OracleHelper ora = new OracleHelper("ora_hl");
                        String sql = "select * from czqj_ybds.address_info\n" +
                                "@qjjc_ybls where dzid='" + dzid + "'";
                        log.warn(sql);
                        List<JSONObject> list = ora.query(sql);
                        if (!list.isEmpty() && list.get(0) != null) {
                            JSONObject jsonObject = list.get(0);
                            String sszrq1 = jsonObject.getString("HJZRQ");
                            sql4Sszrq = ",sszrq = '" + sszrq1 + "'";
                            one.put("sszrq", sszrq1);
                        }
                    } catch (Exception e) {
                       log.error("update sszrq fail ----> {}", e);
                    }
                }

                try {


                    String sql = "update wjsc_jq_cjxx set dwmc='" + dwmc + "',dwdz='" + dwdz + "',dzid='" + dzid +
                            "'"+ sql4Sszrq + " where jjbh='" + one.getString("jjbh") + "' ";//+xq+build_no

                    System.out.println(sql);
                    jdbcTemplate.update(sql);


                } catch (Exception e) {
                    log.error(Lib.getTrace(e));
                }
            }
        }
        return one;

    }

    private String GethttpOk(JSONObject one, String token, String url) {

        try {
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, String.valueOf(one));
            okhttp3.Request request =
                    new Request.Builder().url(url).method("POST", body).addHeader("token", token).addHeader(
                            "Content_Type",
                            "application/json").build();
            okhttp3.Response response = client.newCall(request).execute();
            String ret = response.body().string();
            return ret;
        } catch (Exception e) {
            log.error(Lib.getTrace(e));
            return "";
        }
    }

    @PostMapping("/list")
    @ApiOperation(value = "警情列表")

    public R<List<RespJQBZList>> list(@Valid @RequestBody ReqJQBZQuery params) throws Exception {

        if (params.getNeedChild() != null && params.getNeedChild() == true) {
            List<String> quanXuanList = jqbzService.getIdByQuanXuan(params.getBzbq());
            params.setBzbq(quanXuanList);
        }

        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();

        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        // initDict();


        return getJQList(data);
        //return R.ok();
    }

    @PostMapping("/list_test")
    @ApiOperation(value = "警情列表Test")

    public String list_test(@Valid @RequestBody JSONObject data) throws Exception {
        System.out.println(data);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("");
            String sql = data.getString("sql");
            List<JSONObject> list = mysql.query(sql);
            return String.valueOf(list);

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return Lib.getTrace(ex);
        } finally {
            mysql.close();
        }

        //return getJQListTest(data);
        //return R.ok();
    }

    private R<List<RespJQBZList>> getJQList(JSONObject data) {
        try {
            String sql = "";
            int page = 1;
            int limit = 20;
            //处警类别
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lbs = data.getString("cjlb");
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cj.cjlb like '" + cjlb.replace("000000", "") + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cj.cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cj.cjlb like '%" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cj.cjlb='" + cjlb + "' or ";
                    }
                }
                if (s.endsWith("or ")) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

//处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                String jjbh = data.getString("jjbh");
                if (jjbh.endsWith(",")) {
                    jjbh = jjbh.substring(0, jjbh.length() - 1);
                }
                if (jjbh.startsWith("J") && jjbh.length() == 20) {
                    sql = sql + " and cj.jjbh='" + jjbh + "' ";
                } else if (jjbh.length() > 22 && jjbh.contains(",")) {
                    jjbh = jjbh.substring(0, jjbh.length() - 1);
                    jjbh = jjbh.replace(",", "','");
                    sql = sql + " and cj.jjbh in ('" + jjbh + "') ";
                } else {
                    sql = sql + " and cj.jjbh like '%" + jjbh + "%' ";
                }
            }

//事发场所
            if (data.containsKey("sfcs") && data.getString("sfcs").length() > 0) {

                String lbs = data.getString("sfcs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sfcs = cone.getKey();


                    if (sfcs.contains("-")) {
                        sfcs = sfcs.split("\\-")[1];
                    }
                    if (sfcs.endsWith("00")) {
                        s = s + "  cj.sfcs like '" + sfcs.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.sfcs='" + sfcs + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //发生原因
            if (data.containsKey("fsyy") && data.getString("fsyy").length() > 0) {

                String lbs = data.getString("fsyy");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fsyy = cone.getKey();


                    if (fsyy.contains("-")) {
                        fsyy = fsyy.split("\\-")[1];
                    }
                    if (fsyy.endsWith("00")) {
                        s = s + "  cj.fsyy like '0" + fsyy.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.fsyy='0" + fsyy + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            if (data.containsKey("sszrq") && data.getString("sszrq").length() > 0) {
                String sszrq = data.getString("sszrq");
                int type = 23;
                type = RIUtil.dicts.get(sszrq).getInteger("type");
                if (type == 23) {
                    sql = sql + " and cj.sszrq like '" + sszrq.substring(0, 6) + "%'  ";
                } else if (type == 25 || type == 24) {
                    sql = sql + " and cj.sszrq like '" + sszrq.substring(0, 8) + "%'  ";
                } else {
                    sql = sql + " and cj.sszrq = '" + sszrq + "' ";
                }
            }

            String eventId = data.getString("eventId");
            if (StringUtils.isNotBlank(eventId)) {
                sql = sql + " and cj.eventId = '" + eventId + "' ";
            }
//处理结果内容
            if (data.containsKey("cljgnr") && data.getString("cljgnr").length() > 0) {
                String cljgnr = data.getString("cljgnr");
                sql = sql + " and cj.cljgnr like '%" + cljgnr + "%' ";
            }

//处警详址
            if (data.containsKey("cjxz") && data.getString("cjxz").length() > 0) {
                String cjxz = data.getString("cjxz");
                sql = sql + " and cj.cjxz like '%" + cjxz + "%' ";
            }

//处警结果
            if (data.containsKey("cjjg") && data.getString("cjjg").length() > 0) {
                String lbs = data.getString("cjjg");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjg = cone.getKey();


                    if (cjjg.contains("-")) {
                        cjjg = cjjg.split("\\-")[1];
                    }
                    s = s + "  cj.cjjg='" + cjjg + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //事发时间下限开始
            if (data.containsKey("sfsjxx_start") && data.getString("sfsjxx_start").length() > 0) {
                String sfsjxx_start = data.getString("sfsjxx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time>='" + sfsjxx_start + "' ";
            }

            //事发时间下限结束
            if (data.containsKey("sfsjxx_end") && data.getString("sfsjxx_end").length() > 0) {
                String sfsjxx_end = data.getString("sfsjxx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time<='" + sfsjxx_end + "' ";
            }

            //事发时间上限开始
            if (data.containsKey("sfsjsx_start") && data.getString("sfsjsx_start").length() > 0) {
                String sfsjsx_start = data.getString("sfsjsx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time>='" + sfsjsx_start + "' ";
            }

            //事发时间上限结束
            if (data.containsKey("sfsjsx_end") && data.getString("sfsjsx_end").length() > 0) {
                String sfsjsx_end = data.getString("sfsjsx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time<='" + sfsjsx_end + "' ";
            }

            //处警登记时间开始
            if (data.containsKey("djsj_start") && data.getString("djsj_start").length() > 0) {
                String djsj_start = data.getString("djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time>='" + djsj_start + "' ";
            }

            //处警登记时间结束
            if (data.containsKey("djsj_end") && data.getString("djsj_end").length() > 0) {
                String djsj_end = data.getString("djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time<='" + djsj_end + "' ";
            }

            //处警修改时间开始
            if (data.containsKey("cj_xgsj_start") && data.getString("cj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("cj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time>='" + xgsj_start + "' ";
            }

            //处警修改时间结束
            if (data.containsKey("cj_xgsj_end") && data.getString("cj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("cj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time<='" + xgsj_end + "' ";
            }


            //处警标识
            if (data.containsKey("cjbs") && data.getString("cjbs").length() > 0) {
                String lbs = data.getString("cjbs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjbs = cone.getKey();

                    if (cjbs.contains("-")) {
                        cjbs = cjbs.split("\\-")[1];
                    }
                    s = s + " jj.cjbs='" + cjbs + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警报警人
            if (data.containsKey("bjr") && data.getString("bjr").length() > 0) {
                String bjr = data.getString("bjr");
                sql = sql + " and jj.bjr like '%" + bjr + "%' ";
            }

            //报警类型
            if (data.containsKey("bjlx") && data.getString("bjlx").length() > 0) {
                String lbs = data.getString("bjlx");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjlx = cone.getKey();


                    if (bjlx.contains("-")) {
                        bjlx = bjlx.split("\\-")[1];
                    }
                    if (bjlx.endsWith("0000")) {
                        s = s + " jj.bjlx like '" + bjlx.replace("0000", "") + "%' or ";
                    } else if (bjlx.endsWith("00")) {
                        s = s + "  jj.bjlx like '" + bjlx.replace("00", "") + "%' or ";
                    } else {

                        s = s + " jj.bjlx='" + bjlx + "' or ";
                    }

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警单位
            String unit = "";
            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
                unit = data.getString("jjdw");

            } else {
                unit = data.getString("unit");

            }
            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
            String sq = "";
            for (int i = 0; i < us.size(); i++) {
                String u = us.get(i);
                //todo
                int type = 23;
                try {
                    type = RIUtil.dicts.get(u).getInteger("type");
                } catch (Exception ex) {
                    log.warn(u);
                    try {
                        log.warn(String.valueOf(RIUtil.dicts.get(u)));
                    } catch (Exception e) {

                    }
                }
                if (type == 21 || type == 22 || type == 27) {
                    // sql = sql + " and jj.jjdw like '%" + unit.substring(0, 4) + "%'";
                } else if (type == 23 || type == 24) {
                    sq = sq + "  jj.jjdw like '" + u.substring(0, 6) + "%' or ";
                } else if (type == 26 || type == 25) {

                    sq = sq + "  jj.jjdw like '" + u.substring(0, 8) + "%' or ";
                } else {
                    sq = sq + "  jj.jjdw='" + u + "' or ";
                }
            }
            if (sq.endsWith("or ")) {
                sq = sq.substring(0, sq.length() - 3);
                sql = sql + " and (" + sq + ") ";
            }


            //接警报警人联系电话
            if (data.containsKey("lxdh") && data.getString("lxdh").length() > 0) {
                String lxdh = data.getString("lxdh");
                sql = sql + " and jj.lxdh like '%" + lxdh + "%' ";
            }

            //报警内容
            if (data.containsKey("bjnr") && data.getString("bjnr").length() > 0) {
                String bjnr = data.getString("bjnr");
                sql = sql + " and jj.bjnr like '%" + bjnr + "%' ";
            }

            //发生地点
            if (data.containsKey("sfdd") && data.getString("sfdd").length() > 0) {
                String sfdd = data.getString("sfdd");
                sql = sql + " and jj.sfdd like '%" + sfdd + "%' ";
            }

            //报警方式
            if (data.containsKey("bjxs") && data.getString("bjxs").length() > 0) {
                String lbs = data.getString("bjxs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjxs = cone.getKey();


                    if (bjxs.contains("-")) {
                        bjxs = bjxs.split("\\-")[1];
                    }
                    s = s + "  jj.bjxs='" + bjxs + "' or ";

                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

            //接警登记时间开始
            if (data.containsKey("jj_djsj_start") && data.getString("jj_djsj_start").length() > 0) {
                String djsj_start = data.getString("jj_djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time>='" + djsj_start + "' ";
            }

            //接警登记时间结束
            if (data.containsKey("jj_djsj_end") && data.getString("jj_djsj_end").length() > 0) {
                String djsj_end = data.getString("jj_djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time<='" + djsj_end + "' ";
            }

            //接警修改时间开始
            if (data.containsKey("jj_xgsj_start") && data.getString("jj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("jj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time>='" + xgsj_start + "' ";
            }

            //接警修改时间结束
            if (data.containsKey("jj_xgsj_end") && data.getString("jj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("jj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time<='" + xgsj_end + "' ";
            }

            //接警日期时间开始
            if (data.containsKey("jjrqsj_start") && data.getString("jjrqsj_start").length() > 0) {
                String jjrqsj_start = data.getString("jjrqsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.bjdhsj_time>='" + jjrqsj_start + "' ";
            }

            //接警日期时间结束
            if (data.containsKey("jjrqsj_end") && data.getString("jjrqsj_end").length() > 0) {
                String jjrqsj_end = data.getString("jjrqsj_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and jj.bjdhsj_time<='" + jjrqsj_end + "' ";
            }

            //涉警人员身份证
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                String gmsfhm = data.getString("gmsfhm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm like '%" + gmsfhm + "%') ";
            }

            //涉警人员姓名
            if (data.containsKey("xm") && data.getString("xm").length() > 0) {
                String xm = data.getString("xm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where xm like '%" + xm + "%') ";
            }

            //涉警类别
            if (data.containsKey("sjlb") && data.getString("sjlb").length() > 0) {
                String lbs = data.getString("sjlb");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sjlb = cone.getKey();
                    if (sjlb.contains("-")) {
                        sjlb = sjlb.split("\\-")[1];
                    }
                    s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where sjlb ='" + sjlb + "') or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //反馈时间

            String fksql = " ";
            if (data.containsKey("fk_time_start") && data.getString("fk_time_start").length() > 0) {
                String fk_time_start = data.getString("fk_time_start");

                fksql = fksql + "and create_time >='" + fk_time_start + "' ";
            }

            if (data.containsKey("fk_time_end") && data.getString("fk_time_end").length() > 0) {
                String fk_time_end = data.getString("fk_time_end");

                fksql = fksql + "and create_time <='" + fk_time_end + "' ";
            }
            if (fksql.length() > 5) {
                fksql = "select jjbh from jq_fk where 1=1 " + fksql;
                sql = sql + " and cj.jjbh in (" + fksql + ")";
            }


            //反馈人
            if (data.containsKey("fk_user") && data.getString("fk_user").length() > 0) {
                String fk_user = data.getString("fk_user");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where fkr ='" + fk_user + "') ";
            }
            //反馈单位
            if (data.containsKey("fk_unit") && data.getString("fk_unit").length() > 0) {
                String fk_unit = data.getString("fk_unit");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where unit ='" + fk_unit + "') ";
            }
            //省厅标签dict:75
            if (data.containsKey("cjjqbq") && data.getString("cjjqbq").length() > 0) {
                String lbs = data.getString("cjjqbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjqbq = cone.getKey();

                    if (cjjqbq.contains("-")) {
                        cjjqbq = cjjqbq.split("\\-")[1];
                    }
                    s = s + "  cjjqbq like '%" + cjjqbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //分局标签dict:76
            if (data.containsKey("fjbq") && data.getString("fjbq").length() > 0) {
                String lbs = data.getString("fjbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fjbq = cone.getKey();

                    if (fjbq.contains("-")) {
                        fjbq = fjbq.split("\\-")[1];
                    }
                    s = s + "  fjbq like '%" + fjbq + "%' or ";
                }

                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            if (data.containsKey("currentSearch") && data.getString("currentSearch").length() > 3) {
//                String bzgs = data.getString("currentSearch");
//                List<String> gss = RIUtil.StringToArrayList(bzgs);
//                String s = BzgsUtil.convertBzCondition(gss);
//                if (s.length() > 3) {
//                    s = s.replace("NOT", "!=");
//                    System.out.println(s);
//                    sql = sql + " and (" + s + ")";
//                }
                String lbs = data.getString("currentSearch");
                System.out.println(lbs);
                String tmp = "";
                String plbs = "";
                String dlbs = "";
                String personMConditonSql = "";

                List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                Set<String> personMSet = new HashSet<>();
                int personMCount = 0;
                for (int i = 0; i < lbss.size(); i++) {
                    String lb = lbss.get(i);
                    JSONObject dict = RIUtil.dicts.get(lb);
                    if (dict == null) continue;
                    int lbt = dict.getIntValue("type");
                    if (lbt == 4) {
                        personMSet.add(lb.trim());
                        personMCount ++;
                        plbs = plbs + "\"" + lb + "\" ";
                    } else {
                        dlbs = dlbs + "\"" + lb + "\" ";
                    }
                }

                String lbsql = "";
                if (dlbs.length() > 2) {
                    lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode))  ";
                }

                //版本3
                if (personMCount == 1 && plbs.length() > 2) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " or ";
                    }
                    lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                            + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";
                } else if (personMCount > 1) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " or ";
                    }
                    //优化
                    String sqlCondition = " SELECT\n" +
                            "\t\t\tjjbh \n" +
                            "\t\tFROM\n" +
                            "\t\t\t( SELECT jjbh, JSON_ARRAYAGG( label ) AS jsonLabels FROM `wj_jq_person_label` WHERE is_deleted = 0 GROUP BY jjbh ) AS t \n" +
                            "\t\tWHERE ";
                    String labelCondition = personMSet.stream().filter(StringUtils::isNotBlank).map(personLabel -> " JSON_CONTAINS( jsonLabels, '\"" + personLabel + "\"' ) ").collect(Collectors.joining(" and "));
                    lbsql = lbsql + "cj.jjbh in ( " + sqlCondition + labelCondition + " )";
                }

                if (lbsql.length() > 2) {
                    sql = sql + " and (" + lbsql + ") ";
                }
            }

//标注标签dict:10
            if (data.containsKey("bzbq4Sta") && data.getString("bzbq4Sta") != null && data.getString("bzbq4Sta").length() > 3) {
                String lbs = data.getString("bzbq4Sta");
                System.out.println(lbs);
                String tmp = "";
                String plbs = "";
                String dlbs = "";
                String personMConditonSql = "";

                List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                Set<String> personMSet = new HashSet<>();
                int personMCount = 0;
                for (int i = 0; i < lbss.size(); i++) {
                    String lb = lbss.get(i);
                    JSONObject dict = RIUtil.dicts.get(lb);
                    if (dict == null) continue;
                    int lbt = dict.getIntValue("type");
                    if (lbt == 4) {
                        //版本3
                        personMSet.add(lb.trim());
                        personMCount ++;
                        //版本1
//                         if (lb.contains(":") || lb.contains("-")){
//                             lb = lb.replaceAll("[:-]", " +");
//                         }
//                         plbs = plbs + "+" + lb + " ";
                        plbs = plbs + "+\"" + lb + "\" ";
                    } else {
//                         if (lb.contains(":") || lb.contains("-")){
//                             String matchCondition = lb.replaceAll("[:-]", " +");
//                             dlbs = dlbs + "+" + matchCondition + " ";
//                         } else {
//                             dlbs = dlbs + "+" + lb + " ";
//                         }
                        dlbs = dlbs + "+\"" + lb + "\" ";
                    }
                }

                String lbsql = "";
                if (dlbs.length() > 2) {
                    lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode))  ";
                }

                //版本3
                if (personMCount == 1 && plbs.length() > 2) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " and ";
                    }
                    lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                            + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";
                } else if (personMCount > 1) {
                    if (lbsql.contains("MATCH")) {
                        lbsql = lbsql + " and ";
                    }
                    //优化
                    String sqlCondition = " SELECT\n" +
                            "\t\t\tjjbh \n" +
                            "\t\tFROM\n" +
                            "\t\t\t( SELECT jjbh, JSON_ARRAYAGG( label ) AS jsonLabels FROM `wj_jq_person_label` WHERE is_deleted = 0 GROUP BY jjbh ) AS t \n" +
                            "\t\tWHERE ";
                    String labelCondition = personMSet.stream().filter(StringUtils::isNotBlank).map(personLabel -> " JSON_CONTAINS( jsonLabels, '\"" + personLabel + "\"' ) ").collect(Collectors.joining(" and "));
                    lbsql = lbsql + "cj.jjbh in ( " + sqlCondition + labelCondition + " )";
                }

                if (lbsql.length() > 2) {
                    sql = sql + " and (" + lbsql + ") ";
                }

            } else {
                if (data.containsKey("bzbq") && data.getString("bzbq").length() > 3) {
                    String lbs = data.getString("bzbq");
                    System.out.println(lbs);
                    String tmp = "";
                    String plbs = "";
                    String dlbs = "";
                    String personMConditonSql = "";

                    List<String> lbss = RIUtil.HashToList(RIUtil.StringToList(lbs));
                    Set<String> personMSet = new HashSet<>();
                    int personMCount = 0;
                    for (int i = 0; i < lbss.size(); i++) {
                        String lb = lbss.get(i);
                        JSONObject dict = RIUtil.dicts.get(lb);
                        if (dict == null) continue;
                        int lbt = dict.getIntValue("type");
                        if (lbt == 4) {
                            //版本3
                            personMSet.add(lb.trim());
                            personMCount ++;
                            //版本1
//                         if (lb.contains(":") || lb.contains("-")){
//                             lb = lb.replaceAll("[:-]", " +");
//                         }
//                         plbs = plbs + "+" + lb + " ";
                            plbs = plbs + "\"" + lb + "\" ";
                        } else {
//                         if (lb.contains(":") || lb.contains("-")){
//                             String matchCondition = lb.replaceAll("[:-]", " +");
//                             dlbs = dlbs + "+" + matchCondition + " ";
//                         } else {
//                             dlbs = dlbs + "+" + lb + " ";
//                         }
                            dlbs = dlbs + "\"" + lb + "\" ";
                        }
                    }

                    String lbsql = "";
                    if (dlbs.length() > 2) {
                        lbsql = lbsql + " (MATCH(jqbz) AGAINST ('" + dlbs + "' in boolean mode))  ";
                    }

                    //版本3
                    if (personMCount == 1 && plbs.length() > 2) {
                        if (lbsql.contains("MATCH")) {
                            lbsql = lbsql + " or ";
                        }
                        lbsql = lbsql + " cj.jjbh in (select jjbh from " + "wjsc_jq_sjxx" + " " + "where MATCH(personMs) "
                                + "AGAINST ('" + plbs + "' in boolean mode )" + " ) ";
                    } else if (personMCount > 1) {
                        if (lbsql.contains("MATCH")) {
                            lbsql = lbsql + " or ";
                        }
                        //优化
                        String sqlCondition = " SELECT\n" +
                                "\t\t\tjjbh \n" +
                                "\t\tFROM\n" +
                                "\t\t\t( SELECT jjbh, JSON_ARRAYAGG( label ) AS jsonLabels FROM `wj_jq_person_label` WHERE is_deleted = 0 GROUP BY jjbh ) AS t \n" +
                                "\t\tWHERE ";
                        String labelCondition = personMSet.stream().filter(StringUtils::isNotBlank).map(personLabel -> " JSON_CONTAINS( jsonLabels, '\"" + personLabel + "\"' ) ").collect(Collectors.joining(" and "));
                        lbsql = lbsql + "cj.jjbh in ( " + sqlCondition + labelCondition + " )";
                    }

                    if (lbsql.length() > 2) {
                        sql = sql + " and (" + lbsql + ") ";
                    }

                }
            }


            //标注公式
            if (data.containsKey("bzgs") && data.getString("bzgs").length() > 3) {
                String bzgs = data.getString("bzgs");
                List<String> gss = RIUtil.StringToArrayList(bzgs);
                String s = BzgsUtil.convertBzCondition(gss);
                //StringBuilder s = new StringBuilder("");
//                String s = "";
//                gss = BzgsUtil.fullBzgsList(gss);
//                for (int i = 0; i < gss.size(); i++) {
//                    String g = gss.get(i);
//
//                    if (g.equalsIgnoreCase("(") || g.equalsIgnoreCase(")") || g.equalsIgnoreCase("AND") || g.equalsIgnoreCase("NOT") || g.equalsIgnoreCase("OR")) {
//
//                        //s.append(" ").append(g).append(" ");
//                        s = s + " " + g + " ";
//
//                    } else {
////                        if ((!s.startsWith("AND ") || !s.startsWith(") ") || !s.startsWith("OR ")) && s.length() > 5) {
////                            s = s + " or ";
////                        }
//
//                        //if ((!s.endsWith("AND ") || !s.endsWith(") ") || !s.endsWith("OR ")) && s.length() > 5) {
//                        if ((!StringUtils.endsWithIgnoreCase(s, "AND ") && !StringUtils.endsWithIgnoreCase(s, "OR ")) && s.length() > 5) {
//                            s = s + " AND ";
//                        }
//                        int t = RIUtil.dicts.get(g).getIntValue("type");
////                        if (g.contains(":") || g.contains("-")){
////                            g = g.replaceAll("[:-]", " +");
////                        }
//
//                        g = "\"" + g + "\"";
//                        if (t != 4) {
//                            s = s + " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
//                        } else {
//                            s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
//                        }
//                    }
//
//
//                }

                if (s.length() > 3) {
                    s = s.replace("NOT", "!=");
                    System.out.println(s);

                    sql = sql + " and (" + s + ")";
                }

            }
            //标注状态
            if (data.containsKey("bzzt") && data.getString("bzzt").length() > 0) {
                String is_mark = data.getString("bzzt");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //是否标注
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                String is_mark = data.getString("mark");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
                String bz_time_start = data.getString("bz_time_start");
                sql = sql + " and bz_time>='" + bz_time_start + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
                String bz_time_end = data.getString("bz_time_end");
                sql = sql + " and bz_time<='" + bz_time_end + "'  ";
            }
            //标注人
            if (data.containsKey("bzr") && data.getString("bzr").length() > 0) {
                String bzr = data.getString("bzr");
                sql = sql + " and bzrxm like '%" + bzr + "%'  ";
            }


            //是否关注
            if (data.containsKey("is_gz") && data.getString("is_gz").length() > 0) {
                String is_gz = data.getString("is_gz");
                if (is_gz.equals("1")) {
                    sql =
                            sql + " and jj.jjbh in (select jjbh from jq_gz where isdelete=0 and create_user='" + data.getString("opt_user") + "')";
                }

            }


            //审批人
            if (data.containsKey("spr") && data.getString("spr").length() > 0) {
                String spr = data.getString("spr");
                sql = sql + " and sprxm like  '%" + spr + "%'  ";
            }
            //审批结果
            if (data.containsKey("spjg") && data.getString("spjg").length() > 0) {
                String spjg = data.getString("spjg");
                sql = sql + " and spjg =  '" + spjg + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_start") && data.getString("spsj_start").length() > 0) {
                String spsj_start = data.getString("spsj_start");
                sql = sql + " and sp_time>='" + spsj_start + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_end") && data.getString("spsj_end").length() > 0) {
                String spsj_end = data.getString("spsj_end");
                sql = sql + " and sp_time<='" + spsj_end + "'  ";
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select cj.jjbh,jj.bjdhsj_time ,jjdwmc,bjlx,cjlb,cjjg,cljgnr,sfcs, spjg,cjjqbq,bzzt,mark," +
                            "addressM," + "personM,resultM,timeM,toolM,reasonM,sszrq,eventId" + " " + "from" + " " + "v_jq_cjxx_bz "
                            + " " + "cj " + "LEFT JOIN  " + "wjsc_jq_jjxx jj on cj.jjbh=jj.jjbh  " + "where " + "1=1"
                            + "  " + sql + "  " + "order by " + "bjdhsj_time  ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespJQBZList> d = RelaInfoList(ret, data.getString("opt_user"));
                sqls = "select count(cj.jjbh) as count from v_jq_cjxx_bz cj  left join wjsc_jq_jjxx jj on cj" +
                        ".jjbh" + "=jj" + ".jjbh " + "where " + "1=1 " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private R<List<RespJQBZList>> getJQListTest(JSONObject data) {
        try {
            String sql = "";
            int page = 1;
            int limit = 20;
            //处警类别
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lbs = data.getString("cjlb");
                HashMap<String, String> cjlbs = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : cjlbs.entrySet()) {
                    String cjlb = cone.getKey();

                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        s = s + "  cj.cjlb like '" + cjlb.replace("000000", "") + "%' or ";
                    } else if (cjlb.endsWith("0000")) {
                        s = s + "  cj.cjlb like '" + cjlb.substring(0, 4) + "%' or ";
                    } else if (cjlb.endsWith("00")) {
                        s = s + "  cj.cjlb like '%" + cjlb.substring(0, 6) + "%' or ";
                    } else {

                        s = s + "  cj.cjlb='" + cjlb + "' or ";
                    }
                }
                if (s.endsWith("or ")) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

//处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                String jjbh = data.getString("jjbh");
                if (jjbh.startsWith("J") && jjbh.length() == 20) {
                    sql = sql + " and cj.jjbh='" + jjbh + "' ";
                } else {
                    sql = sql + " and cj.jjbh like '%" + jjbh + "%' ";
                }
            }

//事发场所
            if (data.containsKey("sfcs") && data.getString("sfcs").length() > 0) {

                String lbs = data.getString("sfcs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sfcs = cone.getKey();


                    if (sfcs.contains("-")) {
                        sfcs = sfcs.split("\\-")[1];
                    }
                    if (sfcs.endsWith("00")) {
                        s = s + "  cj.sfcs like '%" + sfcs.replace("00", "") + "%' or ";
                    } else {
                        s = s + "  cj.sfcs='" + sfcs + "' or ";
                    }
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

//处理结果内容
            if (data.containsKey("cljgnr") && data.getString("cljgnr").length() > 0) {
                String cljgnr = data.getString("cljgnr");
                sql = sql + " and cj.cljgnr like '%" + cljgnr + "%' ";
            }

//处警详址
            if (data.containsKey("cjxz") && data.getString("cjxz").length() > 0) {
                String cjxz = data.getString("cjxz");
                sql = sql + " and cj.cjxz like '%" + cjxz + "%' ";
            }

//处警结果
            if (data.containsKey("cjjg") && data.getString("cjjg").length() > 0) {
                String lbs = data.getString("cjjg");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjg = cone.getKey();


                    if (cjjg.contains("-")) {
                        cjjg = cjjg.split("\\-")[1];
                    }
                    s = s + "  cj.cjjg='" + cjjg + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //事发时间下限开始
            if (data.containsKey("sfsjxx_start") && data.getString("sfsjxx_start").length() > 0) {
                String sfsjxx_start = data.getString("sfsjxx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time>='" + sfsjxx_start + "' ";
            }

            //事发时间下限结束
            if (data.containsKey("sfsjxx_end") && data.getString("sfsjxx_end").length() > 0) {
                String sfsjxx_end = data.getString("sfsjxx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjxx_time<='" + sfsjxx_end + "' ";
            }

            //事发时间上限开始
            if (data.containsKey("sfsjsx_start") && data.getString("sfsjsx_start").length() > 0) {
                String sfsjsx_start = data.getString("sfsjsx_start").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time>='" + sfsjsx_start + "' ";
            }

            //事发时间上限结束
            if (data.containsKey("sfsjsx_end") && data.getString("sfsjsx_end").length() > 0) {
                String sfsjsx_end = data.getString("sfsjsx_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and cj.sfsjsx_time<='" + sfsjsx_end + "' ";
            }

            //处警登记时间开始
            if (data.containsKey("djsj_start") && data.getString("djsj_start").length() > 0) {
                String djsj_start = data.getString("djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time>='" + djsj_start + "' ";
            }

            //处警登记时间结束
            if (data.containsKey("djsj_end") && data.getString("djsj_end").length() > 0) {
                String djsj_end = data.getString("djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.djsj_time<='" + djsj_end + "' ";
            }

            //处警修改时间开始
            if (data.containsKey("cj_xgsj_start") && data.getString("cj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("cj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time>='" + xgsj_start + "' ";
            }

            //处警修改时间结束
            if (data.containsKey("cj_xgsj_end") && data.getString("cj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("cj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and cj.xgsj_time<='" + xgsj_end + "' ";
            }


            //处警标识
            if (data.containsKey("cjbs") && data.getString("cjbs").length() > 0) {
                String lbs = data.getString("cjbs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjbs = cone.getKey();

                    if (cjbs.contains("-")) {
                        cjbs = cjbs.split("\\-")[1];
                    }
                    s = s + " jj.cjbs='" + cjbs + "' or ";

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警报警人
            if (data.containsKey("bjr") && data.getString("bjr").length() > 0) {
                String bjr = data.getString("bjr");
                sql = sql + " and jj.bjr like '%" + bjr + "%' ";
            }

            //报警类型
            if (data.containsKey("bjlx") && data.getString("bjlx").length() > 0) {
                String lbs = data.getString("bjlx");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjlx = cone.getKey();


                    if (bjlx.contains("-")) {
                        bjlx = bjlx.split("\\-")[1];
                    }
                    if (bjlx.endsWith("0000")) {
                        s = s + " jj.bjlx like '" + bjlx.replace("0000", "") + "%' or ";
                    } else if (bjlx.endsWith("00")) {
                        s = s + "  jj.bjlx like '" + bjlx.replace("00", "") + "%' or ";
                    } else {

                        s = s + " jj.bjlx='" + bjlx + "' or ";
                    }

                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }

            //接警单位
            String unit = "";
            if (data.containsKey("jjdw") && data.getString("jjdw").length() > 0) {
                unit = data.getString("jjdw");

            } else {
                unit = data.getString("unit");

            }
            List<String> us = RIUtil.HashToList(RIUtil.StringToList(unit));
            String sq = "";
            for (int i = 0; i < us.size(); i++) {
                String u = us.get(i);
                int type = 23;
                try {
                    type = RIUtil.dicts.get(u).getInteger("type");
                } catch (Exception ex) {
                    log.warn(u);
                    try {
                        log.warn(String.valueOf(RIUtil.dicts.get(u)));
                    } catch (Exception e) {

                    }
                }
                if (type == 21 || type == 22 || type == 27) {
                    // sql = sql + " and jj.jjdw like '%" + unit.substring(0, 4) + "%'";
                } else if (type == 23 || type == 24) {
                    //sq = sq + "  jj.jjdw like '" + u.substring(0, 6) + "%' or ";
                } else if (type == 26 || type == 25) {

                    sq = sq + "  jj.jjdw like '" + u.substring(0, 8) + "%' or ";
                } else {
                    sq = sq + "  jj.jjdw='" + u + "' or ";
                }
            }
            if (sq.endsWith("or ")) {
                sq = sq.substring(0, sq.length() - 3);
                sql = sql + " and (" + sq + ") ";
            }


            //接警报警人联系电话
            if (data.containsKey("lxdh") && data.getString("lxdh").length() > 0) {
                String lxdh = data.getString("lxdh");
                sql = sql + " and jj.lxdh like '%" + lxdh + "%' ";
            }

            //报警内容
            if (data.containsKey("bjnr") && data.getString("bjnr").length() > 0) {
                String bjnr = data.getString("bjnr");
                sql = sql + " and jj.bjnr like '%" + bjnr + "%' ";
            }

            //发生地点
            if (data.containsKey("sfdd") && data.getString("sfdd").length() > 0) {
                String sfdd = data.getString("sfdd");
                sql = sql + " and jj.sfdd like '%" + sfdd + "%' ";
            }

            //报警方式
            if (data.containsKey("bjxs") && data.getString("bjxs").length() > 0) {
                String lbs = data.getString("bjxs");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bjxs = cone.getKey();


                    if (bjxs.contains("-")) {
                        bjxs = bjxs.split("\\-")[1];
                    }
                    s = s + "  jj.bjxs='" + bjxs + "' or ";

                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
            }

            //接警登记时间开始
            if (data.containsKey("jj_djsj_start") && data.getString("jj_djsj_start").length() > 0) {
                String djsj_start = data.getString("jj_djsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time>='" + djsj_start + "' ";
            }

            //接警登记时间结束
            if (data.containsKey("jj_djsj_end") && data.getString("jj_djsj_end").length() > 0) {
                String djsj_end = data.getString("jj_djsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.djsj_time<='" + djsj_end + "' ";
            }

            //接警修改时间开始
            if (data.containsKey("jj_xgsj_start") && data.getString("jj_xgsj_start").length() > 0) {
                String xgsj_start = data.getString("jj_xgsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time>='" + xgsj_start + "' ";
            }

            //接警修改时间结束
            if (data.containsKey("jj_xgsj_end") && data.getString("jj_xgsj_end").length() > 0) {
                String xgsj_end = data.getString("jj_xgsj_end").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.xgsj_time<='" + xgsj_end + "' ";
            }

            //接警日期时间开始
            if (data.containsKey("jjrqsj_start") && data.getString("jjrqsj_start").length() > 0) {
                String jjrqsj_start = data.getString("jjrqsj_start").replace("-", "").replace(":", "").replace(" ", "");
                sql = sql + " and jj.bjdhsj_time>='" + jjrqsj_start + "' ";
            }

            //接警日期时间结束
            if (data.containsKey("jjrqsj_end") && data.getString("jjrqsj_end").length() > 0) {
                String jjrqsj_end = data.getString("jjrqsj_end").replace("-", "").replace(":", "").replace(" ", "");
                ;
                sql = sql + " and jj.bjdhsj_time<='" + jjrqsj_end + "' ";
            }

            //涉警人员身份证
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                String gmsfhm = data.getString("gmsfhm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm like '%" + gmsfhm + "%') ";
            }

            //涉警人员姓名
            if (data.containsKey("xm") && data.getString("xm").length() > 0) {
                String xm = data.getString("xm");
                sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where xm like '%" + xm + "%') ";
            }

            //涉警类别
            if (data.containsKey("sjlb") && data.getString("sjlb").length() > 0) {
                String lbs = data.getString("sjlb");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String sjlb = cone.getKey();
                    if (sjlb.contains("-")) {
                        sjlb = sjlb.split("\\-")[1];
                    }
                    s = s + " cj.jjbh in (select jjbh from wjsc_jq_sjxx where sjlb ='" + sjlb + "') or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //反馈时间

            String fksql = " ";
            if (data.containsKey("fk_time_start") && data.getString("fk_time_start").length() > 0) {
                String fk_time_start = data.getString("fk_time_start");

                fksql = fksql + "and create_time >='" + fk_time_start + "' ";
            }

            if (data.containsKey("fk_time_end") && data.getString("fk_time_end").length() > 0) {
                String fk_time_end = data.getString("fk_time_end");

                fksql = fksql + "and create_time <='" + fk_time_end + "' ";
            }
            if (fksql.length() > 5) {
                fksql = "select jjbh from jq_fk where 1=1 " + fksql;
                sql = sql + " and cj.jjbh in (" + fksql + ")";
            }


            //反馈人
            if (data.containsKey("fk_user") && data.getString("fk_user").length() > 0) {
                String fk_user = data.getString("fk_user");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where fkr ='" + fk_user + "') ";
            }
            //反馈单位
            if (data.containsKey("fk_unit") && data.getString("fk_unit").length() > 0) {
                String fk_unit = data.getString("fk_unit");

                sql = sql + " and cj.jjbh in (select jjbh from jq_fk where unit ='" + fk_unit + "') ";
            }
            //省厅标签dict:75
            if (data.containsKey("cjjqbq") && data.getString("cjjqbq").length() > 0) {
                String lbs = data.getString("cjjqbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String cjjqbq = cone.getKey();

                    if (cjjqbq.contains("-")) {
                        cjjqbq = cjjqbq.split("\\-")[1];
                    }
                    s = s + "  cjjqbq like '%" + cjjqbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //分局标签dict:76
            if (data.containsKey("fjbq") && data.getString("fjbq").length() > 0) {
                String lbs = data.getString("fjbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String fjbq = cone.getKey();

                    if (fjbq.contains("-")) {
                        fjbq = fjbq.split("\\-")[1];
                    }
                    s = s + "  fjbq like '%" + fjbq + "%' or ";
                }
                s = s.substring(0, s.length() - 3);
                sql = sql + " and (" + s + ") ";
            }
            //标注标签dict:10
            if (data.containsKey("bzbq") && data.getString("bzbq").length() > 3) {
                String lbs = data.getString("bzbq");
                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
                String s = "";
                String ps = "";
                for (Map.Entry<String, String> cone : lbss.entrySet()) {
                    String bzbq = cone.getKey();

                    if (bzbq.contains("-")) {
                        bzbq = bzbq.split("\\-")[1];
                    }
                    int t = RIUtil.dicts.get(bzbq).getInteger("type");
                    if (t != 4) {
                        s = s + "  jqbz like '%" + bzbq + "%' or ";
                    } else {
                        ps = ps + "  personMs like '%" + bzbq + "%' or ";
                    }
                }
                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                    sql = sql + " and (" + s + ") ";
                }
                if (ps.length() > 3) {
                    ps = ps.substring(0, ps.length() - 3);
                    sql = sql + " and cj.jjbh in (select jjbh from wjsc_jq_sjxx where  " + ps + ")";
                }
            }

            //标注状态
            if (data.containsKey("bzzt") && data.getString("bzzt").length() > 0) {
                String is_mark = data.getString("bzzt");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //是否标注
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                String is_mark = data.getString("mark");
                sql = sql + " and mark = '" + is_mark + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_start") && data.getString("bz_time_start").length() > 0) {
                String bz_time_start = data.getString("bz_time_start");
                sql = sql + " and bz_time>='" + bz_time_start + "'  ";
            }
            //标注时间
            if (data.containsKey("bz_time_end") && data.getString("bz_time_end").length() > 0) {
                String bz_time_end = data.getString("bz_time_end");
                sql = sql + " and bz_time<='" + bz_time_end + "'  ";
            }
            //标注人
            if (data.containsKey("bzr") && data.getString("bzr").length() > 0) {
                String bzr = data.getString("bzr");
                sql = sql + " and bzrxm  like '%" + bzr + "%'  ";
            }


            //是否关注
            if (data.containsKey("is_gz") && data.getString("is_gz").length() > 0) {
                String is_gz = data.getString("is_gz");
                if (is_gz.equals("1")) {
                    sql =
                            sql + " and jj.jjbh in (select jjbh from jq_gz where isdelete=0 and create_user='" + data.getString("opt_user") + "')";
                }

            }


            //审批人
            if (data.containsKey("spr") && data.getString("spr").length() > 0) {
                String spr = data.getString("spr");
                sql = sql + " and sprxm like  '%" + spr + "%'  ";
            }
            //审批结果
            if (data.containsKey("spjg") && data.getString("spjg").length() > 0) {
                String spjg = data.getString("spjg");
                sql = sql + " and spjg =  '" + spjg + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_start") && data.getString("spsj_start").length() > 0) {
                String spsj_start = data.getString("spsj_start");
                sql = sql + " and sp_time>='" + spsj_start + "'  ";
            }
            //审批时间
            if (data.containsKey("spsj_end") && data.getString("spsj_end").length() > 0) {
                String spsj_end = data.getString("spsj_end");
                sql = sql + " and sp_time<='" + spsj_end + "'  ";
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select cj.jjbh,jj.bjdhsj_time ,jjdwmc,bjlx,cjlb,cjjg,cljgnr,sfcs, spjg,cjjqbq,bzzt,mark," +
                            "addressM," + "personM,resultM,timeM,toolM,reasonM" + " " + "from" + " " + "v_jq_cjxx_bz "
                            + " " + "cj " + "LEFT JOIN  " + "wjsc_jq_jjxx jj on cj.jjbh=jj.jjbh  " + "where " + "1=1"
                            + "  " + sql + "  " + "order by " + "bjdhsj_time  ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespJQBZList> d = RelaInfoList(ret, data.getString("opt_user"));
                sqls = "select count(cj.jjbh) as count from wjsc_jq_jjxx jj left join v_jq_cjxx_bz cj on cj" + ".jjbh"
                        + "=jj" + ".jjbh " + "where " + "1=1 " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    //---------------------------RELA.INFO----------------------------
    private List<RespJQBZList> RelaInfoList(JSONArray ret, String opt_user) {

        List<RespJQBZList> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String create_user = one.getString("create_user");
            JSONObject userJson = SsoCache.me.cacheData.getJSONObject("user").getJSONObject(create_user);
            JSONObject org = SsoCache.me.cacheData.getJSONObject("organization").getJSONObject(one.getString("unit"));
            one.put("_create_user", userJson);
            one.put("_unit", org);
            if (one.containsKey("bjdhsj_time") && one.getString("bjdhsj_time") != null && one.getString("bjdhsj_time").length() > 0) {
                try {
                    one.put("bjdhsj_time", RIUtil.get_Time(one.getString("bjdhsj_time")));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjlx") && one.getString("bjlx") != null && one.getString("bjlx").length() > 0) {
                try {
                    one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }

            if (one.containsKey("sszrq") && one.getString("sszrq") != null && one.getString("sszrq").length() > 0) {
                try {
                    one.put("sszrq", RIUtil.dicts.get(one.getString("sszrq")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            String eventId = one.getString("eventId");
            if (StringUtils.isNotBlank(eventId)) {
                try {
                    WjEventLabel wjEventLabel = wjEventLabelMapper.selectOne(Wrappers.<WjEventLabel>lambdaQuery()
                            .eq(WjEventLabel::getId, eventId));
                    one.put("eventName", wjEventLabel == null ? eventId : wjEventLabel.getEventName());
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjlb") && one.getString("cjlb") != null && one.getString("cjlb").length() > 0) {
                try {
                    one.put("cjlb", RIUtil.dicts.get("51-" + one.getString("cjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjjg") && one.getString("cjjg") != null && one.getString("cjjg").length() > 0) {
                try {
                    one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("sfcs") && one.getString("sfcs") != null && one.getString("sfcs").length() > 0) {
                try {
                    one.put("sfcs", RIUtil.dicts.get("74-" + one.getString("sfcs")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            int count =
                    jdbcTemplate.queryForObject("select count(id) as count from jq_gz where isdelete=0 " + "and " +
                            "create_user='" + opt_user + "' and jjbh='" + one.getString("jjbh") + "'", Integer.class);
            if (count > 0) {
                one.put("is_gz", 1);
            } else {
                one.put("is_gz", 0);
            }


            JSONArray jqbzs = new JSONArray();
            try {
                List<String> adds = RIUtil.HashToList(RIUtil.StringToList(one.getString("addressM")));
                JSONArray aMs = new JSONArray();
                for (int a = 0; a < adds.size(); a++) {
                    String id = adds.get(a);
                    JSONObject aone = RIUtil.jqbqs.get(id);
                    String memo = aone.getString("memo");

                    if (aone != null) {
                        jqbzs.add(aone);
                        if (memo.contains("发生部位")) {
                            aone.put("name", aone.getString("dict_name"));
                            aMs.add(aone);
                        }
                    }
                }

                one.put("_addressM", aMs);
            } catch (Exception ex) {

            }
            try {
                //todo
                String sql = "select personM from wjsc_jq_sjxx where jjbh='" + one.getString("jjbh") + "' and personM is not null and personM != '' ";

                List<Map<String, Object>> pMs = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 1000);
                if (pMs.size() > 0) {
                    JSONArray pmss = RIUtil.ListMap2jsa(pMs);

                    String pms = "";
                    for (int p = 0; p < pmss.size(); p++) {
                        String personM = pmss.getJSONObject(p).getString("personM");
                        if (!pms.contains(personM)) {
                            pms = pms + personM + ",";
                        }
                    }


                    List<String> adds = RIUtil.HashToList(RIUtil.StringToList(pms));
                    JSONArray perMs = new JSONArray();
                    for (int a = 0; a < adds.size(); a++) {
                        String id = adds.get(a);
                        JSONObject aone = RIUtil.jqbqs.get(id);
                        if (aone != null) {
                            String memo = aone.getString("memo");
                            jqbzs.add(aone);
                            if (memo.contains("职业")) {
                                aone.put("name", aone.getString("dict_name"));
                                perMs.add(aone);
                            }
                        }
                    }

                    one.put("_personM", perMs);

                }
            } catch (Exception ex) {

            }

            try {
                List<String> adds = RIUtil.HashToList(RIUtil.StringToList(one.getString("resultM")));
                one.put("_resultM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("resultM"))));
                for (int a = 0; a < adds.size(); a++) {
                    String id = adds.get(a);
                    JSONObject aone = RIUtil.jqbqs.get(id);
                    jqbzs.add(aone);
                }
            } catch (Exception ex) {

            }
            try {
                List<String> adds = RIUtil.HashToList(RIUtil.StringToList(one.getString("toolM")));
                one.put("_toolM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("toolM"))));
                for (int a = 0; a < adds.size(); a++) {
                    String id = adds.get(a);
                    JSONObject aone = RIUtil.jqbqs.get(id);
                    jqbzs.add(aone);
                }

            } catch (Exception ex) {

            }
            try {
                List<String> adds = RIUtil.HashToList(RIUtil.StringToList(one.getString("reasonM")));
                one.put("_reasonM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("reasonM"))));
                for (int a = 0; a < adds.size(); a++) {
                    String id = adds.get(a);
                    JSONObject aone = RIUtil.jqbqs.get(id);
                    jqbzs.add(aone);
                }

            } catch (Exception ex) {

            }
            try {
                JSONObject aone = RIUtil.jqbqs.get(one.getString("timeM"));
                one.put("_timeM", RIUtil.dicts.get(one.getString("timeM")));

                if (aone != null) {
                    jqbzs.add(aone);
                }
            } catch (Exception ex) {
            }
            one.put("jqbzs", jqbzs);

            RespJQBZList dd = gson.fromJson(String.valueOf(one), RespJQBZList.class);
            back.add(dd);
        }
        return back;
    }

    private JSONArray RelaInfoListExp(JSONArray ret, String opt_user, int isExp) {

        JSONArray back = new JSONArray();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            if (StringUtils.isNotBlank(one.getString("sszrq"))) {
                try {
                    one.put("dzsq", RIUtil.dicts.get(one.getString("sszrq")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }

            if (one.containsKey("bjdhsj_time") && one.getString("bjdhsj_time").length() > 0) {
                try {
                    one.put("bjdhsj_time", RIUtil.get_Time(one.getString("bjdhsj_time")));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("djsj_time") && one.getString("djsj_time").length() > 0) {
                try {
                    one.put("djsj_time", RIUtil.get_Time(one.getString("djsj_time")));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjsj_time") && one.getString("cjsj_time").length() > 0) {
                try {
                    one.put("cjsj_time", RIUtil.get_Time(one.getString("cjsj_time")));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjlx") && one.getString("bjlx") != null && one.getString("bjlx").length() > 0) {
                try {
                    one.put("bjlx", RIUtil.dicts.get("51-" + one.getString("bjlx")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("bjxs") && one.getString("bjxs") != null && one.getString("bjxs").length() > 0) {
                try {
                    one.put("bjxs", RIUtil.dicts.get("45-" + one.getString("bjxs")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjlb") && one.getString("cjlb") != null && one.getString("cjlb").length() > 0) {
                try {
                    one.put("cjlb", RIUtil.dicts.get("51-" + one.getString("cjlb")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("cjjg") && one.getString("cjjg") != null && one.getString("cjjg").length() > 0) {
                try {
                    one.put("cjjg", RIUtil.dicts.get("46-" + one.getString("cjjg")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("sfcs") && one.getString("sfcs") != null && one.getString("sfcs").length() > 0) {
                try {
                    one.put("sfcs", RIUtil.dicts.get("74-" + one.getString("sfcs")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            if (one.containsKey("fsyy") && one.getString("fsyy") != null && one.getString("fsyy").length() > 0) {
                try {
                    one.put("fsyy", RIUtil.dicts.get("77-" + one.getString("sfcs")).getString("dict_name"));
                } catch (Exception ex) {
                }
            }
            int count =
                    jdbcTemplate.queryForObject("select count(id) as count from jq_gz where isdelete=0 " + "and " +
                            "create_user='" + opt_user + "' and jjbh='" + one.getString("jjbh") + "'", Integer.class);
            if (count > 0) {
                one.put("is_gz", 1);
            } else {
                one.put("is_gz", 0);
            }


            try {


                HashMap<String, String> adds = RIUtil.StringToList(one.getString("addressM"));
                for (Map.Entry<String, String> ao : adds.entrySet()) {
                    String oneid = ao.getKey();
                    String memo = RIUtil.dicts.get(oneid).getString("memo").split("\\-")[0];
                    String name = RIUtil.dicts.get(oneid).getString("memo");
                    one.put("addressM_" + memo, name);
                }
                one.put("addressM", RIUtil.RealDictNames(RIUtil.StringToList(one.getString("addressM"))));
                System.out.println(one);
            } catch (Exception ex) {
                one.put("addressM", "");
            }
            try {
                String sql = "select personM from wjsc_jq_sjxx where jjbh='" + one.getString("jjbh") + "'";

                List<Map<String, Object>> pMs = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 1000);
                if (pMs.size() > 0) {
                    JSONArray pmss = RIUtil.ListMap2jsa(pMs);

                    String pms = "";
                    for (int p = 0; p < pmss.size(); p++) {
                        String personM = pmss.getJSONObject(p).getString("personM");
                        if (!pms.contains(personM)) {
                            pms = pms + personM + " ";
                        }
                    }


                    List<String> adds = RIUtil.HashToList(RIUtil.StringToList(pms));
                    String perMs = "";
                    for (int a = 0; a < adds.size(); a++) {
                        String id = adds.get(a);
                        JSONObject aone = RIUtil.jqbqs.get(id);
                        String name = "";
                        if (isExp == 1) {
                            name = aone.getString("memo");
                        } else {
                            name = aone.getString("dict_name");
                        }
                        perMs = perMs + name + ",";
                        String memo = aone.getString("memo").split("\\-")[0];
                        one.put("personM_" + memo, name);

                    }

                    one.put("personM", perMs);

                }
            } catch (Exception ex) {
                one.put("personM", "");
            }

            try {

                if (isExp == 1) {

                    HashMap<String, String> adds = RIUtil.StringToList(one.getString("resultM"));
                    for (Map.Entry<String, String> ao : adds.entrySet()) {
                        String oneid = ao.getKey();
                        String memo = RIUtil.dicts.get(oneid).getString("memo").split("\\-")[0];
                        String name = RIUtil.dicts.get(oneid).getString("memo");
                        one.put("resultM_" + memo, name);
                    }
                    one.put("resultM", RIUtil.RealDictMemoNames(RIUtil.StringToList(one.getString("resultM"))));
                } else {
                    one.put("resultM", RIUtil.RealDictNames(RIUtil.StringToList(one.getString("resultM"))));
                }
            } catch (Exception ex) {
                one.put("resultM", "");
            }
            try {

                if (isExp == 1) {

                    one.put("toolM", RIUtil.RealDictMemoNames(RIUtil.StringToList(one.getString("toolM"))));
                } else {
                    one.put("toolM", RIUtil.RealDictNames(RIUtil.StringToList(one.getString("toolM"))));
                }


            } catch (Exception ex) {
                one.put("toolM", "");
            }
            try {

                if (isExp == 1) {

                    one.put("reasonM", RIUtil.RealDictMemoNames(RIUtil.StringToList(one.getString("reasonM"))));
                } else {
                    one.put("reasonM", RIUtil.RealDictNames(RIUtil.StringToList(one.getString("reasonM"))));
                }

            } catch (Exception ex) {
                one.put("reasonM", "");
            }
            try {


                if (isExp == 1) {
                    one.put("timeM", RIUtil.RealDictMemoNames(RIUtil.StringToList(one.getString("timeM"))));
                } else {
                    one.put("timeM", RIUtil.RealDictNames(RIUtil.StringToList(one.getString("timeM"))));
                }
            } catch (Exception ex) {
                one.put("timeM", "");
            }
            // one.put("jqbzs", jqbzs);


            back.add(one);
        }
        return back;
    }


    @PostMapping("/det")
    @ApiOperation(value = "标注明细")

    public R<RespJQBZ> detbz(@Valid @RequestBody ReqJQBZDel params) {

        Gson gson = new Gson();

        JSONObject data = JSONObject.parseObject(gson.toJson(params));

        return getDet(data);

    }

    private R<RespJQBZ> getDet(JSONObject data) {

        try {
            String jjbh = "";
            //处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                jjbh = data.getString("jjbh");
            } else {
                return R.fail("缺少jjbh");
            }

            String sql =
                    "select jjbh,bzzt,addressM,personM,resultM,timeM,toolM,reasonM,unit, jgbh,spjg,spr," + "sp_time," +
                            "spnr," +
                            "bzr," + "bz_time,mark,baidu,dwmc,dwdz,dzid,jgbh,dsdz,dz_type,eventId,sszrq from wjsc_jq_cjxx where " +
                            "jjbh='" + jjbh + "'";
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 999);

            JSONArray ret = RIUtil.ListMap2jsa(list);
            List<RespJQBZ> d = RelaInfo(ret);


            OracleHelper ora = null;
            try {
                ora = new OracleHelper("");
                for (RespJQBZ item : d) {
                    String jgbh = item.getJgbh();
                    String dwmc = item.getDwmc();
                    if (StringUtils.isBlank(jgbh) || StringUtils.isNotBlank(dwmc)) continue;
                    String sql4Ora = "select jgbh,dwmc,sjjymc,dwdzmc,dzid,dwdzmc from jwry_dba.czjg_jbxx@qjjc_sydw where jgbh = '" + jgbh + "'";
                    List<JSONObject> query = ora.query(sql4Ora);
                    if (!CollectionUtils.isEmpty(query)) {
                        JSONObject jsonObject = query.get(0);
                        dwmc = jsonObject.getString("DWMC");
                        String dzid = jsonObject.getString("DZID");
                        String dwdzmc = jsonObject.getString("DWDZMC");
                        wjscJqCjxxMapper.update(Wrappers.<WjscJqCjxx>lambdaUpdate()
                                .set(WjscJqCjxx::getDwmc, dwmc)
                                .set(WjscJqCjxx::getDzid, dzid)
                                .set(WjscJqCjxx::getDwdz, dwdzmc)
                                .eq(WjscJqCjxx::getJjbh, jjbh));
                        item.setDwmc(dwmc);
                        item.setDzid(dzid);
                        item.setDwdz(dwdzmc);
                    }
                }
            } catch (Exception ex) {
                log.error(Lib.getTrace(ex));
                return R.fail(Lib.getTrace(ex));
            } finally {
                ora.close();
            };

            RespJQBZ one = d.get(0);
            return R.ok(one);

        } catch (Exception ex) {

            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }

    }


    //---------------------------CREATE----------------------------
    @PostMapping("/jqgz")
    @ApiOperation(value = "创建警情关注")

    public R<Object> create_jqgz(@Valid @RequestBody ReqJQgzCreate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        System.out.println(user);

        System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("create_user", user.getIdCard());
        data.put("name", user.getName());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        return createJQgz(data);
        //return R.ok();
    }

    private R<Object> createJQgz(JSONObject data) {

        try {
            int id = 0;
            String reason = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String jjbh = "";

            //关注原因
            if (data.containsKey("reason") && data.getString("reason").length() > 0) {
                reason = data.getString("reason");
            }
            //创建人
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
            }
            //jjbh
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
            } else {
                return R.fail("缺少jjbh");
            }
            String sqls = "insert jq_gz (id,reason,create_user,create_time,jjbh) values('" + id + "','" + reason +
                    "','" + create_user + "','" + create_time + "','" + jjbh + "')";
            log.warn(sqls);
            jdbcTemplate.update(sqls);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
        return R.ok();
    }


    //---------------------------delete----------------------------
    @PostMapping("/jqqxgz")
    @ApiOperation(value = "删除警情关注")

    public R<Object> delete(@Valid @RequestBody ReqJQgzDel params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());

        String jjbh = data.getString("jjbh");
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String sql = "delete from jq_gz where " + "jjbh='" + jjbh + "' and create_user='" + user.getIdCard() + "'";
        jdbcTemplate.update(sql);
        return R.ok();
    }

    //---------------------------query----------------------------
    @PostMapping("/jqgz_list")
    @ApiOperation(value = "警情关注列表")

    public R<List<RespJQgz>> list_jqgz(@Valid @RequestBody ReqJQgzQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getJQgz(data);
        //return R.ok();
    }

    private R<List<RespJQgz>> getJQgz(JSONObject data) {
        try {
            String sql = "";
            int page = 1;
            int limit = 20;


            //jjbh
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                String jjbh = data.getString("jjbh");
                sql = sql + " and jjbh = '" + jjbh + "'  ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from jq_gz where isdelete=0  " + sql + "  order by create_time desc ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespJQgz> d = RelaInfoGZ(ret);
                sqls = "select count(id) as count from jq_gz where isdelete=0 " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    //---------------------------RELA.INFO----------------------------
    private List<RespJQgz> RelaInfoGZ(JSONArray ret) {

        List<RespJQgz> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String create_user = one.getString("create_user");
            JSONObject userJson = SsoCache.me.cacheData.getJSONObject("user").getJSONObject(create_user);

            one.put("_create_user", userJson);
            JSONObject unit = userJson.getJSONArray("organization").getJSONObject(0);

            one.put("_unit", unit);

            RespJQgz dd = gson.fromJson(String.valueOf(one), RespJQgz.class);
            back.add(dd);
        }
        return back;
    }

    @PostMapping("/baidu")
    @ApiOperation(value = "获取百度标签")

    public R<RespJQBZ> getBaidu(@Valid @RequestBody ReqJQBZDel params) {

        String jjbh = params.getJjbh();
        JSONObject baidus = GetBaidu(jjbh);

        RespJQBZ det = new RespJQBZ();
        det.setBaidu(baidus);
        return R.ok(det);

    }

    private JSONObject GetBaidu(String jjbh) {
        JSONObject baidus = new JSONObject();
        try {
            String sql = "select cljgnr from wjsc_jq_cjxx where jjbh='" + jjbh + "'";
            JSONObject nrs = RIUtil.queryOne(sql, jdbcTemplate);
            String cljg = nrs.getString("cljgnr");

            JSONObject det = new JSONObject();
            det.put("content", cljg);
            det.put("caseType", "警情");
            det.put("text", cljg);
            String url = "http://*************:8002/case_admin/quick_analysis";
            String ret = GetHttps(det, url);
            //System.out.println(ret);

            JSONObject rett = JSONObject.parseObject(ret);
            if (rett.containsKey("code") && rett.getIntValue("code") == 0 && rett.containsKey("data")) {

                JSONObject data = rett.getJSONObject("data");
                System.out.println(data);
                if (data.containsKey("caseCategory")) {
                    String degree = data.getString("confidentDegree").substring(2, 4) + "%";
                    baidus.put("reasonM", data.getString("caseCategory") + "(" + degree + ")");
                }


                // addressM time  result

                if (data.containsKey("timeSpaceClue") && data.getString("timeSpaceClue") != null
                        && data.getString("timeSpaceClue").length() > 0) {
                    System.out.println(data.getString("timeSpaceClue"));
                    JSONObject tsc = data.getJSONObject("timeSpaceClue");
                    String addM = "";
                    String timeM = "";
                    String resultM = "";


                    if (tsc.containsKey("happenPeriod")) {
                        JSONArray hp = tsc.getJSONArray("happenPeriod");
                        String hps = "";
                        for (int a = 0; a < hp.size(); a++) {
                            //System.out.println(hp.get(a));
                            hps = hps + hp.getString(a) + " ";
                        }
                        timeM = timeM + "发生时段:" + hps + ";";
                    }
                    if (tsc.containsKey("baseAddr")) {
                        String baseAddr = tsc.getString("baseAddr");
                        addM = addM + "作案地址:" + baseAddr + ";";
                    }

                    if (tsc.containsKey("areaType")) {
                        String areaType = tsc.getString("areaType");
                        addM = addM + "场所分类:" + areaType + ";";
                    }
                    if (tsc.containsKey("eventTimes")) {
                        JSONArray et = tsc.getJSONArray("eventTimes");
                        String ets = "";
                        for (int a = 0; a < et.size(); a++) {
                            //System.out.println(hp.get(a));
                            ets = ets + et.getString(a) + " ";
                        }

                        timeM = timeM + "案发时间:" + ets + ";";
                    }
                    if (tsc.containsKey("insideFlag")) {
                        boolean insideFlag = tsc.getBooleanValue("insideFlag");
                        if (insideFlag) {
                            addM = addM + "是否建筑物内:是;";
                        } else {
                            addM = addM + "是否建筑物内:否;";
                        }
                    }
                    int perpetratorNum = tsc.getIntValue("perpetratorNum");
                    if (perpetratorNum > 0) {
                        resultM = resultM + "作案人数:" + perpetratorNum + ";";
                    }

                    int hurtNum = tsc.getIntValue("hurtNum");
                    if (hurtNum > 0) {
                        resultM = resultM + "受伤人数:" + hurtNum + ";";
                    }

                    int deadNum = tsc.getIntValue("deadNum");
                    if (deadNum > 0) {
                        resultM = resultM + "死亡人数:" + deadNum + ";";
                    }


                    baidus.put("addressM", addM);
                    baidus.put("timeM", timeM);
                    baidus.put("resultM", resultM);

                }

                //手段

                url = "http://*************:36000/shouduan/predict";
                ret = GetHttps(det, url);
                //System.out.println(ret);
                try {
                    JSONObject rets = JSONObject.parseObject(ret);
                    if (rets.containsKey("error_code") && rets.getIntValue("error_code") == 0 && rets.containsKey("result")) {
                        JSONObject datas = rets.getJSONObject("result");
                        if (datas.containsKey("pred_results")) {
                            JSONArray results = datas.getJSONArray("pred_results");
                            if (results.size() > 0) {
                                String tool = results.getJSONObject(0).getString("pred_label");
                                String tdg = results.getJSONObject(0).getString("pred_prob").substring(2, 4) + "%";
                                baidus.put("toolM", tool + "(" + tdg + ")");
                            }

                        }


                    } else {
                        log.error(ret);
                    }
                } catch (Exception e) {
                    System.out.println(Lib.getTrace(e));
                }

            } else {
                log.error(rett.toString());
            }
            log.warn(jjbh + "-->" + baidus);

            sql = "update wjsc_jq_cjxx set baidu='" + baidus + "' where jjbh='" + jjbh + "'";
            jdbcTemplate.update(sql);


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
        }
        return baidus;
    }

    private String GetHttps(JSONObject det, String url) {

        try {
            OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(60 * 5, TimeUnit.SECONDS)
                    .readTimeout(60 * 5, TimeUnit.SECONDS).writeTimeout(60 * 5, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("application/json");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, String.valueOf(det));
            Request request = new Request.Builder().url(url)
                    .method("POST", body).addHeader("Content-Type", "application/json").build();
            Response response = client.newCall(request).execute();

            String ret = response.body().string();
            log.warn(ret);
            return ret;
        } catch (Exception e) {
            log.error("-->" + Lib.getTrace(e));
        }
        return "";

    }

    private String GetContent(String fileName) {
        log.warn(fileName);
        String n[] = fileName.split("\\.");
        String suffix = n[1];
        if (fileName.contains("ai")) {
            return "application/postscript";
        } else if (fileName.contains("eps")) {
            return "application/postscript";
        } else if (fileName.contains("exe")) {
            return "application/octet-stream";
        } else if (fileName.contains("doc")) {
            return "application/vnd.ms-word";
        } else if (fileName.contains("xls")) {
            return "application/vnd.ms-excel";
        } else if (fileName.contains("xlsx")) {
            return "application/vnd.ms-excel";
        } else if (fileName.contains("ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (fileName.contains("pps")) {
            return "application/vnd.ms-powerpoint";
        } else if (fileName.contains("pdf")) {
            return "application/pdf";
        } else if (fileName.contains("xml")) {
            return "application/xml";
        } else if (fileName.contains("odt")) {
            return "application/vnd.oasis.opendocument.text";
        } else if (fileName.contains("swf")) {
            return "application/x-shockwave-flash";
        } else if (fileName.contains("gz")) {
            return "application/x-gzip";
        } else if (fileName.contains("tgz")) {
            return "application/x-gzip";
        } else if (fileName.contains("bz")) {
            return "application/x-bzip2";
        } else if (fileName.contains("bz2")) {
            return "application/x-bzip2";
        } else if (fileName.contains("tbz")) {
            return "application/x-bzip2";
        } else if (fileName.contains("zip")) {
            return "application/zip";
        } else if (fileName.contains("rar")) {
            return "application/x-rar";
        } else if (fileName.contains("tar")) {
            return "application/x-tar";
        } else if (fileName.contains("7z")) {
            return "application/x-7z-compressed";
        } else if (fileName.contains("txt")) {
            return "text/plain";
        } else if (fileName.contains("php")) {
            return "text/x-php";
        } else if (fileName.contains("html")) {
            return "text/html";
        } else if (fileName.contains("htm")) {
            return "text/html";
        } else if (fileName.contains("js")) {
            return "text/javascript";
        } else if (fileName.contains("css")) {
            return "text/css";
        } else if (fileName.contains("rtf")) {
            return "text/rtf";
        } else if (fileName.contains("rtfd")) {
            return "text/rtfd";
        } else if (fileName.contains("py")) {
            return "text/x-python";
        } else if (fileName.contains("java")) {
            return "text/x-java-source";
        } else if (fileName.contains("rb")) {
            return "text/x-ruby";
        } else if (fileName.contains("sh")) {
            return "text/x-shellscript";
        } else if (fileName.contains("pl")) {
            return "text/x-perl";
        } else if (fileName.contains("sql")) {
            return "text/x-sql";
        } else if (fileName.contains("bmp")) {
            return "image/x-ms-bmp";
        } else if (fileName.contains("jpg")) {
            return "image/jpeg";
        } else if (fileName.contains("jpeg")) {
            return "image/jpeg";
        } else if (fileName.contains("gif")) {
            return "image/gif";
        } else if (fileName.contains("png")) {
            return "image/png";
        } else if (fileName.contains("tif")) {
            return "image/tiff";
        } else if (fileName.contains("tiff")) {
            return "image/tiff";
        } else if (fileName.contains("tga")) {
            return "image/x-targa";
        } else if (fileName.contains("psd")) {
            return "image/vnd.adobe.photoshop";
        } else if (fileName.contains("mp3")) {
            return "audio/mpeg";
        } else if (fileName.contains("mid")) {
            return "audio/midi";
        } else if (fileName.contains("ogg")) {
            return "audio/ogg";
        } else if (fileName.contains("mp4a")) {
            return "audio/mp4";
        } else if (fileName.contains("wav")) {
            return "audio/wav";
        } else if (fileName.contains("wma")) {
            return "audio/x-ms-wma";
        } else if (fileName.contains("avi")) {
            return "video/x-msvideo";
        } else if (fileName.contains("dv")) {
            return "video/x-dv";
        } else if (fileName.contains("mp4")) {
            return "video/mp4";
        } else if (fileName.contains("mpeg")) {
            return "video/mpeg";
        } else if (fileName.contains("mpg")) {
            return "video/mpeg";
        } else if (fileName.contains("mov")) {
            return "video/quicktime";
        } else {
            return "multipart/form-data";
        }

    }

    private String ExportTables(JSONArray datas, String head, String keys, String name) {

        String path = "";
        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(upload_path + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            exporthelper = new ExportXlsxHelper();
            path = upload_path + filePath + FileName;
            exporthelper.init(path);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            return path;

        } catch (Exception e) {
            log.error(Lib.getTrace(e));
            return "";
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }

    }

    public void initDict() {


        try {


            String sql =
                    "select id,dict_name,permission,color," + "father_id,index_no,type,gadm,remark,is_kh,memo," +
                            "static_index ,isdelete,label_yw from " + "dict " + "where  type is not null and length" + "(permission)>0 order by " + "type,index_no," + "id";
            System.out.println(sql);
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 999999);


            System.out.println(list.size());
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                long start = System.currentTimeMillis();
                //  RIUtil.dicts = new HashMap<>();

                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.getJSONObject(i);
                    if (one.getString("id").equals("26a20011-d093-4827-9062-a1c9e17fd774")) {
                        System.out.println(one);
                    }

                    if (!one.containsKey("dict_name")) {

                        break;
                    }
                    if (!one.containsKey("permission")) {
                        one.put("permission", "");
                    }
                    if (!one.containsKey("label_yw")) {
                        one.put("label_yw", "");
                    }
                    if (!one.containsKey("memo")) {
                        one.put("memo", "");
                    }
                    if (!one.containsKey("color")) {
                        one.put("color", "");
                    }

                    if (!one.containsKey("isdelete")) {
                        one.put("isdelete", "2");
                    }
                    if (!one.containsKey("father_id")) {
                        one.put("father_id", "");
                    }
                    if (!one.containsKey("index_no")) {
                        one.put("index_no", "99");
                    }
                    if (!one.containsKey("type")) {
                        one.put("type", "");
                    }
                    if (!one.containsKey("gadm")) {
                        one.put("gadm", "");
                    }
                    if (!one.containsKey("remark")) {
                        one.put("remark", "");
                    }

                    if (!one.containsKey("static_index")) {
                        one.put("static_index", "0");
                    }

                    RIUtil.dicts.put(one.getString("id"), one);
                    int del = one.getInteger("isdelete");
                    try {
                        if (one.getString("permission").equals("jqbq") && del == 1) {
                            RIUtil.jqbqs.put(one.getString("id"), one);
                        }
                    } catch (Exception ex) {
                        //System.out.println(one);
                    }

                    if (one.getString("type").equals("127") && del == 1) {
                        RIUtil.mzs.put(one.getString("id"), one);
                    }
                    if (one.getString("type").equals("3") && del == 1) {
                        RIUtil.dzbq.put(one.getString("id"), one);
                    }
                    if (one.getString("type").equals("4") && del == 1) {
                        RIUtil.rybq.put(one.getString("id"), one);
                    }

                }
                log.warn("init.dict->" + RIUtil.dicts.size());
                log.warn("init.rybq->" + RIUtil.rybq.size());
                log.warn("init.dzbq->" + RIUtil.dzbq.size());

                long end = System.currentTimeMillis();
                long bt = end - start;
                System.out.println(bt);


            }

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));

        }
    }

    private final static List<String> bzbq_lazy_list = Arrays.asList("3", "4", "6", "7", "8", "9");

    @PostMapping("/bzbqLazy")
    @ApiOperation(value = "标注标签懒加载")
    public R<List<BzbqLazyVo>> bzbqLazy(@Valid @RequestBody BzbqLazyReq req) throws Exception {
        List<Dict> list = new ArrayList<>();
        if (req.getId() == null || "-1".equals(req.getId())) {
           list = dictService.list(Wrappers.<Dict>lambdaQuery()
                   .eq(Dict::getIsdelete, "1")
                   .eq(Dict::getFatherId, "-1")
                   .in(Dict::getType, bzbq_lazy_list)
                   .orderByAsc(Dict::getIndexNo));
       } else {
           list = dictService.lambdaQuery()
                   .eq(Dict::getIsdelete, "1")
                   .eq(Dict::getFatherId, req.getId())
                   .orderByAsc(Dict::getIndexNo)
                   .list();
       }
        if (list.isEmpty()) return R.ok(Collections.emptyList());
        List<String> dictIds = list.stream().map(Dict::getId).collect(Collectors.toList());
        List<Dict> childs4dictIds = dictService.lambdaQuery()
                .eq(Dict::getIsdelete, "1")
                .in(Dict::getFatherId, dictIds)
                .list();
        List<String> hasChildIds = childs4dictIds.stream().map(Dict::getFatherId).distinct().collect(Collectors.toList());
        List<BzbqLazyVo> vo = list.stream().map(entity -> BzbqLazyVo.builder()
                .id(entity.getId())
                .dickName(entity.getDictName())
                .type(entity.getType())
                .fatherId(entity.getFatherId())
                .isdelete(entity.getIsdelete())
                .hasChild(hasChildIds.contains(entity.getId()))
                .build()).collect(Collectors.toList());
        return R.ok(vo);
    }

}

