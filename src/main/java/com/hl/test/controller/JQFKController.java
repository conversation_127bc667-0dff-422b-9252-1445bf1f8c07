package com.hl.test.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;
import com.hl.security.config.sso.cache.SsoCache;

import com.hl.test.Utils.Lib;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.req.ReqJQFKCreate;
import com.hl.test.domain.req.ReqJQFKDel;
import com.hl.test.domain.req.ReqJQFKQuery;
import com.hl.test.domain.req.ReqJQFKUpdate;
import com.hl.test.domain.resp.RespJQFK;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@Api(tags = "警情反馈")
@RequestMapping("/jqfk")
@SuppressWarnings("unchecked")
public class JQFKController {

    @Resource
    private  JdbcTemplate jdbcTemplate;

    @PostMapping("/create")
    @ApiOperation(value = "创建警情反馈")

    public R<Object> create(@Valid @RequestBody ReqJQFKCreate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        System.out.println(user);

        System.out.println(params);

        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("create_user", user.getIdCard());
        data.put("fkr", user.getName());
        JSONArray units = user.getOrganization();
        JSONObject u = units.getJSONObject(0);

        data.put("unit", u.getString("organization_id"));
        return createJQFK(data);
        //return R.ok();
    }

    private R<Object> createJQFK(JSONObject data) {

        try {
            String jjbh = "";
            String content = "";
            String create_user = "";
            String unit = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            //接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
            }
            //反馈内容
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
            }
            //反馈人
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
            }
            //反馈部门
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }

            String sqls =
                    "insert jq_fk (jjbh,content,create_user,unit,create_time,fkr) values('" + jjbh + "','" + content + "','" + create_user + "','" + unit + "','" + create_time + "','" + data.getString("fkr") + "')";
            log.warn(sqls);
            jdbcTemplate.update(sqls);
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
        return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新警情反馈")

    public R<Object> update(@Valid @RequestBody ReqJQFKUpdate params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());
        return updateJQFK(data);
    }

    private R<Object> updateJQFK(JSONObject data) {
        try {
            String sql = "";
            String id = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' , ";

            } else {
                return R.fail("缺少id");
            }

            //反馈内容
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                String content = data.getString("content");
                sql = sql + " content='" + content + "' , ";

            }

            String sqls = "update jq_fk set " + sql + " isdelete=0  where id='" + id + "'";
            System.out.println(sqls);
            jdbcTemplate.update(sqls);
            return R.ok();
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除警情反馈")

    public R<Object> delete(@Valid @RequestBody ReqJQFKDel params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        data.put("opt_user", user.getIdCard());

        String id = data.getString("id");
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String sql = "update jq_fk set isdelete=1 ,delete_user='" + user.getIdCard() + "',delete_time='" + time + "' " +
                " " + "where " + "id='" + id + "'";
        jdbcTemplate.update(sql);
        return R.ok();
    }

    @PostMapping("/list")
    @ApiOperation(value = "警情反馈列表")

    public R<List<RespJQFK>> list(@Valid @RequestBody ReqJQFKQuery params) throws Exception {
        log.warn(params.toString());
        User user = UserUtils.getUserNull();
        Gson gson = new Gson();
        JSONObject data = JSONObject.parseObject(gson.toJson(params));
        return getJQFK(data);

    }

    private R<List<RespJQFK>> getJQFK(JSONObject data) {
        try {
            String sql = "";
            int page = 1;
            int limit = 20;
            //接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                String jjbh = data.getString("jjbh");
                sql = sql + " and jjbh ='" + jjbh + "'  ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from jq_fk where isdelete=0  " + sql + "  order by create_time desc ";
            log.warn(sqls);

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", (page - 1) * limit, limit);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                List<RespJQFK> d = RelaInfo(ret);
                sqls = "select count(id) as count from jq_fk where isdelete=0  " + sql;
                Integer count = jdbcTemplate.queryForObject(sqls, Integer.class);

                return R.ok(d, count);

            } else {
                return R.ok(new ArrayList<>(), 0);
            }


        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
            return R.fail(Lib.getTrace(ex));
        }
    }

    private List<RespJQFK> RelaInfo(JSONArray ret) {

        List<RespJQFK> back = new ArrayList<>();
        Gson gson = new Gson();

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String create_user = one.getString("create_user");
            JSONObject userJson = SsoCache.me.cacheData.getJSONObject("user").getJSONObject(create_user);
            JSONObject org = SsoCache.me.cacheData.getJSONObject("organization").getJSONObject(one.getString("unit"));
            one.put("_create_user", userJson);
            one.put("_unit", org);

            RespJQFK dd = gson.fromJson(String.valueOf(one), RespJQFK.class);
            back.add(dd);
        }
        return back;
    }

}
