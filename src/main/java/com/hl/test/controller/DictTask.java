package com.hl.test.controller;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.test.Utils.Lib;
import com.hl.test.Utils.MysqlHelper;
import com.hl.test.Utils.OracleHelper;
import com.hl.test.Utils.RIUtil;
import com.sun.media.sound.SoftTuning;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@EnableScheduling
@Slf4j

public class DictTask {
    // private static JdbcTemplate jdbcTemplate = null;
    private long lastUpTime = 0L;


    @Resource
    private JdbcTemplate jdbcTemplate;


    //@Scheduled(cron = "*/5 * * * * ? *")
    //或直接设定时间间隔，5秒
    @Scheduled(fixedRate = 1000 * 60 * 5)
    public void someTask() {
        System.out.println("每5分钟同步一次字典: " + System.currentTimeMillis());
        long s = System.currentTimeMillis();
        //计算count
        GetDictCount();
        System.out.println("--cal.count--->" + (System.currentTimeMillis() - s));
        initDict();
        initTree();
    }

    //@Scheduled(cron = "*/5 * * * * ? *")
    //字典变量
    @Scheduled(fixedRate = 1000)
    public void somesTask() {
        try {
            String sql = "select up_time from dict order by up_time desc limit 1";
            String upTime = jdbcTemplate.queryForObject(sql, String.class);
            long up = RIUtil.dateToStamp(upTime);
            // long cuu = System.currentTimeMillis();
            // log.warn(lastUpTime + "-" + up);
            if (up > lastUpTime) {
                GetDictCount();
                initDict();
                initTree();

                lastUpTime = up;
            }
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
        }
    }

    private void initTree() {
        long start = System.currentTimeMillis();
        JSONArray tw1 = RIUtil.GetDictByType_bq(4, RIUtil.rybq);
        List<JSONObject> nodes = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.getJSONObject(i);
            nodes.add(one);
        }

        List<JSONObject> treeNodes = buildTree(nodes, "DB21DC0D66864DACBA5CC6A9A655E443");
        RIUtil.rybqs = new JSONArray();
        for (JSONObject one : treeNodes) {
            RIUtil.rybqs.add(one);
        }

        log.warn("-ryTree--->" + (System.currentTimeMillis() - start));
        tw1 = RIUtil.GetDictByType_bq(3, RIUtil.dzbq);
        nodes = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.getJSONObject(i);
            nodes.add(one);
        }

        treeNodes = buildTree(nodes, "5F646FAFB4B24D5EAAECFF79AA9E8D1C");
        RIUtil.dzbqs = new JSONArray();
        for (JSONObject one : treeNodes) {
            RIUtil.dzbqs.add(one);
        }
        log.warn("-dzTree--->" + (System.currentTimeMillis() - start));
    }


    public static List<JSONObject> buildTree(List<JSONObject> nodeList, String fid) {
        List<JSONObject> tree = new ArrayList<>();
        for (JSONObject node : nodeList) {
            String id = node.getString("id");
            String f_id = node.getString("father_id");
            if (f_id.equals(fid)) {
                //使用递归方法构建子节点树
                List<JSONObject> children = buildTree(nodeList, id);
                children = RIUtil.GetSort(children, "index_no", 1);


                //node.setChildren(children);
                node.put("dets", children);
                tree.add(node);
            }
        }
        return tree;
    }


    public void initDict() {


        try {


            String sql =
                    "select id,dict_name,permission,color," + "father_id,index_no,type,gadm,remark,is_kh,memo," +
                            "static_index ,isdelete,label_yw from " + "dict " + "where  type is not null and length" + "(permission)>0 order by " + "type,index_no," + "id";
            System.out.println(sql);
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 999999);


            System.out.println(list.size());
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                long start = System.currentTimeMillis();
                //  RIUtil.dicts = new HashMap<>();

                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.getJSONObject(i);
                    if (one.getString("id").equals("26a20011-d093-4827-9062-a1c9e17fd774")) {
                        System.out.println(one);
                    }

                    if (!one.containsKey("dict_name")) {

                        break;
                    }
                    if (!one.containsKey("permission")) {
                        one.put("permission", "");
                    }
                    if (!one.containsKey("label_yw")) {
                        one.put("label_yw", "");
                    }
                    if (!one.containsKey("memo")) {
                        one.put("memo", "");
                    }
                    if (!one.containsKey("color")) {
                        one.put("color", "");
                    }

                    if (!one.containsKey("isdelete")) {
                        one.put("isdelete", "2");
                    }
                    if (!one.containsKey("father_id")) {
                        one.put("father_id", "");
                    }
                    if (!one.containsKey("index_no")) {
                        one.put("index_no", "99");
                    }
                    if (!one.containsKey("type")) {
                        one.put("type", "");
                    }
                    if (!one.containsKey("gadm")) {
                        one.put("gadm", "");
                    }
                    if (!one.containsKey("remark")) {
                        one.put("remark", "");
                    }

                    if (!one.containsKey("static_index")) {
                        one.put("static_index", "0");
                    }

                    RIUtil.dicts.put(one.getString("id"), one);
                    int del = one.getInteger("isdelete");
                    try {
                        if (one.getString("permission").equals("jqbq") && del == 1) {
                            RIUtil.jqbqs.put(one.getString("id"), one);
                        }
                    } catch (Exception ex) {
                        //System.out.println(one);
                    }

                    if (one.getString("type").equals("127") && del == 1) {
                        RIUtil.mzs.put(one.getString("id"), one);
                    }
                    if (one.getString("type").equals("3") && del == 1) {
                        RIUtil.dzbq.put(one.getString("id"), one);
                    }
                    if (one.getString("type").equals("4") && del == 1) {
                        RIUtil.rybq.put(one.getString("id"), one);
                    }

                }
                log.warn("init.dict->" + RIUtil.dicts.size());
                log.warn("init.rybq->" + RIUtil.rybq.size());
                log.warn("init.dzbq->" + RIUtil.dzbq.size());

                long end = System.currentTimeMillis();
                long bt = end - start;
                System.out.println(bt);


            }

        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));

        }
    }

    private void GetDictCount() {

        try {

            String sql =
                    "select father_id,count(id)as count ,type from dict where type<=10 and isdelete=1 group by " +
                            "father_id ";

            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 99999);

            sql = "update dict set label_yw=0 where type<10";
            jdbcTemplate.update(sql);
            if (list.size() > 0) {
                JSONArray ret = RIUtil.ListMap2jsa(list);
                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.getJSONObject(i);
                    String fid = one.getString("father_id");
                    int count = one.getInteger("count");
                    int type = one.getInteger("type");


                    if (fid.equals("-1")) {
                        sql = "update dict set label_yw='" + count + "' where type='" + type + "' and father_id='-1'";
                    } else {
                        sql = "update dict set label_yw='" + count + "' where id='" + fid + "'";
                    }
                    //  System.out.println(sql);
                    jdbcTemplate.update(sql);

                }


            }
        } catch (Exception ex) {
            log.error(Lib.getTrace(ex));
        }
    }


    @Scheduled(fixedRate = 1000 * 10)
    public void doSjry() {
        //log.warn("---sjrybq--->" + RIUtil.sjryxg);

        String persons[] = RIUtil.sjryxg.split(",");

        RIUtil.sjryxg = "";

        for (int i = 0; i < persons.length; i++) {
            String sfz = persons[i];

            if (sfz.length() > 2) {
                try {

                    String sql = "select jjbh from wjsc_jq_sjxx where gmsfhm='" + sfz + "'";

                    String jjbhs = RIUtil.GetStringFListSql(sql, "jjbh", jdbcTemplate);

                    if (jjbhs.endsWith(",")) {
                        jjbhs = jjbhs.substring(0, jjbhs.length() - 1);
                    }

                    jjbhs = jjbhs.replace("|", "','");
                    sql = "select sjlb,personM from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm='" + sfz + "'";
                    List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 99999);

                    JSONArray ret = RIUtil.ListMap2jsa(list);

                    String sjlbs = "";
                    HashMap<String, String> personMs = new HashMap<>();
                    for (int a = 0; a < ret.size(); a++) {
                        JSONObject one = ret.getJSONObject(a);
                        String sjlb = one.getString("sjlb");
                        sjlbs = sjlbs + sjlb + ",";
                        String personM = one.getString("personM");

                        HashMap<String, String> pms = RIUtil.StringToList(personM);
                        personMs.putAll(pms);

                    }

                    String cjlbs = "";
                    String cjdws = "";
                    HashMap<String, String> addressMs = new HashMap<>();
                    HashMap<String, String> reasonMs = new HashMap<>();
                    HashMap<String, String> resultMs = new HashMap<>();
                    HashMap<String, String> toolMs = new HashMap<>();
                    String timeMs = "";

                    sql = "select jjbh,cjlb,addressM,reasonM,resultM,toolM,timeM,cjdw from wjsc_jq_cjxx where" + " " + "jjbh in" + " ('" + jjbhs + "') ";
                    list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 99999);

                    ret = RIUtil.ListMap2jsa(list);

                    for (int a = 0; a < ret.size(); a++) {
                        JSONObject one = ret.getJSONObject(a);

                        String cjlb = one.getString("cjlb");
                        if (cjlb != null) {
                            cjlbs = cjlbs + cjlb + ",";
                        }

                        String cjdw = one.getString("cjdw");
                        if (cjdw != null) {
                            cjdws = cjdws + cjdw + ",";
                        }

                        HashMap<String, String> adMs = RIUtil.StringToList(one.getString("addressM"));
                        addressMs.putAll(adMs);

                        HashMap<String, String> reMs = RIUtil.StringToList(one.getString("reasonM"));
                        reasonMs.putAll(reMs);

                        HashMap<String, String> resMs = RIUtil.StringToList(one.getString("resultM"));
                        resultMs.putAll(resMs);

                        HashMap<String, String> toMs = RIUtil.StringToList(one.getString("toolM"));
                        toolMs.putAll(toMs);

                        String timeM = one.getString("timeM");
                        if (timeM != null && !timeMs.contains(timeM)) {
                            timeMs = timeMs + timeM + ",";
                        }

                    }

                    sql = "update jq_person set jjbhs='" + jjbhs + "', sjlbs='" + sjlbs + "'," + "personMs='" + RIUtil.HashToList(personMs) + "',addressMs='" + RIUtil.HashToList(addressMs) + "'," + "reasonMs='" + RIUtil.HashToList(reasonMs) + "'," + "resultMs='" + RIUtil.HashToList(resultMs) + "'," + "toolMs='" + RIUtil.HashToList(toolMs) + "'," + "timeMs='" + timeMs + "',cjlbs='" + cjlbs + "',cjdws='" + cjdws + "'"

                            + " where gmsfhm='" + sfz + "'";
                    System.out.println(sql);
                    jdbcTemplate.update(sql);

                } catch (Exception e) {
                    System.out.println(Lib.getTrace(e));
                    log.error(Lib.getTrace(e));
                }
            }
        }


    }

    /* @Scheduled(fixedRate = 1000 * 10)
     public void doSjry1() {

         log.warn("-----------------doSjry------------------");
         long start=System.currentTimeMillis();

         try {
             String up_time = RIUtil.stampToTime(System.currentTimeMillis() - (1000 * 15));
             String sql = "select gmsfhm from jq_person where up_time>='" + up_time + "'";
             String sfzs = RIUtil.GetStringFListSql(sql, "gmsfhm", jdbcTemplate);

             if (sfzs.length() > 2) {
                 String sfzss[] = sfzs.split(",");

                 for (int i = 0; i < sfzss.length; i++) {
                     String gmsfhm = sfzss[i];

                     sql = "select jjbh from wjsc_jq_sjxx where gmsfhm='" + gmsfhm + "'";
                     String jjbhs = RIUtil.GetStringFListSql(sql, "jjbh", jdbcTemplate);
                     System.out.println(jjbhs);

                     if (jjbhs.endsWith(",")) {
                         jjbhs = jjbhs.substring(0, jjbhs.length() - 1);
                     }

                     jjbhs = jjbhs.replace("|", ",");
                     jjbhs = jjbhs.replace(",", "','");

                     sql = "select gmsfhm from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm!='" + gmsfhm +
                     "' ";
                     System.out.println(sql);
                     String tjrys = RIUtil.GetStringFListSql(sql, "gmsfhm", jdbcTemplate);


                     sql = "select sjlb,personM from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "') and gmsfhm='" +
                     gmsfhm + "'";
                     List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

                     JSONArray ret = RIUtil.ListMap2jsa(list);

                     String sjlbs = "";
                     List<String> personMs = new ArrayList<>();
                     for (int a = 0; a < ret.size(); a++) {
                         JSONObject one = ret.getJSONObject(a);
                         String sjlb = one.getString("sjlb");
                         sjlbs = sjlbs + sjlb + ",";
                         String personM = one.getString("personM");

                         List<String> pms = RIUtil.HashToList(RIUtil.StringToList(personM));
                         personMs.addAll(pms);

                     }

                     String cjlbs = "";
                     String cjdws = "";
                     String cjjgs = "";
                     String bjlxs="";
                     List<String> addressMs = new ArrayList<>();
                     List<String> reasonMs = new ArrayList<>();
                     List<String> resultMs = new ArrayList<>();
                     List<String> toolMs = new ArrayList<>();
                     String timeMs = "";

                     sql = "select jjbh,cjlb,addressM,reasonM,resultM,toolM,timeM,cjdw,cjjg from wjsc_jq_cjxx where" +
                      " jjbh in" + " ('" + jjbhs + "') ";
                     list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

                     ret = RIUtil.ListMap2jsa(list);

                     for (int a = 0; a < ret.size(); a++) {
                         JSONObject one = ret.getJSONObject(a);

                         String cjlb = one.getString("cjlb");
                         if (cjlb != null) {
                             cjlbs = cjlbs + cjlb + ",";
                         }
                         String cjjg = one.getString("cjjg");
                         if (cjjg != null) {
                             cjjgs = cjjgs + cjjg + ",";
                         }

                         String cjdw = one.getString("cjdw");
                         if (cjdw != null) {
                             cjdws = cjdws + cjdw + ",";
                         }
                         sql="select bjlx from wjsc_jq_jjxx where jjbh in ('"+jjbhs+"')";
                          bjlxs=RIUtil.GetStringFListSql(sql,"bjlx",jdbcTemplate);

                         List<String> adMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("addressM")));
                         addressMs.addAll(adMs);

                         List<String> reMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("reasonM")));
                         reasonMs.addAll(reMs);

                         List<String> resMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("resultM")));
                         resultMs.addAll(resMs);

                         List<String> toMs = RIUtil.HashToList(RIUtil.StringToList(one.getString("toolM")));
                         toolMs.addAll(toMs);

                         String timeM = one.getString("timeM");

                         timeMs = timeMs + timeM + ",";


                     }

                     jjbhs = jjbhs.replace("'", "");
                     sql = "update jq_person set jjbhs='" + jjbhs + "', sjlbs='" + sjlbs + "'," + "personMs='" +
                     personMs +
                             "',addressMs='" + addressMs + "'," + "reasonMs='" + reasonMs + "'," + "resultMs='" +
                             resultMs +
                             "'," + "toolMs='" + toolMs + "'," + "timeMs='" + timeMs + "',cjlbs='" + cjlbs + "',
                             cjdws='" + cjdws + "'," +
                             "cjjgs='"+cjjgs+"',tjrys='"+tjrys+"',bjlxs='"+bjlxs+"'"

                             + " where gmsfhm='" + gmsfhm + "'";
                     System.out.println(sql);
                     jdbcTemplate.update(sql);
                 }
             }
         log.warn(System.currentTimeMillis()-start+"-->");
         } catch (Exception e) {
             System.out.println(Lib.getTrace(e));
             log.error(Lib.getTrace(e));
         }


     }*/
   /* @Scheduled(fixedRate = 1000 * 20)
    public void personM2Bq() {


        JSONArray dets = new JSONArray();
        try {


            String sql = "select jjbh from personM_tmp where mark=0";


            String jjbhs = RIUtil.GetStringFListSql(sql, "jjbh", jdbcTemplate);
            if (jjbhs.length() > 5) {
                log.warn(jjbhs);

                jjbhs = jjbhs.replace(",", "','");

                sql = "select jjbh,personMs,gmsfhm from wjsc_jq_sjxx where jjbh in ('" + jjbhs + "')";
                System.out.println(sql);

                List<Map<String, Object>> rets = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 99999);


                if (rets.size() > 0) {
                    dets = RIUtil.ListMap2jsa(rets);

                    sql = "delete from  personM_tmp  where jjbh in ('" + jjbhs + "')";
                    jdbcTemplate.update(sql);

                }
            }
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        }
        for (int i = 0; i < dets.size(); i++) {
            JSONObject one = dets.getJSONObject(i);
            String jjbh = one.getString("jjbh");
            String gmsfhm = one.getString("gmsfhm");
            try {
                String[] pms = one.getString("personMs").split(" ");


                String sql = "delete from  wj_jq_label  where jjbh='" + jjbh + "' and gmsfhm='" + gmsfhm + "'";
                //System.out.println(sql);
                jdbcTemplate.update(sql);

                for (int j = 0; j < pms.length; j++) {
                    String label = pms[j];
                    sql = "insert into wj_jq_label (jjbh,label,gmsfhm) values ('" + jjbh + "','" + label + "','" + gmsfhm + "');";
                    jdbcTemplate.update(sql);
                }
            } catch (Exception e) {
                log.error(Lib.getTrace(e));
                log.error("pMs-->" + one.getString("personMs"));
            }


        }

        //小区
        dets = new JSONArray();
        try {


            String sql = "SELECT id,dict_name FROM `wjjq`.`dict` WHERE `type` < '5' AND `gadm` IS NULL AND `remark` IS NOT NULL";
            System.out.println(sql);

            List<Map<String, Object>> rets = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 99999);


            if (rets.size() > 0) {
                dets = RIUtil.ListMap2jsa(rets);
            }

        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        }
        for (int i = 0; i < dets.size(); i++) {
            JSONObject one = dets.getJSONObject(i);
            String id = one.getString("id");
            String name = one.getString("dict_name");

            String unit = "";
            String xq = "";
            OracleHelper ora = null;
            try {
                ora = new OracleHelper("");


                if (id.startsWith("JZGD")) {
                    String sql = "select sszrq from jwry_dba.CZJG_JZGD_JBXX@qjjc_sydw where jgbh='" + id.replace("ZY_", "") + "'";
                    unit = ora.query_one(sql, "SSZRQ");
                } else if (id.startsWith("JG") || id.startsWith("ZY_")) {
                    String sql = "select sszrq from jwry_dba.CZJG_JBXX@qjjc_sydw where jgbh='" + id.replace("ZY_", "") + "'";
                    unit = ora.query_one(sql, "SSZRQ");
                    if (unit == null || unit.length() == 0) {
                        sql = "select sszrq from jwry_dba.CZJG_JBXX@qjjc_sydw where dwmc='" + name.replace("()", "") + "' and dzid is not null";
                        unit = ora.query_one(sql, "SSZRQ");
                    }


                } else {
                    if (name.contains("(临)")) {//非标
                        String sql = "select sjgsdwdm from czqj_ybds.address_fb_info@qjjc_ybls where ID='" + id + "'";
                        unit = ora.query_one(sql, "SJGSDWDM");
                    } else {//标准地址
                        String sql = "select HJZRQ,dmdm from czqj_ybds.address_info@qjjc_ybls where dzid='" + id + "'";
                        unit = ora.query_one(sql, "HJZRQ");
                        xq = ora.query_one(sql, "DMDM");
                    }


                }
            } catch (Exception e) {
                log.error(Lib.getTrace(e));
            } finally {
                ora.close();
            }


            log.warn(id + "--" + name + "-" + unit);

            if (unit != null && unit.startsWith("3204")) {
                try {


                    String sql = "update dict set gadm='" + unit + "',label_jz='" + xq + "' where id='" + id + "'";
                    System.out.println(sql);
                    jdbcTemplate.update(sql);


                } catch (Exception e) {
                    log.error(Lib.getTrace(e));
                }
            }


        }

    }
*/
}
