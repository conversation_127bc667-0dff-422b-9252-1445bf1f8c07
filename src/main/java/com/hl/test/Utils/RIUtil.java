package com.hl.test.Utils;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class RIUtil {
    public static String enName = "1n2a3m4e";
    public static String enTele = "1t2e3l4e";
    public static String enDate = "1d2a3t4e";
    public static String enNum = "1n2u3m";
    public static String enTitle = "1t2i3t4l5e";
    public static String enContent = "1c2o3n4t5e6n7t";

    public static HashMap<String, JSONObject> dicts = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> jqbqs = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> rybq = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> dzbq = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> mzs = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> kh_configs = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> users = new HashMap<>();
    public static HashMap<String, JSONObject> users1 = new HashMap<>();
    public static JSONArray org_tree = new JSONArray();
    public static JSONArray login_times = new JSONArray();
    public static String sjryxg = "";
    public static JSONArray rybqs = new JSONArray();
    public static JSONArray dzbqs = new JSONArray();


    public static HashMap StringToList(String members) {
        HashMap back = new HashMap();
        try {

            // System.out.println("or->" + members);
            String m = members.replace("[", "").replace("]", "").replace("\"", "");
            //System.out.println("cut->" + m);
            String[] mm = m.split(",");
            if (mm.length > 0) {
                for (int i = 0; i < mm.length; i++) {
                    String ms = mm[i].trim();
                    if (ms.length() > 0) {
                        back.put(ms, "");
                    }
                }
            }
        } catch (Exception ex) {

        }
        //System.out.println("S2L->" + back);
        return back;

    }

    public static List<String> StringToArrayList(String members) {
        List<String> back = new ArrayList<>();
        try {

            // System.out.println("or->" + members);
            String m = members.replace("[", "").replace("]", "").replace("\"", "");
            //System.out.println("cut->" + m);
            String[] mm = m.split(",");
            if (mm.length > 0) {
                for (int i = 0; i < mm.length; i++) {
                    String ms = mm[i].trim();
                    if (ms.length() > 0) {
                        back.add(ms);
                    }
                }
            }
        } catch (Exception ex) {

        }
        //System.out.println("S2L->" + back);
        return back;

    }

    public static HashMap StringToList(String members, String type) {
        HashMap back = new HashMap();
        // System.out.println("or->" + members);
        String m = members.replace("[", "").replace("]", "").replace("\"", "");
        //System.out.println("cut->" + m);
        String[] mm = m.split(",");
        if (mm.length > 0) {
            for (int i = 0; i < mm.length; i++) {
                String ms = mm[i].trim();
                if (ms.length() > 0) {
                    back.put(type + ms, "");
                }
            }
        }
        //System.out.println("S2L->" + back);
        return back;

    }


    public static JSONArray UseridToNames(HashMap<String, String> members) throws Exception {
        //System.out.println("86->" + members);
        JSONArray back = new JSONArray();

        for (Map.Entry<String, String> one : members.entrySet()) {
            String id = one.getKey();

            try {

                JSONObject u = users.get(id);
                if (u != null) {

                    back.add(u);
                }
            } catch (Exception ex) {
                // System.out.println(Lib.getTrace(ex));
            }
        }


        return back;

    }


    public static List<String> HashToList(HashMap<String, String> datas) {
        List<String> back = new ArrayList<>();
        for (Map.Entry<String, String> one : datas.entrySet()) {
            back.add(one.getKey().trim());
        }
        return back;
    }

    //将时间转换为时间戳
    public static long dateToStamp(String s) throws Exception {
        String res;//设置时间格式，将该时间格式的时间转换为时间戳
        if (s.length() == 16) {
            s = s + ":00";
        }
        if (s.length() == 10) {
            s = s + " 00:00:00";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse(s);
        long time = date.getTime();

        return time;
    }

    //将时间戳转换为时间
    public static String stampToTime(long s) throws Exception {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long lt = new Long(s);//将时间戳转换为时间
        Date date = new Date(lt);//将时间调整为yyyy-MM-dd HH:mm:ss时间样式
        res = simpleDateFormat.format(date);
        return res;
    }

    public static int dateToWeek(String datetime) {
        if (datetime.length() == 16) {
            datetime = datetime + ":00";
        }
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        Date date;
        try {
            date = f.parse(datetime);
            cal.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //一周的第几天
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) w = 0;
        return w;
    }

    public static String getLMonthEnd(String start_time) {//设置时间格式
        int[] monthdays = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        String dates[] = start_time.split("-");
        int year = Integer.parseInt(dates[0]);
        int month = Integer.parseInt(dates[1]);
        String m = "";
        if (year % 4 == 0 && month == 2) {
            m = "0" + 2;
            return year + "-" + m + "-29 23:59:59";
        } else {
            if (month < 10) {
                m = "0" + month;
            } else {
                m = String.valueOf(month);
            }
            return year + "-" + m + "-" + monthdays[month] + " 23:59:59";
        }


    }

    /**
     * 获取本周的最后一天
     *
     * @param start_time
     * @return String
     */
    public static String getWeekEnd(String start_time) throws ParseException {

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date date = new Date(new SimpleDateFormat("yyyy-MM-dd").parse(start_time).getTime());

        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 23:59:59";
    }

    public static String[] GetSelects(String selects) {
        String ss = selects.replace("{", "").replace("}", "");
        String[] s = ss.split(",");
        String back[] = new String[s.length];
        for (int i = 0; i < s.length; i++) {
            String[] one = s[i].split(":");
            back[i] = one[1];
        }
        return back;
    }

    public static String GetNextDateTime(String start_time, int days) throws ParseException {
        if (start_time.length() == 16) {
            start_time = start_time + ":00";
        }
        if (start_time.length() == 10) {
            start_time = start_time + " 00:00:00";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date sDate = sdf.parse(start_time);

        Calendar c = Calendar.getInstance();
        c.setTime(sDate);
        c.add(Calendar.DAY_OF_MONTH, days);        //利用Calendar 实现 Date日期+1天

        sDate = c.getTime();
        return sdf.format(sDate);

    }

    public static String GetNextDate(String startData, int days) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date sDate = sdf.parse(startData);

        Calendar c = Calendar.getInstance();
        c.setTime(sDate);
        c.add(Calendar.DAY_OF_MONTH, days);        //利用Calendar 实现 Date日期+1天

        sDate = c.getTime();
        return sdf.format(sDate);

    }


    public static String getLastMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前时间
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1); // 设置为上一个月
        date = calendar.getTime();
        String accDate = format.format(date) + "01";
        return accDate;
    }

    public static String getNextMonth(String time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");

        try {
            Date date = format.parse(time);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date); // 设置为当前时间
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1); // 设置为下一个月
            date = calendar.getTime();
            return format.format(date) + "-01 00:00:00";
        } catch (Exception e) {
            return "";
        }
    }

    public static int get2DateBetween(String start, String end) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(start));
        long time1 = cal.getTimeInMillis();
        cal.setTime(sdf.parse(end));
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(between_days));
    }

    public static String getWeekStart(String d) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(sdf.parse(d).getTime());
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekday = cal.get(Calendar.DAY_OF_WEEK) - 2;
        cal.add(Calendar.DATE, -weekday);
        sdf.format(cal.getTime());
        return sdf.format(cal.getTime());
    }

    public static int GetWeekNum(String str) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = format.parse(str);
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.setTime(date);
            return calendar.get(Calendar.WEEK_OF_YEAR);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return 99;

    }

    public static String GetQuarterNum(String month) {
        try {
            if (month.equals("01") || month.equals("02") || month.equals("03")) {
                return "01";
            } else if (month.equals("04") || month.equals("05") || month.equals("06")) {
                return "02";
            } else if (month.equals("07") || month.equals("08") || month.equals("09")) {
                return "03";
            } else if (month.equals("10") || month.equals("11") || month.equals("12")) {
                return "04";
            } else {
                return "";
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return "00";
    }

    public static String GetQuarterDate(String quarter, String year) {
        String start_time = "";
        String end_time = "";
        if (quarter.equals("01")) {
            start_time = year + "-01-01 00:00:00";
            end_time = year + "-03-31 23:59:59";
        }
        if (quarter.equals("02")) {
            start_time = year + "-04-01 00:00:00";
            end_time = year + "-06-30 23:59:59";
        }
        if (quarter.equals("03")) {
            start_time = year + "-07-01 00:00:00";
            end_time = year + "-09-30 23:59:59";
        }
        if (quarter.equals("04")) {
            start_time = year + "-10-01 00:00:00";
            end_time = year + "-12-31 23:59:59";
        }
        return start_time + "," + end_time;
    }

    //获取身份证信息
    public static JSONObject GetIDinfo(String id_num) {
        JSONObject back = new JSONObject();
        if (id_num.length() == 15) {
            back = identityCard15(id_num);
        } else if (id_num.length() == 18) {
            back = identityCard18(id_num);
        }
        return back;
    }

    /**
     * 18位身份证获取性别和年龄
     *
     * @param CardCode
     * @return
     * @throws Exception
     */
    public static JSONObject identityCard18(String CardCode) {
        JSONObject map = new JSONObject();
        try {

            // 得到年份
            String year = CardCode.substring(6).substring(0, 4);
            // 得到月份
            String month = CardCode.substring(10).substring(0, 2);
            //得到日
            String day = CardCode.substring(12).substring(0, 2);
            String sex;
            // 判断性别
            if (Integer.parseInt(CardCode.substring(16).substring(0, 1)) % 2 == 0) {
                sex = "1";
            } else {
                sex = "2";
            }
            // 得到当前的系统时间
            Date date = new Date();
            // 当前年份
            String currentYear = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(0, 4);
            // 月份
            String currentMonth = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(5, 7);
            //String currentdDay=format.format(date).substring(8,10);
            int age = 0;
            // 当前月份大于用户出身的月份表示已过生日
            if (Integer.parseInt(month) <= Integer.parseInt(currentMonth)) {
                age = Integer.parseInt(currentYear) - Integer.parseInt(year) + 1;
            } else {
                // 当前用户还没过生日
                age = Integer.parseInt(currentYear) - Integer.parseInt(year);
            }
            map.put("sex", sex);
            map.put("age", age);
            map.put("birth", year + "-" + month + "-" + day);
        } catch (Exception ex) {

        }
        return map;
    }

    /**
     * 15位身份证获取性别和年龄
     *
     * @param card
     * @return
     * @throws Exception
     */
    public static JSONObject identityCard15(String card) {

        JSONObject map = new JSONObject();
        try {
            //年份
            String year = "19" + card.substring(6, 8);
            //月份
            String yue = card.substring(8, 10);
            //日
            String day = card.substring(10, 12);
            String sex;
            if (Integer.parseInt(card.substring(14, 15)) % 2 == 0) {
                sex = "1";
            } else {
                sex = "2";
            }
            // 得到当前的系统时间
            Date date = new Date();
            //当前年份
            String currentYear = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(0, 4);
            //月份
            String currentMonth = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(5, 7);
            //String fday=format.format(date).substring(8,10);
            int age = 0;
            //当前月份大于用户出身的月份表示已过生日
            if (Integer.parseInt(yue) <= Integer.parseInt(currentMonth)) {
                age = Integer.parseInt(currentYear) - Integer.parseInt(year) + 1;
            } else {
                // 当前用户还没过生日
                age = Integer.parseInt(currentYear) - Integer.parseInt(year);
            }
            map.put("sex", sex);
            map.put("age", age);
            map.put("birth", year + "-" + yue + "-" + day);

            String sql = "select prefecture from label_location " + "where code='" + card.substring(0, 6) + "'";

        } catch (Exception ex) {
        }
        return map;
    }

    public static String RealDictNames(HashMap<String, String> ids) {
        String back = "";

        try {

            for (Map.Entry<String, String> one : ids.entrySet()) {
                String id = one.getKey();
                String names = "";
                if (dicts.containsKey(id)) {

                    names = dicts.get(id).getString("dict_name");

                    back = back + names + ",";
                }

            }
            back = back.substring(0, back.length() - 1);
        } catch (Exception ex) {

        }

        return back;
    }

    public static String RealDictMemoNames(HashMap<String, String> ids) {
        String back = "";

        try {

            for (Map.Entry<String, String> one : ids.entrySet()) {
                String id = one.getKey();
                String names = "";
                if (dicts.containsKey(id)) {

                    names = dicts.get(id).getString("memo");

                    back = back + names + ",";
                }

            }
            back = back.substring(0, back.length() - 1);
        } catch (Exception ex) {

        }

        return back;
    }

    public static JSONArray RealDictNameList(HashMap<String, String> ids) {
        JSONArray back = new JSONArray();


        try {
            for (Map.Entry<String, String> one : ids.entrySet()) {
                String id = one.getKey();
                String names = "";
                JSONObject o = new JSONObject();

                if (dicts.containsKey(id)) {

                    o.put("id", id);

                    names = dicts.get(id).getString("dict_name");
                    o.put("name", names);
                    o.put("permission", dicts.get(id).get("permission"));
                    o.put("index_no", dicts.get(id).get("index_no"));
                    o.put("remark", dicts.get(id).get("remark"));
                    o.put("memo", dicts.get(id).getString("memo"));
                    o.put("color", dicts.get(id).getString("color"));
                    back.add(o);

                }
            }
        } catch (Exception ex) {

        }
        return back;
    }

    public static String GetJSid() {
        String back = "";

        for (Map.Entry<String, JSONObject> one : dicts.entrySet()) {
            JSONObject per = one.getValue();
            String id = one.getKey();
            String p = per.getString("permission");
            if (p.contains("J")) {
                back = id;
                break;
            }
        }
        return back;
    }


    public static JSONArray GetDictByType(int type) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            int t = one.getValue().getInteger("type");
            int del = one.getValue().getInteger("isdelete");
            if (t == type && del == 1) {
                back.add(one.getValue());
            }
        }
        back = GetSort(back, "index_no", 1);
        return back;

    }

    public static JSONArray GetDictByType_bq(int type, HashMap<String, JSONObject> ds) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : ds.entrySet()) {

            int del = one.getValue().getInteger("isdelete");
            int t = one.getValue().getIntValue("type");
            if (del == 1 && t == type) {
                back.add(one.getValue());
            }
        }

        return back;

    }

    public static List<JSONObject> GetDictByTypeList(int type) {
        List<JSONObject> back = new ArrayList<>();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            int t = one.getValue().getInteger("type");
            int d = one.getValue().getInteger("isdelete");
            if (t == type && d == 1) {
                back.add(one.getValue());
            }
        }
        return back;

    }

    public static JSONArray GetDictByTypeFather(int type, String father_id) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            int t = one.getValue().getInteger("type");
            int del = one.getValue().getInteger("isdelete");
            String fid = one.getValue().getString("father_id");
            if (t == type && father_id.equals(fid) && del == 1) {
                back.add(one.getValue());
            }
        }
        back = GetSort(back, "index_no", 1);
        return back;

    }

    public static JSONArray GetDictByTypeFather_bq(int type, String father_id, HashMap<String, JSONObject> ds) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : ds.entrySet()) {
            int t = one.getValue().getInteger("type");
            String fid = one.getValue().getString("father_id");
            if (t == type && father_id.equals(fid)) {
                back.add(one.getValue());
            }
        }
        back = GetSort(back, "index_no", 1);
        return back;

    }

    private static JSONArray GetSort(JSONArray tw1, String col, int mark) {

        List<JSONObject> list = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.getJSONObject(i);
            if (!one.containsKey("index_no") || one.getString("index_no") == null || one.getString("index_no").length() == 0) {
                String id = one.getString("id");
                String index = "";
                if (id.contains("-")) {
                    index = id.split("\\-")[1];
                } else {
                    index = id;
                }
                one.put("index_no", index);

            }
            list.add(one);
        }

        try {
            Collections.sort(list, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger(col);
                    b = o2.getInteger(col);
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {
                    //降序排列，升序改成a>b
                    if (mark == 1) {
                        return 1;
                    } else {
                        return -1;
                    }
                } else if (a == b) {
                    return 0;
                } else {
                    if (mark == 1) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            });

            tw1 = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                tw1.add(one);
            }
        } catch (Exception ex) {

        }
        return tw1;
    }

    public static List<JSONObject> GetSort(List<JSONObject> tw1, String col, int mark) {

        try {
            List<JSONObject> list = new ArrayList<>();
            for (int i = 0; i < tw1.size(); i++) {
                JSONObject one = tw1.get(i);
                if (!one.containsKey("index_no") || one.getString("index_no") == null || one.getString("index_no").length() == 0) {
                    String id = one.getString("id");
                    String index = "";
                    if (id.contains("-")) {
                        index = id.split("\\-")[1];
                    } else {
                        index = id;
                    }
                    one.put("index_no", index);

                }
                list.add(one);
            }

            Collections.sort(list, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger(col);
                    b = o2.getInteger(col);
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {
                    //降序排列，升序改成a>b
                    if (mark == 1) {
                        return 1;
                    } else {
                        return -1;
                    }
                } else if (a == b) {
                    return 0;
                } else {
                    if (mark == 1) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            });

        } catch (Exception ex) {

        }
        return tw1;
    }

    public static JSONArray GetDictByFather(String father_id) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            //    int t = one.getValue().getInteger("type");
            String fid = one.getValue().getString("father_id");
            int isdel = one.getValue().getIntValue("isdelete");
            if (father_id.equals(fid) && isdel == 1) {
                back.add(one.getValue());
            }
        }
        back = GetSort(back, "index_no", 1);
        return back;

    }

    public static JSONArray GetDictByFatherbq(String father_id) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.jqbqs.entrySet()) {
            int d = one.getValue().getInteger("isdelete");
            String fid = one.getValue().getString("father_id");
            if (father_id.equals(fid) && d == 1) {
                back.add(one.getValue());
            }
        }
        return back;

    }

    public static void main(String[] args) {
        System.out.println(Math.random());
    }


    public static String Chat2Unicode(String str) {
        String codes[] = {"0020", "0021", "0022", "0023", "0024", "0025", "0026", "0027", "0028", "0029", "002A",
                "002B", "002C", "002D", "002E", "002F", "0030", "0031", "0032", "0033", "0034", "0035", "0036", "0037"
                , "0038", "0039", "003A", "003B", "003C", "003D", "003E", "003F", "0040", "0041", "0042", "0043",
                "0044", "0045", "0046", "0047", "0048", "0049", "004A", "004B", "004C", "004D", "004E", "004F", "0500"
                , "0051", "0052", "0053", "0054", "0055", "0056", "0057", "0058", "0059", "005A", "005B", "005C",
                "005D", "005E", "005F", "0060", "0061", "0062", "0063", "0064", "0065", "0066", "0067", "0068", "0069"
                , "006A", "006B", "006C", "006D", "006E", "006F", "0070", "0071", "0072", "0073", "0074", "0075",
                "0076", "0077", "0078", "0079", "007A", "007B", "007C", "007D", "007E"};
        String unis[] = {" ", "!", "“", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", "0", "1", "2"
                , "3", "4", "5", "6", "7", "8", "9", ":", ";", "<", "=", ">", "?", "@", "A", "B", "C", "D", "E", "F",
                "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
                "[", " ", "]", "^", "_", "`", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
                "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "{", "|", "}", "~"};
        HashMap<String, String> unicodes = new HashMap<>();
        for (int i = 0; i < codes.length; i++) {
            unicodes.put(unis[i], codes[i]);
        }
        DecimalFormat df = new DecimalFormat("0000");
        String back = "";
        try {
            back = unicodes.get(str);
            back = "\\\\u" + back;
        } catch (Exception ex) {

        }

        back = back.substring(1);

        return back;
    }


    public static HashMap<String, String> GetPolices() {
        HashMap<String, String> polices = new HashMap<String, String>();
        for (Map.Entry<String, JSONObject> one : users.entrySet()) {
            JSONObject o = one.getValue();
            String Main = o.getString("isMain");
            if (Main.equals("0")) {
                String id_num = o.getString("id_num");
                if (id_num.length() > 0) {
                    polices.put(id_num, "");
                }
                String tele = o.getString("tele_long");
                if (tele.length() > 0) {
                    polices.put(tele, "");
                }
            }
        }

        return polices;
    }


    public static JSONArray ListMap2jsa(List<Map<String, Object>> list) {

        JSONArray back = new JSONArray();

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> lone = list.get(i);
            JSONObject one = new JSONObject();
            for (Map.Entry<String, Object> o : lone.entrySet()) {
                one.put(o.getKey(), o.getValue());

            }

            back.add(one);


        }


        return back;
    }

    public static JSONArray getDictByPermission(String jqbz, String father_id) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.jqbqs.entrySet()) {
            try {
                String t = one.getValue().getString("permission");
                String fid = one.getValue().getString("father_id");
                int d = one.getValue().getInteger("isdelete");
                if (t.equals(jqbz) && father_id.equals(fid) && d == 1) {
                    back.add(one.getValue());
                }


            } catch (Exception ex) {

            }
        }
        back = GetSort(back, "index_no", 1);
        return back;
    }

    public static String get_Time(String string) {
        String date =
                string.substring(0, 4) + "-" + string.substring(4, 6) + "-" + string.substring(6, 8) + " " + string.substring(8, 10) + ":" + string.substring(10, 12) + ":" + string.substring(12, 14);
        return date;


    }


    public static String GetStringFListSql(String sqls, String col, JdbcTemplate jdbcTemplate) {
        String back = "";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sqls + " limit ?,?", 0, 9999);

        JSONArray ret = RIUtil.ListMap2jsa(list);

        for (int i = 0; i < ret.size(); i++) {
            JSONObject one = ret.getJSONObject(i);
            String o = one.getString(col);

            if (o != null && o.length() > 0) {
                back = back + o + ",";
            }
        }

        if (back.endsWith(",")) {
            back = back.substring(0, back.length() - 1);
        }

        return back;

    }

    public static String GetDictIdByNameType(int type, String dict_name) {

        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            try {
                int t = one.getValue().getIntValue("type");
                int del = one.getValue().getInteger("isdelete");
                String dname = one.getValue().getString("dict_name");
                if (type == t && dname.equals(dict_name) && del == 1) {
                    return one.getKey();
                }


            } catch (Exception ex) {

            }
        }

        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            try {
                int t = one.getValue().getIntValue("type");
                int del = one.getValue().getInteger("isdelete");
                String dname = one.getValue().getString("dict_name");
                if (type == t && dname.contains(dict_name) && del == 1) {
                    return one.getKey();
                }


            } catch (Exception ex) {

            }
        }


        return "";
    }

    public static JSONObject queryOne(String sql, JdbcTemplate jdbcTemplate) {
        JSONObject back = new JSONObject();
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql + " limit ?,?", 0, 9999);

        JSONArray ret = RIUtil.ListMap2jsa(list);
        System.out.println(ret);
        if (ret.size() > 0) {

            JSONObject one = ret.getJSONObject(0);
            for (Map.Entry<String, Object> o : one.entrySet()) {

                String val = "";
                try {
                    val = (String) o.getValue();
                    if (val == null) {
                        val = "";
                    }
                } catch (Exception ex) {

                }
                one.put(o.getKey(), val);
            }
            back = one;

        }
        return back;
    }

    public static JSONArray GetDictCounts(String det, int type) {//type:1赋值在dict_name后面2加count字段
        JSONArray back = new JSONArray();
        List<String> dets = StringToArrayList(det);
        HashMap<String, Integer> addMs = new HashMap<>();
        for (int a = 0; a < dets.size(); a++) {
            String aone = dets.get(a);
            if (aone != null && !aone.equals("null")) {
                int c = 1;
                if (addMs.containsKey(aone)) {
                    c = addMs.get(aone);
                    c++;
                }

                addMs.put(aone, c);
            }
        }

        for (Map.Entry<String, Integer> dd : addMs.entrySet()) {
            String d = dd.getKey();
            int c = dd.getValue();

            JSONObject one = new JSONObject();
            try {
                String ds = dicts.get(d).toString();
                one = JSONObject.parseObject(ds);

                one.put("dict_name", one.getString("dict_name") + "(" + c + ")");
                one.put("name", one.getString("dict_name"));
                one.put("count", c);

            } catch (Exception ex) {
                one = new JSONObject();
                one.put("id", d);

                one.put("dict_name", d + "(" + c + ")");
                one.put("name", one.getString("dict_name"));
                one.put("count", c);

            }

            back.add(one);

        }

        back = GetSort(back, "count", 0);

        return back;
    }


    public static JSONArray GetDictCountsOpts(String det, String opts, int isList, String cols) {//isList:1

        JSONArray back = new JSONArray();
        if(StringUtils.isBlank(det)) return back;
        String newDet = det.replaceAll("[\\[\\]\\s\"]", "");
        if(newDet.length() == 0) return back;
        List<String> dets = Arrays.stream(newDet.split(",")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
       // List<String> dets = StringToArrayList(det);
        HashMap<String, Integer> addMs = new HashMap<>();
        for (int a = 0; a < dets.size(); a++) {
            String aone = dets.get(a);
            if (aone != null && !aone.equals("null")) {
                int c = 1;
                if (addMs.containsKey(aone)) {
                    c = addMs.get(aone);
                    c++;
                }

                addMs.put(aone, c);
            }
        }

        for (Map.Entry<String, Integer> dd : addMs.entrySet()) {
            String d = dd.getKey();
            int c = dd.getValue();

            JSONObject one = new JSONObject();
            try {
                if (dicts.get(d) == null) continue;
                String ds = dicts.get(d).toString();
                one = JSONObject.parseObject(ds);
                if (one.getInteger("isdelete") == null || one.getInteger("isdelete") != 1) continue;
                if (!"-1".equals(one.getString("father_id"))) {
                    String father = dicts.get(one.getString("father_id")).toString();
                    JSONObject fatherObj = JSONObject.parseObject(father);
                    if(fatherObj == null || fatherObj.getInteger("isdelete") != 1) continue;
                }
                one.put("dict_name", one.getString("dict_name") + "(" + c + ")");
                one.put("name", one.getString("dict_name"));
                one.put("count", c);
                JSONObject opt = JSONObject.parseObject(cols);
                if (isList == 1) {
                    List<String> ls = new ArrayList<>();
                    ls.add(d);
                    opt.put(opts, ls);
                } else {
                    opt.put(opts, d);
                }


                one.put("opts", opt);
            } catch (Exception ex) {
                one = new JSONObject();
                one.put("id", d);

                one.put("dict_name", d + "(" + c + ")");
                one.put("name", one.getString("dict_name"));
                one.put("count", c);
                JSONObject opt = JSONObject.parseObject(cols);
                if (isList == 1) {
                    List<String> ls = new ArrayList<>();
                    ls.add(d);
                    opt.put(opts, ls);
                } else {
                    opt.put(opts, d);
                }
                one.put("opts", opt);
            }

            back.add(one);

        }

        back = GetSort(back, "count", 0);

        return back;
    }

    public static String GetBqTypeName(int type) {

        String[] names = {"地址标签", "人员标签", "时间标签", "结果标签", "原因标签", "手段标签"};
        String[] types = {"3", "4", "6", "7", "8", "9"};
        String[] ids = {"5F646FAFB4B24D5EAAECFF79AA9E8D1C", "DB21DC0D66864DACBA5CC6A9A655E443",
                "6002C301013241C8AB69D9BFA2E0C9A8", "6E7448F25AD645E999DBBD7022964614",
                "08CC6B6A0EE644D381F149D06A9CB4A6", "8F0164E90B3149B5B33066C173CC1CB9"};
        HashMap<String, String> tpss = new HashMap<>();
        for (int a = 0; a < types.length; a++) {
            String ty = types[a];
            String fid = names[a];
            tpss.put(ty, fid);
        }

        return tpss.get(String.valueOf(type));

    }

    public static String GetBqTypeId(int type) {

        String[] names = {"地址标签", "人员标签", "时间标签", "结果标签", "原因标签", "手段标签"};
        String[] types = {"3", "4", "6", "7", "8", "9"};
        String[] ids = {"5F646FAFB4B24D5EAAECFF79AA9E8D1C", "DB21DC0D66864DACBA5CC6A9A655E443",
                "6002C301013241C8AB69D9BFA2E0C9A8", "6E7448F25AD645E999DBBD7022964614",
                "08CC6B6A0EE644D381F149D06A9CB4A6", "8F0164E90B3149B5B33066C173CC1CB9"};
        HashMap<String, String> tpss = new HashMap<>();
        for (int a = 0; a < types.length; a++) {
            String ty = types[a];
            String fid = ids[a];
            tpss.put(ty, fid);
        }

        return tpss.get(String.valueOf(type));

    }

    public static String GetBqNameType(String name) {

        String[] names = {"地址标签", "人员标签", "时间标签", "结果标签", "原因标签", "手段标签"};
        String[] types = {"3", "4", "6", "7", "8", "9"};
        String[] ids = {"5F646FAFB4B24D5EAAECFF79AA9E8D1C", "DB21DC0D66864DACBA5CC6A9A655E443",
                "6002C301013241C8AB69D9BFA2E0C9A8", "6E7448F25AD645E999DBBD7022964614",
                "08CC6B6A0EE644D381F149D06A9CB4A6", "8F0164E90B3149B5B33066C173CC1CB9"};
        HashMap<String, String> tpss = new HashMap<>();
        for (int a = 0; a < types.length; a++) {
            String ty = names[a];
            String fid = types[a];
            tpss.put(ty, fid);
        }

        return tpss.get(name);

    }


}
