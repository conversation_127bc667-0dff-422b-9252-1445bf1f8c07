package com.hl.test.Utils.Export;



import com.alibaba.fastjson2.JSONArray;

import java.util.HashMap;
import java.util.List;

public interface ExportInterface {
    void init(String path)throws Exception;
    void close();
    void write_head(List<String> header,
                    HashMap<String, String> headername)throws Exception;
    void write_data(JSONArray data)throws Exception;
    void write_data_num(JSONArray data)throws Exception;
    void write_data(List<HashMap<String, String>> data)throws Exception;
}
