package com.hl.test.Utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson2.JSONObject;


public class OracleHelper {
    private Connection conn = null;
    private Statement stmt = null;
    private String url;
    public static String user = "";

    static {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    public OracleHelper(String key) {

        try {
            String host = "*************";
            String port = "11521";
            String data = "ORCL";
            user = "HL";
            String pass = "hl1234";

            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "jdbc:oracle:thin:@" + host + ":" + port + "/" + data + "";

            conn = DriverManager.getConnection(url, user, pass);

            stmt = conn.createStatement();

        } catch (Exception e) {

            System.out.println(Lib.getTrace(e));

        }
    }

    public void close() {
        if (stmt != null) {
            try {
                stmt.close();

            } catch (Exception e) {
                System.out.println(Lib.getTrace(e));
            }
        }

        if (conn != null) {
            try {
                conn.close();

            } catch (Exception e) {
                System.out.println(Lib.getTrace(e));
            }
        }
    }

    public void update(String sql) throws Exception {
        try {
            stmt.executeUpdate(sql);
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
            throw e;
        } finally {
            // close();
        }
    }

    public List<JSONObject> query(String sql) throws Exception {
        try {
            List<JSONObject> list = new ArrayList<>();

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();

                while (rs.next()) {
                    JSONObject map = new JSONObject();
                    for (int i = 1; i <= count; i++) {
                        if (rs.getString(i) == null) {
                            map.put(md.getColumnName(i), "");
                        } else {
                            map.put(md.getColumnName(i), rs.getString(i));
                        }
                    }
                    list.add(map);
                }
            } finally {
                rs.close();
            }

            return list;
        } catch (Exception e) {
            System.out.println(sql);
            System.out.println(Lib.getTrace(e));
            throw e;
        } finally {
            // close();
        }
    }

    public int query_count(String sql) throws Exception {
        int c = 0;
        try {

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();
                while (rs.next()) {

                    for (int i = 1; i <= count; i++) {

                        if (md.getColumnName(i).equals("count") || md.getColumnName(i).equals("COUNT")) {
                            c = rs.getInt(i);
                            break;
                        }
                    }
                }
            } finally {
                rs.close();
            }

            return c;
        } catch (Exception e) {
            System.out.println(sql);
            throw e;
        } finally {

        }
    }

    public String query_one(String sql, String col) {
        String back = "";
        try {

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();
                while (rs.next()) {

                    for (int i = 1; i <= count; i++) {
                        if (md.getColumnName(i).equals(col)) {
                            back = rs.getString(i);
                            break;
                        }
                    }
                }
            } finally {
                rs.close();
            }

            return back;
        } catch (Exception e) {
            System.out.println(sql);
            System.out.println(Lib.getTrace(e));
            return "";
        } finally {
            // close();
        }
    }

    public void insertBatchOra(StringBuilder sb) {

        String sql = "";
        try {

            sql = sb.toString();
            stmt.executeUpdate(sql);
        } catch (Exception e) {
            System.out.println(sql);
            System.out.println(Lib.getTrace(e));

        } finally {
            // close();
        }

    }

    public void updateBatchOra(String sql, List<String> list) {
        PreparedStatement stat = null;
        ResultSet rs = null;
        String err = "";
        try {
            conn.setAutoCommit(false);

            stat = conn.prepareStatement(sql);

            for (int i = 0; i < list.size(); i++) {
                err = list.get(i);
                String[] dets = list.get(i).split(",");
                for (int d = 0; d < dets.length; d++) {
                    stat.setString(d + 1, dets[d]);
                }
                stat.addBatch();
            }
            stat.executeBatch();
            conn.commit();
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
            System.out.println(err);
        }

    }

}
