package com.hl.test.Utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

public class BzgsUtil {
    public static String convertBzCondition(List<String> bzgsList) {
        List<String> fullBzgs = fullBzgsList(bzgsList);
        StringBuilder sql = new StringBuilder();
        List<String> group = new ArrayList<>();
        boolean inGroup = false;

        for (int i = 0; i < fullBzgs.size(); i++) {
            String g = fullBzgs.get(i);
            if ("(".equals(g)) {
                inGroup = true;
                group.clear();
            } else if (")".equals(g)) {
                inGroup = false;
                if (!group.isEmpty()) {
                    sql.append(buildGroupMatch(group));
                }
            } else if ("AND".equalsIgnoreCase(g)) {
                sql.append(" AND ");
            } else if ("OR".equalsIgnoreCase(g)) {
                if (!inGroup) {
                    sql.append(" OR ");
                }
            } else {
                if (inGroup) {
                    group.add(g);
                } else {
                    if (sql.length() > 0 && !sql.toString().endsWith("AND ") && !sql.toString().endsWith("OR ") && !sql.toString().endsWith("( ")) {
                        sql.append(" AND ");
                    }
                    sql.append(buildSingleMatch(g));
                }
            }
        }
        return sql.toString().trim();
    }

    private static String buildSingleMatch(String g) {
        Integer type = RIUtil.dicts.get(g).getInteger("type");
        g = "\"" + g + "\"";
        if (type != 4) {
            return " MATCH(jqbz) AGAINST ('" + g + "' in boolean mode ) ";
        } else {
            return " cj.jjbh in (select jjbh from wjsc_jq_sjxx where MATCH(personMs) AGAINST ('" + g + "' in boolean mode ))";
        }
    }

    private static String buildGroupMatch(List<String> group) {
        if (group.isEmpty()) return "";
        boolean allType4 = group.stream().allMatch(g -> RIUtil.dicts.get(g).getInteger("type") == 4);
        StringJoiner joiner = new StringJoiner(" | ");
        for (String g : group) {
            joiner.add("\"" + g + "\"");
        }
        if (allType4) {
            return " cj.jjbh in (select jjbh from wjsc_jq_sjxx " +
                    "where MATCH(personMs) AGAINST ('" + joiner.toString() + "' in boolean mode ))";
        } else {
            return " MATCH(jqbz) AGAINST ('" + joiner.toString() + "' in boolean mode ) ";
        }
    }

    private static Integer getType(String label) {
        return RIUtil.dicts.get(label).getInteger("type");
    }


    public static List<String> fullBzgsList(List<String> bzgsList) {
        ArrayList<String> result = new ArrayList<>();
        ArrayList<String> buffer = new ArrayList<>();

        for (String token : bzgsList) {
            if ("AND".equalsIgnoreCase(token) || "OR".equalsIgnoreCase(token) || "(".equals(token) || ")".equals(token)) {
                if (!buffer.isEmpty()) {
                    result.addAll(processBuffer(buffer));
                    buffer.clear();
                }
                result.add(token);
            }  else {
                buffer.add(token);
            }
        }
        if (!buffer.isEmpty()) {
            result.addAll(processBuffer(buffer));
        }
        return result;
    }

    private static List<String> processBuffer(List<String> labels) {
        if (labels.size() == 1) {
            return  new ArrayList<>(labels);
        }
        Map<Integer, List<String>> grouped = labels.stream().collect(Collectors.groupingBy(BzgsUtil::getType));
        List<String> result = new ArrayList<>();
        boolean firstGroup = true;
        for (Map.Entry<Integer, List<String>> entry : grouped.entrySet()) {
            if (!firstGroup) {
                result.add("AND");
            }
            firstGroup = false;

            List<String> group = entry.getValue();
            if (group.size() > 1) {
                result.add("(");
                for (int i = 0; i < group.size(); i++) {
                    if (i > 0) result.add("OR");
                    result.add(group.get(i));
                }
                result.add(")");
            } else {
                result.add(group.get(0));
            }
        }
        return result;
    }
}
