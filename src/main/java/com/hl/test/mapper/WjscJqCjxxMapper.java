package com.hl.test.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.test.domain.TmpDictId;
import com.hl.test.domain.WjscJqCjxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface WjscJqCjxxMapper extends BaseMapper<WjscJqCjxx> {


    @Select(" select old_id as oldId, new_id as newId, xq_code as xqCode from tmp_dict_id")
    List<TmpDictId> queryTmpDictId();

    int updateBatch(@Param("list") List<WjscJqCjxx> list);

//    @Select({"select s.id from jq_bz s INNER JOIN (\n" +
//            "SELECT jjbh, id, max(bz_time) as max_bz_time from jq_bz\n" +
//            "where jjbh in " +
//            "<foreach collection='jjbhs' item='jjbh' open='(' separator=',' close=')'>" +
//            " #{jjbh}" +
//            "</foreach>" +
//            "and his_type != 3 GROUP BY jjbh\n" +
//            ") l on s.jjbh = l.jjbh and s.bz_time = l.max_bz_time where s.his_type != 3 "})
    List<String> selectJqbz(@Param("jjbhs") List<String> jjbhs);
}
