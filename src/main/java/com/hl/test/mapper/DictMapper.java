package com.hl.test.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.test.domain.Dict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DictMapper extends BaseMapper<Dict> {
    @Select({
            "<script>",
            " select id from dict where father_id in ",
            "<foreach item='id' collection='parentIds' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    List<String> findBatchChildren(@Param("parentIds") List<String> currentParentIds);
}
