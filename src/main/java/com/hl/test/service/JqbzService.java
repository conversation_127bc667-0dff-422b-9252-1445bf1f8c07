package com.hl.test.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.common.domain.R;
import com.hl.test.domain.JqBz;
import com.hl.test.domain.req.*;
import com.hl.test.domain.vo.JqbqDataStatisticsTableDto;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface JqbzService extends IService<JqBz> {
    R<?> updateJQBZ(ReqJQBZUpdate req);

    R<?> updateJQBZPerson(ReqJQB<PERSON><PERSON>erson req);

    List<String> getIdByQuanXuan(List<String> bzbq);

    List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable(JqbqDataStatisticsTableReq req);

    void exportJqbqDataStatisticsTable(JqbqDataStatisticsTableReq req, HttpServletResponse response) throws IOException;

    List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable2(JqbqDataStatisticsTableReq req);
    List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable3(JqbqDataStatisticsTableReq req);

    R<?> FuzzyJqbq4Lazy(FuzzyJqbq4LazyReq req);

    R<?> getSszrqList(SearchStrReq req);

}
