package com.hl.test.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.WjscJqCjxx;
import com.hl.test.domain.resp.RespJQSta;
import com.hl.test.mapper.WjscJqCjxxMapper;
import com.hl.test.service.JqStaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class JqStaServiceImpl implements JqStaService {
    private final WjscJqCjxxMapper wjscJqCjxxMapper;


    public R<List<RespJQSta>> queryJqSta(JSONObject data) {
        List<String> jjdws = new ArrayList<>();
        String jjdw = data.getString("jjdw");
        if (StringUtils.isBlank(jjdw) || JSONArray.parseArray(jjdw, String.class).isEmpty()) {
            jjdws = Collections.singletonList(UserUtils.getUserNull().getBindOrganization());
        } else {
            jjdws = JSONArray.parseArray(jjdw, String.class);
        }
        Set<String> oidCondition = jjdws.stream().map(y -> Optional.ofNullable(RIUtil.dicts.get(y))
                .map(x -> {
                    Integer type = x.getInteger("type");
                    if (type == 21 || type == 22) return y.substring(0, 4);
                    else if (type == 23) return y.substring(0, 6);
                    else if (type == 24 || type == 25) return y.substring(0, 8);
                    else return y;
                })
                .orElse(y)).collect(Collectors.toSet());

        Set<String> cjlbCondition = null;
        if (StringUtils.isNotBlank("cjlb")) {
            List<String> cjlbs= JSONArray.parseArray(data.getString("cjlb"), String.class);
            if (!cjlbs.isEmpty()) {
                cjlbCondition = cjlbs.stream().map(cjlb -> {
                    if (cjlb.contains("-")) {
                        cjlb = cjlb.split("\\-")[1];
                    }
                    if (cjlb.endsWith("000000")) {
                        return cjlb.substring(0, 2);
                    } else if (cjlb.endsWith("0000")) {
                        return cjlb.substring(0, 4);
                    } else if (cjlb.endsWith("00")) {
                        return cjlb.substring(0, 6);
                    } else {
                        return cjlb;
                    }
                }).collect(Collectors.toSet());
            }
        }
        return R.ok();

//        String tabId = "";
//        if (data.containsKey("tabId") && data.getString("tabId").length() > 0) {
//            tabId = data.getString("tabId");
//        }
//        //统计层级
//        int level = -1;
//        if (data.containsKey("level") && data.getString("level").length() > 0) {
//            level = data.getInteger("level");
//            if (level > 0) {
//                sql = sql + " and level = '" + level + "' ";
//            } else {
//                level = -1;
//            }
//        }
//
//
//        //警情标签
////        if (data.containsKey("jqbz") && data.getString("jqbz").length() > 2) {
////            String lbs = data.getString("jqbz");
//        if (data.containsKey("bzbq") && data.getString("bzbq").length() > 2) {
//            String lbs = data.getString("bzbq");
//            if (lbs.length() > 3) {
//                HashMap<String, String> lbss = RIUtil.StringToList(lbs);
//                String s = "";
//                for (Map.Entry<String, String> cone : lbss.entrySet()) {
//                    String jqbz = cone.getKey();
//                    int lev = RIUtil.dicts.get(jqbz).getInteger("static_index");
//
//                    if (lev == 1) {
//                        int t = RIUtil.dicts.get(jqbz).getIntValue("type");
//                        if (level != -1) {
//                            s = s + " type='" + t + "' or ";
//                        } else {
//                            s = s + " (type='" + t + "'  and level=2)or ";
//                        }
//                    } else {
//                        String n = RIUtil.dicts.get(jqbz).getString("dict_name");
//                        String m = RIUtil.dicts.get(jqbz).getString("memo");
//                        String fid = RIUtil.dicts.get(jqbz).getString("father_id");
//                        if (level == -1) {
//                            JSONArray ns = RIUtil.GetDictByFather(jqbz);
//                            if (ns.size() > 0) {
//                                //s = s + " (memo like '%-" + n + "%' and level='" + (lev + 1) + "')or ";
//                                s = s + " (memo like '" + m + "%' and level='" + (lev + 1) + "')or ";
//                            } else {
//                                // 对于地址标签为所属商圈、工业园区下面的，统计的数据需要为关联的发生部位的统计数据
//                                if(org.apache.commons.lang3.StringUtils.equalsAny(fid, "5FF9C8735F3243DA8C391859D371D9BD", "3226EB8CC1A649A7AD87E0B825F7AD01")) {
//                                    if (tabId.length() == 0) {
//                                        sql = sql + " and ((type=3 and level=5 ) or (type=4 and level=3 and memo like '人员职业%' ) or(type=6 and level=2 )or(type=7 and level=2 )or(type=8 and level=2 )or(type=9 and level=2 ))  ";
//                                    }
//                                    // 版本1
////                                    if (jqbz.contains(":") || jqbz.contains("-")){
////                                        jqbz = jqbz.replaceAll("[:-]", " +");
////                                    }
////                                    jqbz = "+" + jqbz;
////                                    s = s + " MATCH(jqbz) AGAINST ('" + jqbz + "' in boolean mode ) or ";
//
//                                    s = s+ "jjbh in (\n" +
//                                            "\t\tselect DISTINCT jjbh from v_jq_label where label = '" + jqbz + "'\n" +
//                                            "\t) or ";
//
//                                } else {
//                                    s = s + " label = '" + jqbz + "' or ";
//
//                                }
//
//                                // s = s + " memo like '%-" + n + "%' or ";
//                            }
//                        } else {
//
//
//                            s = s + " memo like '%-" + n + "%' or ";
//                        }
//                    }
//
//
//                }
//                s = s.substring(0, s.length() - 3);
//                sql = sql + " and (" + s + ") ";
//            }
//        }
//        else {
//            if (level == -1) {
//                if (tabId.length() == 0) {
//                    sql = sql + " and ((type=3 and level=5 ) or (type=4 and level=3 and memo like '人员职业%' ) or(type=6 and level=2 )or(type=7 and level=2 )or(type=8 and level=2 )or(type=9 and level=2 ))  ";
//                } else {
//                    sql = sql + " and ((type=3 and level=5 ) or (type=4 and level=3 and memo like '人员职业%' ) or(type=6 and level=2 )or(type=7 and level=2 )or(type=8 and level=2 )or(type=9 and level=2 ))  ";
//
//                    String ts[] = tabId.split(",");
//                    for (int i = 0; i < ts.length; i++) {
//
//
//                        String t1 = ts[i];
//                        String t1Type = RIUtil.dicts.get(t1).getString("type");
//                        String meStr = RIUtil.dicts.get(t1).getString("dict_name");
//                        if (t1Type.equals("4")) {
//
//                            sql = sql.replace("人员职业", meStr);
//                            System.out.println(sql);
//                        } else {
//                            String s = "memo like '" + meStr + "%' and level=3";
//                            sql = sql.replace("type=7 and level=2", s);
//                            System.out.println(sql);
//                        }
//
//                    }
//
//                }
//            }
//        }







    }

}
