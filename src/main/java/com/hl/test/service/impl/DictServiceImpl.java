package com.hl.test.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.test.domain.Dict;
import com.hl.test.mapper.DictMapper;
import com.hl.test.service.DictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DictServiceImpl extends ServiceImpl<DictMapper, Dict> implements DictService {
    public final static String TREE_KEY_PREFIX = "getTreeByType:";

    private final StringRedisTemplate redisTemplate;
    @Override
    public void saveDictTree2Redis(Integer type, Integer topLevel) {
        if (type == null) return;
        List<Dict> all = this.lambdaQuery().eq(Dict::getType, type).eq(Dict::getIsdelete, 1).list();
        if (all.isEmpty()) return;
        Map<String, List<Dict>> childMap = all.stream().collect(Collectors.groupingBy(Dict::getFatherId));
        int rootLevel = topLevel == null ? 1 : topLevel;
        List<Dict> rootList = all.stream().filter(d -> d.getStaticIndex().equals(rootLevel)).collect(Collectors.toList());
        for (Dict root : rootList){
            buildTree(root, childMap);
        }
        String key = TREE_KEY_PREFIX + type;
        JSONArray objects = JSONArray.parseArray(JSON.toJSONString(rootList));
        redisTemplate.opsForValue().set(key, objects.toJSONString());
        log.info("type:{} 字典树已放入redis;", type);
    }

    private void buildTree(Dict parent, Map<String, List<Dict>> childMap) {
        List<Dict> dicts = childMap.get(parent.getId());
        if (dicts != null && !dicts.isEmpty()) {
            parent.setChild(dicts);
            for (Dict dict : dicts) {
                buildTree(dict, childMap);
            }
        }
    }

    @Override
    public JSONArray getDictTreeFromRedis(Integer type) {
        String key = TREE_KEY_PREFIX + type;
        String s = redisTemplate.opsForValue().get(key);
        return s == null ? new JSONArray() : JSON.parseArray(s);
    }
}
