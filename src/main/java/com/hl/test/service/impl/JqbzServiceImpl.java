package com.hl.test.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.common.domain.R;
import com.hl.common.utils.StringUtils;
import com.hl.security.User;
import com.hl.security.UserUtils;
import com.hl.test.domain.Dict;
import com.hl.test.domain.JqBz;
import com.hl.test.domain.WjscJqCjxx;
import com.hl.test.domain.req.*;
import com.hl.test.domain.vo.JqbqDataStatisticsTableDto;
import com.hl.test.domain.WjJqCount;
import com.hl.test.mapper.*;
import com.hl.test.service.DictCacheService;
import com.hl.test.service.DictService;
import com.hl.test.service.JqbzService;
import com.hl.test.service.handle.CustomSheetWriteHandler;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class JqbzServiceImpl extends ServiceImpl<JqBzMapper, JqBz> implements JqbzService {
    private final WjscJqCjxxMapper wjscJqCjxxMapper;
    private final WjJqCountMapper wjJqCountMapper;
    private final DictService dictService;
    private final DictMapper dictMapper;
    private final JqBzMapper jqBzMapper;
    private final DictCacheService dictCacheService;

    private String convertTimeRange(String startTime, String endTime) {
        LocalDateTime startTimeLocal = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime endTimeLocal = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return startTimeLocal.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ~ " + endTimeLocal.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Override
    public void exportJqbqDataStatisticsTable(JqbqDataStatisticsTableReq req, HttpServletResponse response) throws IOException {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String timeRange = "";
        List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTableDtos = jqbqDataStatisticsTable2(req);// 获取数据
        String title = "打标统计表";
        String remark = "备注：数据仅供参考";
        int columnCount = 9; // A~I 是 9 列
        if (StringUtils.isNotEmpty(req.getStartTime()) || StringUtils.isNotEmpty(req.getStartTime())) {
            if (StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isEmpty(req.getEndTime())) {
                timeRange = req.getStartTime() + " 至 " + LocalDateTime.now().format(dateTimeFormatter);
            } else if (StringUtils.isEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())) {
                timeRange = req.getStartTime() + " 以前";
            } else {
                timeRange = req.getStartTime() + " 至 " + req.getEndTime();
            }
        }
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("打标统计表", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        ExcelWriter writter = EasyExcel.write(response.getOutputStream(), JqbqDataStatisticsTableDto.class)
                .registerWriteHandler(new CustomSheetWriteHandler(timeRange, title, remark, columnCount))
                .build();
        WriteSheet sheet = EasyExcel.writerSheet("sheet1")
                .head(JqbqDataStatisticsTableDto.class)
                .relativeHeadRowIndex(1)
                .build();
        writter.write(jqbqDataStatisticsTableDtos, sheet);

        Workbook workbook = ((WriteWorkbookHolder)writter.writeContext().writeWorkbookHolder()).getWorkbook();
        Sheet sheetAt = workbook.getSheet("sheet1");

        int lastRowNum = sheetAt.getLastRowNum();
        Row row = sheetAt.createRow(lastRowNum + 1);
        Cell cell = row.createCell(0);
        cell.setCellValue("备注");
        sheetAt.addMergedRegion(new CellRangeAddress(lastRowNum + 1, lastRowNum + 1, 0, columnCount - 1));

        writter.finish();
    }



    @Override
    public List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable(JqbqDataStatisticsTableReq req) {
        List<Dict> pcsList = dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
                .select(Dict::getId, Dict::getDictName)
                .eq(Dict::getIsdelete, 1)
                .eq(Dict::getFatherId, "320412000000")
                .eq(Dict::getType, 25));
        Map<String, String> dwSub2Name = pcsList.stream().collect(Collectors.toMap(x -> x.getId().substring(0, 8), Dict::getDictName, (vo, v) -> v));

        LambdaQueryWrapper<WjscJqCjxx> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(WjscJqCjxx::getJjbh, WjscJqCjxx::getCjdw, WjscJqCjxx::getBzzt, WjscJqCjxx::getSpjg);
        wrapper.likeRight(WjscJqCjxx::getCjdw, "320412");
        Optional.ofNullable(req.getStartTime())
                .ifPresent(time -> wrapper.ge(WjscJqCjxx::getDjsjTime, time.replaceAll("[^0-9]", "")));
        Optional.ofNullable(req.getEndTime())
                .ifPresent(time -> wrapper.le(WjscJqCjxx::getDjsjTime, time.replaceAll("[^0-9]", "")));

        List<WjscJqCjxx> wjscJqCjxxes = wjscJqCjxxMapper.selectList(wrapper);
        Map<String, List<WjscJqCjxx>> dwSub2List = wjscJqCjxxes.stream()
                .filter(entity -> dwSub2Name.keySet().contains(entity.getCjdw().substring(0, 8)))
                .collect(Collectors.groupingBy(entity -> entity.getCjdw().substring(0, 8)));

        //分局
        dwSub2List.put("32041200", wjscJqCjxxes);

        List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable = dwSub2List.entrySet().parallelStream().map(entry -> {
            String key = entry.getKey();
            String unitName = dwSub2Name.getOrDefault(key, "武进分局");
            val builder = JqbqDataStatisticsTableDto.builder()
                    .unitId(key)
                    .unitName(unitName);
            List<WjscJqCjxx> jqCjxxList = entry.getValue();
            List<String> jjbhList = jqCjxxList.stream().map(WjscJqCjxx::getJjbh).collect(Collectors.toList());
            List<JqBz> jqBzs = jqBzMapper.selectList(Wrappers.<JqBz>lambdaQuery()
                    .select(JqBz::getJjbh, JqBz::getBzzt, JqBz::getSpjg)
                    .in(JqBz::getJjbh, jjbhList));
            long bzs = jqCjxxList.stream().filter(x -> x.getBzzt() != 0).count();
            int jqs = jqCjxxList.size();
            builder.jqs(jqs)
                    .bzs(bzs)
                    .bzl(new BigDecimal(bzs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(jqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%");
            Map<Integer, List<JqBz>> spjg2List = jqBzs.stream().collect(Collectors.groupingBy(JqBz::getSpjg));
            List<JqBz> thList = spjg2List.getOrDefault(2, Collections.emptyList()); // 退回
            int thzcs = thList.size();
            Set<String> thjjbhSet = thList.stream().map(JqBz::getJjbh).collect(Collectors.toSet());
            int thjqs = thjjbhSet.size();
            builder.thjqs(thjqs)
                    .thl(new BigDecimal(thjqs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(jqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%")
                    .thzcs(thzcs);
            long dshs = jqCjxxList.stream().filter(x -> x.getSpjg() == 0 || x.getSpjg() == 2).count();
            List<JqBz> tgList = spjg2List.getOrDefault(1, Collections.emptyList());
            int tgzcs = tgList.size();
            return builder.dshs(dshs)
                    .shzcs(tgzcs + thzcs)
                    .build();
        }).collect(Collectors.toList());
        return jqbqDataStatisticsTable;
    }

    @Override
    public List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable2(JqbqDataStatisticsTableReq req) {
        List<Dict> pcsList = dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
                .select(Dict::getId, Dict::getDictName)
                .eq(Dict::getIsdelete, 1)
                .eq(Dict::getFatherId, "320412000000")
                .eq(Dict::getType, 25));
        Map<String, String> dwSub2Name = pcsList.stream().collect(Collectors.toMap(x -> x.getId().substring(0, 8), Dict::getDictName, (vo, v) -> v));

        // 求总警情数
        List<String> cjlbList = Arrays.asList("01", "10", "04", "98", "02", "08", "06", "05", "09", "00");

        QueryWrapper<WjJqCount> wrapper2jqs = new QueryWrapper<>();
        wrapper2jqs.select("jjbh", "LEFT(jjdw, 8) AS jjdw", "bzzt", "spjg", "mark");
        Optional.ofNullable(req.getStartTime())
                .ifPresent(time -> wrapper2jqs.ge("bjdhsj_time", time));
        Optional.ofNullable(req.getEndTime())
                .ifPresent(time -> wrapper2jqs.le("bjdhsj_time", time));
        wrapper2jqs.in(!dwSub2Name.keySet().isEmpty(), "LEFT(jjdw, 8)", dwSub2Name.keySet())
                .in("LEFT(cjlb, 2)", cjlbList);
        List<WjJqCount> jqList = wjJqCountMapper.selectList(wrapper2jqs);
        if(jqList.isEmpty()) return new ArrayList<>();
        //各派出所对应警情数
        Map<String, Long> dwSub2jqs = jqList.stream().collect(Collectors.groupingBy(WjJqCount::getJjdw, Collectors.counting()));
        //分局警情数
        int fjJqs = jqList.size();

        // 已标注警情
        List<WjJqCount> mark1Jq = jqList.stream().filter(x -> 1 == x.getMark() || 3 == x.getBzzt()).collect(Collectors.toList());
        Map<String, List<WjJqCount>> dwSub2List = mark1Jq.stream()
                .filter(entity -> dwSub2Name.containsKey(entity.getJjdw()))
                .collect(Collectors.groupingBy(WjJqCount::getJjdw));

        dwSub2List.put("32041200", mark1Jq);

        List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable = dwSub2List.entrySet().parallelStream().map(entry -> {
            String key = entry.getKey();
            String unitName = dwSub2Name.getOrDefault(key, "武进分局");
            val builder = JqbqDataStatisticsTableDto.builder()
                    .unitId(key)
                    .unitName(unitName);
            List<WjJqCount> jqCjxxList = entry.getValue();

            List<String> jjbhList = jqCjxxList.stream().map(WjJqCount::getJjbh).collect(Collectors.toList());
            List<JqBz> jqBzs = Collections.emptyList();
            if (!jjbhList.isEmpty()) {
                jqBzs = jqBzMapper.selectList(Wrappers.<JqBz>lambdaQuery()
                        .select(JqBz::getJjbh, JqBz::getBzzt, JqBz::getSpjg)
                        .in(JqBz::getJjbh, jjbhList));
            }
            //标注数
            long bzs = jqCjxxList.stream().count();
            //int jqs = jqCjxxList.size();
            //警情数
            long jqs = key.equals("32041200") ? fjJqs : dwSub2jqs.get(key);
            String bzl = jqs == 0 ? "0%" : new BigDecimal(bzs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(jqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
            builder.jqs(jqs)
                    .bzs(bzs)
                    .bzl(bzl);
            Map<Integer, List<JqBz>> spjg2List = jqBzs.stream().collect(Collectors.groupingBy(JqBz::getSpjg));
            List<JqBz> thList = spjg2List.getOrDefault(2, Collections.emptyList()); // 退回
            //审核退回总次数
            int thzcs = thList.size();
            Set<String> thjjbhSet = thList.stream().map(JqBz::getJjbh).collect(Collectors.toSet());
            //退回警情数
            int thjqs = thjjbhSet.size();

            List<JqBz> tgList = spjg2List.getOrDefault(1, Collections.emptyList()); // 退回
            Set<String> tgjjbhSet = tgList.stream().map(JqBz::getJjbh).collect(Collectors.toSet());
            //通过警情数量
            tgjjbhSet.addAll(thjjbhSet);
            //审核警情数
            int shjqs = tgjjbhSet.size();
            String thl = shjqs == 0 ? "0%" : new BigDecimal(thjqs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(shjqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
            builder.thjqs(thjqs)
                    .thl(thl)
                    .thzcs(thzcs);
            //待审核数
            long dshs = jqCjxxList.stream().filter(x -> x.getSpjg() == 0 || x.getSpjg() == 2).count();
            //审核通过总次数
            int tgzcs = tgList.size();
            return builder.dshs(dshs)
                    .shzcs(tgzcs + thzcs)
                    .build();
        }).sorted(Comparator.comparingLong(JqbqDataStatisticsTableDto::getJqs)).collect(Collectors.toList());
        return jqbqDataStatisticsTable;
    }

    @Override
    public R<?> getSszrqList(SearchStrReq req) {
        LambdaQueryWrapper<Dict> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Dict::getIsdelete, 1)
                .eq(Dict::getType, 26)
                .likeRight(Dict::getId, "320412");
        if(StringUtils.isNotEmpty(req.getSearchStr())) wrapper.like(Dict::getDictName, req.getSearchStr());
        List<Dict> list = dictService.list(wrapper);
        return R.ok(list);
    }

    @Override
    public R<?> FuzzyJqbq4Lazy(FuzzyJqbq4LazyReq req) {
        String searchStr = req.getSearchStr();
        if (StringUtils.isBlank(searchStr) && CollectionUtils.isEmpty(req.getIds())) return R.ok(Collections.emptyList());
        List<Dict> list = dictService.lambdaQuery()
                .select(Dict::getId, Dict::getDictName, Dict::getFatherId, Dict::getType, Dict::getStaticIndex, Dict::getUpTime, Dict::getMemo)
                .in(!CollectionUtils.isEmpty(req.getTypeList()), Dict::getType, req.getTypeList())
                .eq(Dict::getIsdelete, 1)
                .nested(str ->
                    str.like(StringUtils.isNotBlank(searchStr), Dict::getDictName, searchStr)
                       .or()
                       .in(!CollectionUtils.isEmpty(req.getIds()),Dict::getId, req.getIds())
                )
                .list();
        if (list.isEmpty()) return R.ok(Collections.emptyList());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Map<Boolean, List<Dict>> collect = list.stream().collect(Collectors.partitioningBy(x -> x.getType() == 3));
        List<Dict> needDistinct = collect.getOrDefault(true, Collections.emptyList());
        List<Dict> finalList = new ArrayList<>(collect.getOrDefault(false, Collections.emptyList()));
        if (!needDistinct.isEmpty()) {
            List<Dict> list1 = needDistinct.stream().collect(Collectors.toMap(Dict::getMemo, data -> data, (v1, v2) -> LocalDateTime.parse(v2.getUpTime(), dateTimeFormatter).isBefore(LocalDateTime.parse(v1.getUpTime(), dateTimeFormatter)) ? v1 : v2))
                    .values()
                    .stream().collect(Collectors.toList());
            finalList.addAll(list1);
        }
        List<String> dictIds = finalList.stream().map(Dict::getId).collect(Collectors.toList());
        QueryWrapper<Dict> dictQueryWrapper = new QueryWrapper<Dict>()
                .select("father_id", "count(1) as count")
                .eq("isdelete", 1)
                .in("father_id", dictIds)
                .groupBy("father_id");
        List<Map<String, Object>> maps = dictMapper.selectMaps(dictQueryWrapper);
        Set<String> haveChild = maps.stream().flatMap(x -> x.keySet().stream()).collect(Collectors.toSet());
        list.forEach(x -> x.setLeaf(!haveChild.contains(x.getId())));

        Map<Integer, List<Dict>> level2List = finalList.stream()
                .collect(Collectors.groupingBy(Dict::getStaticIndex,
                        () -> new TreeMap<>(Comparator.reverseOrder()),
                        Collectors.toList()));
        List<Integer> levels = new ArrayList<>(level2List.keySet());
        Integer maxLevels = levels.get(0);
        List<Dict> newList = new ArrayList<>();
        while (true) {
            if (maxLevels == 1) break;
            List<Dict> dicts = level2List.getOrDefault(maxLevels, new ArrayList<>());
            List<Dict> finalNewList = newList;
            List<Dict> collect1 = dicts.stream().filter(x -> finalNewList.stream().map(Dict::getId).noneMatch(y -> y.equals(x.getId()))).collect(Collectors.toList());
            collect1.addAll(newList);
            newList = findFatherDict(collect1);
            maxLevels --;
        }
        return R.ok(newList);
    }

    private List<Dict> findFatherDict(List<Dict> list) {
        Map<String, List<Dict>> fatherId2List = list.stream().collect(Collectors.groupingBy(Dict::getFatherId));
        Set<String> ids = fatherId2List.keySet();
        List<Dict> dicts = dictService.lambdaQuery()
                .select(Dict::getId, Dict::getFatherId, Dict::getDictName)
                .in(!ids.isEmpty(), Dict::getId, ids)
                .eq(Dict::getIsdelete, 1)
                .list();
        dicts.forEach(x -> x.setChild(fatherId2List.get(x.getId())));
        return dicts;
    }

//    @Override
//    public List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable3(JqbqDataStatisticsTableReq req) {
//        List<Dict> pcsList = dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
//                .select(Dict::getId, Dict::getDictName)
//                .eq(Dict::getIsdelete, 1)
//                .eq(Dict::getFatherId, "320412000000")
//                .eq(Dict::getType, 25));
//        Map<String, String> dwSub2Name = pcsList.stream().collect(Collectors.toMap(x -> x.getId().substring(0, 8), Dict::getDictName, (vo, v) -> v));
//
//        // 求总警情数
//        List<String> cjlbList = Arrays.asList("01", "10", "04", "98", "02", "08", "06", "05", "09", "00");
//
//        QueryWrapper<WjJqCount> wrapper2jqs = new QueryWrapper<>();
//        wrapper2jqs.select("LEFT(jjdw, 8) AS dwSub", "COUNT(1) AS jqs");
//        Optional.ofNullable(req.getStartTime())
//                .ifPresent(time -> wrapper2jqs.ge("bjdhsj_time", time));
//        Optional.ofNullable(req.getEndTime())
//                .ifPresent(time -> wrapper2jqs.le("bjdhsj_time", time));
//        wrapper2jqs.eq("LEFT(jjdw, 6)", "320412")
//                .in("LEFT(cjlb, 2)", cjlbList);
//        wrapper2jqs.groupBy("LEFT(jjdw, 8)");
//        List<Map<String, Object>> result = wjJqCountMapper.selectMaps(wrapper2jqs);
//        //各派出所对应警情数
//        Map<String, Integer> dwSub2jqs = result.stream().collect(Collectors.toMap(map -> (String) map.get("dwSub"), map -> ((Number) map.get("jqs")).intValue()));
//
//        QueryWrapper<WjJqCount> wrapper2fjjqs = new QueryWrapper<>();
//        wrapper2fjjqs.eq("LEFT(jjdw, 6)", "320412")
//                .in("LEFT(cjlb, 2)", cjlbList);
//        Optional.ofNullable(req.getStartTime())
//                .ifPresent(time -> wrapper2fjjqs.ge("bjdhsj_time", time));
//        Optional.ofNullable(req.getEndTime())
//                .ifPresent(time -> wrapper2fjjqs.le("bjdhsj_time", time));
//        //分局警情数
//        Long fjJqs = wjJqCountMapper.selectCount(wrapper2fjjqs);
//
//
//        QueryWrapper<WjJqCount> wrapper = new QueryWrapper<>();
//        wrapper.select("jjbh", "jjdw", "spjg")
//                .eq("LEFT(jjdw, 6)", "320412")
//                .in("LEFT(cjlb, 2)", cjlbList)
//                .eq("mark", 1);
//        Optional.ofNullable(req.getStartTime())
//                .ifPresent(time -> wrapper.ge("bjdhsj_time", time));
//        Optional.ofNullable(req.getEndTime())
//                .ifPresent(time -> wrapper.le("bjdhsj_time", time));
//
//        List<WjJqCount> wjscJqCjxxes = wjJqCountMapper.selectList(wrapper);
//        if(wjscJqCjxxes.isEmpty()) return new ArrayList<>();
//        Map<String, List<WjJqCount>> dwSub2List = wjscJqCjxxes.stream()
//                .filter(entity -> dwSub2Name.keySet().contains(entity.getJjdw().substring(0, 8)))
//                .collect(Collectors.groupingBy(entity -> entity.getJjdw().substring(0, 8)));
//
//        dwSub2List.put("32041200", wjscJqCjxxes);
//
//        List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable = dwSub2List.entrySet().parallelStream().map(entry -> {
//            String key = entry.getKey();
//            String unitName = dwSub2Name.getOrDefault(key, "武进分局");
//            val builder = JqbqDataStatisticsTableDto.builder()
//                    .unitId(key)
//                    .unitName(unitName);
//            List<WjJqCount> jqCjxxList = entry.getValue();
//
//            List<String> jjbhList = jqCjxxList.stream().map(WjJqCount::getJjbh).collect(Collectors.toList());
//            List<JqBz> jqBzs = jqBzMapper.selectList(Wrappers.<JqBz>lambdaQuery()
//                    .select(JqBz::getJjbh, JqBz::getBzzt, JqBz::getSpjg)
//                    .in(JqBz::getJjbh, jjbhList));
//            //标注数
//            long bzs = jqCjxxList.stream().count();
//            //int jqs = jqCjxxList.size();
//            //警情数
//            int jqs = key.equals("32041200") ? fjJqs.intValue() : dwSub2jqs.get(key);
//            String bzl = jqs == 0 ? "0%" : new BigDecimal(bzs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(jqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
//            builder.jqs(jqs)
//                    .bzs(bzs)
//                    .bzl(bzl);
//            Map<Integer, List<JqBz>> spjg2List = jqBzs.stream().collect(Collectors.groupingBy(JqBz::getSpjg));
//            List<JqBz> thList = spjg2List.getOrDefault(2, Collections.emptyList()); // 退回
//            //审核退回总次数
//            int thzcs = thList.size();
//            Set<String> thjjbhSet = thList.stream().map(JqBz::getJjbh).collect(Collectors.toSet());
//            //退回警情数
//            int thjqs = thjjbhSet.size();
//            //审核警情数
//            long shjqs = jqCjxxList.stream().filter(x -> x.getSpjg() == 1 || x.getSpjg() == 2).count();
//            String thl = shjqs == 0 ? "0%" : new BigDecimal(thjqs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(shjqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
//            builder.thjqs(thjqs)
//                    .thl(thl)
//                    .thzcs(thzcs);
//            //待审核数
//            long dshs = jqCjxxList.stream().filter(x -> x.getSpjg() == 0 || x.getSpjg() == 2).count();
//            List<JqBz> tgList = spjg2List.getOrDefault(1, Collections.emptyList());
//            //审核通过总次数
//            int tgzcs = tgList.size();
//            return builder.dshs(dshs)
//                    .shzcs(tgzcs + thzcs)
//                    .build();
//        }).sorted(Comparator.comparingLong(JqbqDataStatisticsTableDto::getJqs)).collect(Collectors.toList());
//        return jqbqDataStatisticsTable;
//    }

    @Override
    public List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable3(JqbqDataStatisticsTableReq req) {
        List<Dict> pcsList = dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
                .select(Dict::getId, Dict::getDictName)
                .eq(Dict::getIsdelete, 1)
                .eq(Dict::getFatherId, "320412000000")
                .eq(Dict::getType, 25));
        Map<String, String> dwSub2Name = pcsList.stream().collect(Collectors.toMap(x -> x.getId().substring(0, 8), Dict::getDictName, (vo, v) -> v));

        // 求总警情数
        List<String> cjlbList = Arrays.asList("01", "10", "04", "98", "02", "08", "06", "05", "09", "00");

        QueryWrapper<WjJqCount> wrapper2jqs = new QueryWrapper<>();
        wrapper2jqs.select("LEFT(jjdw, 8) AS dwSub", "COUNT(1) AS jqs");
        Optional.ofNullable(req.getStartTime())
                .ifPresent(time -> wrapper2jqs.ge("bjdhsj_time", time));
        Optional.ofNullable(req.getEndTime())
                .ifPresent(time -> wrapper2jqs.le("bjdhsj_time", time));
        wrapper2jqs.eq("LEFT(jjdw, 6)", "320412")
                .in("LEFT(cjlb, 2)", cjlbList);
        wrapper2jqs.groupBy("LEFT(jjdw, 8)");
        List<Map<String, Object>> result = wjJqCountMapper.selectMaps(wrapper2jqs);
        //各派出所对应警情数
        Map<String, Integer> dwSub2jqs = result.stream().collect(Collectors.toMap(map -> (String) map.get("dwSub"), map -> ((Number) map.get("jqs")).intValue()));

        QueryWrapper<WjJqCount> wrapper2fjjqs = new QueryWrapper<>();
        wrapper2fjjqs.eq("LEFT(jjdw, 6)", "320412")
                .in("LEFT(cjlb, 2)", cjlbList);
        Optional.ofNullable(req.getStartTime())
                .ifPresent(time -> wrapper2fjjqs.ge("bjdhsj_time", time));
        Optional.ofNullable(req.getEndTime())
                .ifPresent(time -> wrapper2fjjqs.le("bjdhsj_time", time));
        //分局警情数
        Long fjJqs = wjJqCountMapper.selectCount(wrapper2fjjqs);


        QueryWrapper<WjJqCount> wrapper = new QueryWrapper<>();
        wrapper.select("jjbh", "jjdw", "spjg")
                .eq("LEFT(jjdw, 6)", "320412")
                .in("LEFT(cjlb, 2)", cjlbList)
                .eq("mark", 1);
        Optional.ofNullable(req.getStartTime())
                .ifPresent(time -> wrapper.ge("bjdhsj_time", time));
        Optional.ofNullable(req.getEndTime())
                .ifPresent(time -> wrapper.le("bjdhsj_time", time));

        List<WjJqCount> wjscJqCjxxes = wjJqCountMapper.selectList(wrapper);
        if(wjscJqCjxxes.isEmpty()) return new ArrayList<>();
        Map<String, List<WjJqCount>> dwSub2List = wjscJqCjxxes.stream()
                .filter(entity -> dwSub2Name.keySet().contains(entity.getJjdw().substring(0, 8)))
                .collect(Collectors.groupingBy(entity -> entity.getJjdw().substring(0, 8)));

        dwSub2List.put("32041200", wjscJqCjxxes);

        List<JqbqDataStatisticsTableDto> jqbqDataStatisticsTable = dwSub2List.entrySet().parallelStream().map(entry -> {
            String key = entry.getKey();
            String unitName = dwSub2Name.getOrDefault(key, "武进分局");
            val builder = JqbqDataStatisticsTableDto.builder()
                    .unitId(key)
                    .unitName(unitName);
            List<WjJqCount> jqCjxxList = entry.getValue();

            List<String> jjbhList = jqCjxxList.stream().map(WjJqCount::getJjbh).collect(Collectors.toList());
            List<JqBz> jqBzs = jqBzMapper.selectList(Wrappers.<JqBz>lambdaQuery()
                    .select(JqBz::getJjbh, JqBz::getBzzt, JqBz::getSpjg)
                    .in(JqBz::getJjbh, jjbhList));
            //标注数
            long bzs = jqCjxxList.stream().count();
            //int jqs = jqCjxxList.size();
            //警情数
            int jqs = key.equals("32041200") ? fjJqs.intValue() : dwSub2jqs.get(key);
            String bzl = jqs == 0 ? "0%" : new BigDecimal(bzs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(jqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
            builder.jqs(jqs)
                    .bzs(bzs)
                    .bzl(bzl);
            Map<Integer, List<JqBz>> spjg2List = jqBzs.stream().collect(Collectors.groupingBy(JqBz::getSpjg));
            List<JqBz> thList = spjg2List.getOrDefault(2, Collections.emptyList()); // 退回
            //审核退回总次数
            int thzcs = thList.size();
            Set<String> thjjbhSet = thList.stream().map(JqBz::getJjbh).collect(Collectors.toSet());
            //退回警情数
            int thjqs = thjjbhSet.size();

            List<JqBz> tgList = spjg2List.getOrDefault(1, Collections.emptyList()); // 退回
            Set<String> tgjjbhSet = tgList.stream().map(JqBz::getJjbh).collect(Collectors.toSet());
            //通过警情数量
            tgjjbhSet.addAll(thjjbhSet);
            //审核警情数
            int shjqs = tgjjbhSet.size();
            String thl = shjqs == 0 ? "0%" : new BigDecimal(thjqs + "").multiply(new BigDecimal("100")).divide(new BigDecimal(shjqs + ""), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%";
            builder.thjqs(thjqs)
                    .thl(thl)
                    .thzcs(thzcs);
            //待审核数
            long dshs = jqCjxxList.stream().filter(x -> x.getSpjg() == 0 || x.getSpjg() == 2).count();
            //审核通过总次数
            int tgzcs = tgList.size();
            return builder.dshs(dshs)
                    .shzcs(tgzcs + thzcs)
                    .build();
        }).sorted(Comparator.comparingLong(JqbqDataStatisticsTableDto::getJqs)).collect(Collectors.toList());
        return jqbqDataStatisticsTable;
    }

    @Override
    public R<?> updateJQBZ(ReqJQBZUpdate dto) {
        try {
            // 1. 基础校验
            User user = validateAndGetUser();
            WjscJqCjxx baseInfo = validateAndGetBaseInfo(dto.getJjbh());

            // 2. 处理地址标签（核心逻辑）
            processAddressLabel(dto, baseInfo, user);

            // 3. 处理其他标签
           // processCommonLabel(dto.getPersonM(), "人员", this::validatePersonLabel);
            processCommonLabel(dto.getResultM(), "结果", this::validateResultLabel);
            processCommonLabel(dto.getToolM(), "手段", this::validateToolLabel);
            processCommonLabel(dto.getReasonM(), "原因", this::validateReasonLabel);
            processTimeLabel(dto, baseInfo);

            // 4. 更新主记录
            updateMainRecord(baseInfo, user);

            // 5. 保存操作记录
            saveOperationRecord(dto, user);

            return R.ok();
        } catch (RuntimeException e) {
            log.warn("业务校验失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("警情标注更新异常", e);
            return R.fail("系统处理异常，请稍后重试");
        }
    }

    @Override
    public List<String> getIdByQuanXuan(List<String> bzbq) {
        Set<String> allIds = new HashSet<>();
        findAllChildren(bzbq, allIds);
        return new ArrayList<>(allIds);
    }

    private void findAllChildren(List<String> ids, Set<String> all) {
        if (CollectionUtils.isEmpty(ids)) return;
        all.addAll(ids);
        List<String> children = dictMapper.findBatchChildren(ids);
        if(!children.isEmpty()) findAllChildren(children, all);
    }

    @Override
    public R<?> updateJQBZPerson(ReqJQBZPerson dto) {

//        try {
//            // 1. 参数校验
//            validateBaseParams(dto);
//
//            ProcessJgResult jgResult = processJgInfo(dto.getJgbh());
//
//            // 3. 处理人员标签
//            ProcessPersonResult personResult = processPersonLabels(dto.getPersonM(), jgResult);
//
//            // 4. 更新主记录
//            updateMainRecord(dto, personResult, jgResult);
//
//            // 5. 保存操作记录
//            saveOperationRecord(dto);
//
//            return R.ok();
//        } catch (RuntimeException e) {
//            log.warn("业务校验失败: {}", e.getMessage());
//            return R.fail(e.getMessage());
//        } catch (Exception e) {
//            log.error("人员标注更新异常", e);
//            return R.fail("系统处理异常，请稍后重试");
//        }
        return null;
    }

    private ProcessPersonResult processPersonLabels(String personM, ProcessJgResult jgResult) {
        List<String> labelIds = parseLabels(personM);
        StringBuilder bzs = new StringBuilder();
        List<String> validLabels = new ArrayList<>();

        for (String labelId : labelIds) {
            Dict dict = getValidDict(labelId);
            validateLabelDepth(dict, "人员");
            buildLabelPath(bzs, dict);

            // 处理职业相关标签
            if ("人员职业".equals(dict.getMemo()) && dict.getStaticIndex() == 4) {
                handleZyLabel(jgResult, dict);
                validLabels.add(jgResult.getJgbh());
            } else {
                validLabels.add(labelId);
            }
        }

        return new ProcessPersonResult(bzs.toString(), validLabels);
    }

    private void handleZyLabel(ProcessJgResult jgResult, Dict dict) {



    }

    @Data
    private static class ProcessPersonResult {
        private final String bzs;
        private final List<String> labels;

        public ProcessPersonResult(String bzs, List<String> labels) {
            this.bzs = bzs;
            this.labels = labels;
        }
    }

    private ProcessJgResult processJgInfo(String jgbhInput) {
        ProcessJgResult result = new ProcessJgResult();
        if (StringUtils.isBlank(jgbhInput)) return result;

        String[] parts = jgbhInput.split("\\|");
        String id = parts[0];
        String name = parts.length > 1 ? parts[1] : "";

        if (!id.startsWith("ZY")) {
            id = "ZY_" + id;
            // 创建新字典记录
            Dict newDict = createZyDict(id, name);
            dictMapper.insert(newDict);
            dictCacheService.refreshCache();
        }

        result.setJgbh(id);
        result.setDwmc(name);
        return result;
    }

    private Dict createZyDict(String id, String name) {
        return Dict.builder()
                .id(id)
                .dictName(name)
                .staticIndex(5)
                .type(4)
                .memo("人员职业-自定义")
                .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .build();
    }

    @Data
    private static class ProcessJgResult {
        private String jgbh;
        private String dwmc;
    }

    private void validateBaseParams(ReqJQBZPerson dto) {
        if (dto.getPersonM() == null || dto.getPersonM().isEmpty()) {
            throw new RuntimeException("人员标签不能为空");
        }
    }



    private void processAddressLabel(ReqJQBZUpdate dto, WjscJqCjxx baseInfo, User user) {
        List<String> addressLabels = dto.getAddressM();
        StringBuilder bzs = new StringBuilder();

        for (String labelId : addressLabels) {
            Dict dict = getValidDict(labelId);
            buildLabelPath(bzs, dict);

            // 仅处理包含"发生部位"的标签
            if (dict.getMemo().contains("发生部位")) {
                handleSpecialAddress(dto, baseInfo, user, dict);
            }
        }

        // 更新处理后的地址标签
        baseInfo.setAddressm(String.join(",", addressLabels));
        baseInfo.setJqbz(bzs.toString().trim());
    }

    private void handleSpecialAddress(ReqJQBZUpdate dto, WjscJqCjxx baseInfo, User user, Dict dict) {
        switch (dict.getStaticIndex()) {
            case 5:
                handleLevel5Address(baseInfo, dict);
                break;
            case 4:
                handleLevel4Address(dto, baseInfo, user, dict);
                break;
            default:
                validateLabelDepth(dict, "地址");
        }
    }

    private void handleLevel5Address(WjscJqCjxx baseInfo, Dict dict) {
        baseInfo.setJgbh(dict.getId());
        baseInfo.setDwmc(dict.getDictName());
        baseInfo.setDwdz(dict.getRemark());

        // 更新字典父关系
        dictMapper.updateById(Dict.builder().id(dict.getId()).fatherId(dict.getFatherId()).build());
    }

    private void handleLevel4Address(ReqJQBZUpdate dto, WjscJqCjxx baseInfo, User user, Dict dict) {
        // 验证是否存在末端信息
        if (StringUtils.isBlank(baseInfo.getDwmc())) {
            throw new RuntimeException("发生部位未选到最后一层");
        }

        // 生成新节点并刷新缓存
        Dict newDict = createNewDictNode(baseInfo, dict, user);
        dictMapper.insert(newDict);
        dictCacheService.refreshCache();

        // 替换标签ID
        Collections.replaceAll(dto.getAddressM(), dict.getId(), newDict.getId());
        dto.setAddressM(dto.getAddressM());
    }

    private List<String> parseLabels(String labels) {
        if (StringUtils.isBlank(labels)) return Collections.emptyList();
        return Arrays.stream(labels.split(","))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }
    private void buildLabelPath(StringBuilder bzs, Dict dict) {
        String currentId = dict.getId();
        while (StringUtils.isNotBlank(currentId)) {
            bzs.append(currentId).append(" ");
            currentId = Optional.ofNullable(dictMapper.selectById(currentId))
                    .map(Dict::getFatherId)
                    .orElse("");
        }
    }

    private Dict getValidDict(String id) {
        return Optional.ofNullable(dictCacheService.getById(id))
                .orElseThrow(() -> new RuntimeException("无效的标签ID: " + id));
    }

    private Dict createNewDictNode(WjscJqCjxx info, Dict fatherDict, User user) {
        return Dict.builder()
                .id(UUID.randomUUID().toString())
                .dictName(info.getDwmc())
                .fatherId(fatherDict.getId())
                .staticIndex(5)
                .memo(fatherDict.getMemo() + "-" + info.getDwmc())
                .remark(info.getDwdz())
                .type(3)
                .upTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .createTime(user.getIdCard())
                .createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .build();
    }
    //endregion

    //region ---------- 通用校验方法 ----------
    private void validateLabelDepth(Dict dict, String labelType) {
        if (dictMapper.selectCount(new QueryWrapper<Dict>().eq("father_id", dict.getId())) > 0) {
            throw new RuntimeException(labelType + "标签[" + dict.getDictName() + "]未选择到最底层");
        }
    }

    private void processCommonLabel(List<String> labels, String labelType, Consumer<List<String>> validator) {
        validator.accept(labels);
    }

    private void validatePersonLabel(List<String> labels) {
        if (labels.isEmpty()) throw new RuntimeException("人员标签必选");
        labels.forEach(id -> validateLabelDepth(getValidDict(id), "人员"));
    }

    private void validateResultLabel(List<String> labels) {
        if (labels.isEmpty()) throw new RuntimeException("结果标签必选");
        labels.forEach(id -> validateLabelDepth(getValidDict(id), "结果"));
    }

    private void validateToolLabel(List<String> labels) {
        if (labels.isEmpty()) throw new RuntimeException("手段标签必选");
        labels.forEach(id -> validateLabelDepth(getValidDict(id), "手段"));
    }

    private void validateReasonLabel(List<String> labels) {
        if (labels.isEmpty()) throw new RuntimeException("原因标签必选");
        labels.forEach(id -> validateLabelDepth(getValidDict(id), "原因"));
    }
    //endregion

    //region ---------- 基础校验 ----------
    private User validateAndGetUser() {
        User user = UserUtils.getUser();
        if (user == null || StringUtils.isBlank(user.getIdCard())) {
            throw new RuntimeException("用户身份验证失败");
        }
        return user;
    }

    private WjscJqCjxx validateAndGetBaseInfo(String jjbh) {
        if (StringUtils.isBlank(jjbh)) {
            throw new RuntimeException("警情编号不能为空");
        }
        return Optional.ofNullable(wjscJqCjxxMapper.selectById(jjbh))
                .orElseThrow(() -> new RuntimeException("警情信息不存在"));
    }
    //endregion

    //region ---------- 数据库操作 ----------
    private void updateMainRecord(WjscJqCjxx baseInfo, User user) {
        baseInfo.setBzzt(2); // 标注状态：已标注待审核
        baseInfo.setMark(1);
        baseInfo.setSpjg(0);
        baseInfo.setUnit(user.getOrganization().getJSONObject(0).getString("organization_id"));
        baseInfo.setBzr(user.getIdCard());
        baseInfo.setBzTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        baseInfo.setBzrxm(user.getName());

        if (wjscJqCjxxMapper.updateById(baseInfo) == 0) {
            throw new RuntimeException("警情信息更新失败");
        }
    }

    private void saveOperationRecord(ReqJQBZUpdate dto, User user) {
        JqBz record = JqBz.builder()
                .bzr(user.getIdCard())
                .bzTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .hisType(1)
                .jjbh(dto.getJjbh())
                .addressM(String.join(",", dto.getAddressM()))
                .resultM(String.join(",", dto.getResultM()))
                .timeM(dto.getTimeM())
                .toolM(String.join(",", dto.getToolM()))
                .reasonM(String.join(",", dto.getReasonM()))
                .build();

        if (jqBzMapper.insert(record) == 0) {
            throw new RuntimeException("操作记录保存失败");
        }
    }
    //endregion

    //region ---------- 时间标签处理 ----------
    private void processTimeLabel(ReqJQBZUpdate dto, WjscJqCjxx baseInfo) {
        if (StringUtils.isBlank(dto.getTimeM())) {
            throw new RuntimeException("时间标签不能为空");
        }
        validateTimeFormat(dto.getTimeM());
        baseInfo.setTimem(dto.getTimeM());
    }

    private void validateTimeFormat(String time) {
        LocalDateTimeUtil.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
