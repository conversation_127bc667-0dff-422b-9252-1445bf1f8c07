package com.hl.test.service.handle;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

public class CustomSheetWriteHandler implements SheetWriteHandler {

    private final String timeRange;
    private final String title;
    private final String remark;
    private final int columnSize;

    public CustomSheetWriteHandler(String timeRange, String title, String remark, int columnSize) {
        this.timeRange = timeRange;
        this.title = title;
        this.remark = remark;
        this.columnSize = columnSize;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {}

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        // 第一行：标题
//        Cell cell0 = sheet.createRow(0).createCell(0);
//        cell0.setCellValue(title);
//        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnSize - 1));

        // 第二行：时间
        Row row1 = sheet.createRow(0);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue("时间：" + timeRange);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnSize - 1));

    }
}
