package com.hl.test.service.handle;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.hl.test.domain.vo.LabelDictDto;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CellMergeStrategy implements SheetWriteHandler {
    private final List<List<String>> dataList;
    private final List<Integer> mergeColumnIndexList; // 要合并的列索引，如 0=一级标签，1=二级标签

    public CellMergeStrategy(List<LabelDictDto> dictList, List<Integer> mergeColumnIndexList) {
        // 提取每行的字段值作为合并依据
        this.dataList = dictList.stream()
                .map(d -> Arrays.asList(d.getLevel1(), d.getLevel2(), d.getLevel3(), d.getLevel4(), d.getLevel5()))
                .collect(Collectors.toList());
        this.mergeColumnIndexList = mergeColumnIndexList;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        for (Integer colIndex : mergeColumnIndexList) {
            mergeSameCell(sheet, colIndex);
        }
    }

    private void mergeSameCell(Sheet sheet, int colIndex) {
        int startRow = 1; // 从第2行开始（第1行为表头）
        int endRow = dataList.size();

        String previous = null;
        int mergeStart = startRow;

        for (int i = startRow; i <= endRow; i++) {
            String current = (i == endRow) ? null : dataList.get(i - 1).get(colIndex);

            if (previous == null) {
                previous = current;
                continue;
            }

            if (!Objects.equals(previous, current)) {
                if (i - mergeStart > 1) {
                    // 合并单元格（起始行, 结束行, 列号, 列号）
                    sheet.addMergedRegion(new CellRangeAddress(mergeStart, i - 1, colIndex, colIndex));
                }
                mergeStart = i;
                previous = current;
            }
        }
    }
}
