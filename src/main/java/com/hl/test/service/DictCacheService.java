package com.hl.test.service;

import com.hl.test.domain.Dict;
import com.hl.test.mapper.DictMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DictCacheService {
    private final DictMapper dictMapper;
    private Map<String, Dict> cache;

    @PostConstruct
    public void refreshCache() {
        this.cache = dictMapper.selectList(null)
                .stream()
                .collect(Collectors.toMap(Dict::getId, Function.identity()));
    }

    public Dict getById(String id) {
        return cache.get(id);
    }
}
