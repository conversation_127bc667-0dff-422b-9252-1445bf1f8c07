package com.hl.test.cache;

import com.hl.test.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class DictCacheLoader implements CommandLineRunner {

    @Autowired
    private DictService dictService;

    @Override
    public void run(String... args) throws Exception {
        dictService.saveDictTree2Redis(3, null);
    }
}
