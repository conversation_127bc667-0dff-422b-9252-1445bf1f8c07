package com.hl.test.config;


import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Component
public class Test {

//    @PostConstruct
//    public void testDb(){
//        MysqlBinLogServ serv = new MysqlBinLogServ(new GraceThreadPool());
//        serv.run();
//    }
    private static String schema = "wj_jq";
    private static String host = "*************";
    private static int port = 3306;
    static String user = "hl";
    static String pwd = "hl1234!@#$";
    private static HashMap<Long, String> table_name = new HashMap<>();

    @Resource
    private JdbcTemplate jdbcTemplate;

    @PostConstruct
    public void main() {
    }


}
