package com.hl.test.config.Sql;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class XssInjectionParse implements ConstraintValidator<XssInjection, Object> {
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext context) {
        // 获取数据值
        String s = String.valueOf(o);
        if (Strings.isEmpty(s)) {
            return true;
        }
        s = s.trim();
        Pattern matcher = Pattern.compile("script", Pattern.CASE_INSENSITIVE);
        Matcher m = matcher.matcher(s);
        boolean isMatch = m.find();
        if (isMatch) {
            log.error("xss injection: {}", s);
        }
        return !isMatch;
    }
}
