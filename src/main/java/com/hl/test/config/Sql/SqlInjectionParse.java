package com.hl.test.config.Sql;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.engine.constraintvalidation.ConstraintValidatorContextImpl;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Map;

@Slf4j
public class SqlInjectionParse implements ConstraintValidator<SqlInjection, Object> {
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext context) {
        if (o == null) {
            return true;
        }
        // 校验逻辑
        ConstraintValidatorContextImpl con = (ConstraintValidatorContextImpl) context;
        // 获取注解中的属性值
        Map<String, Object> maps = con.getConstraintDescriptor().getAttributes();
        // 获取设置的或者默认的正则表达式
        // 获取设置的或者默认的正则表达式
        String pattern = (String) maps.get("pattern");
        boolean isString = (boolean) maps.get("isString");

        // 获取数据值
        if (!isString) {
            if (o instanceof List<?>) {
                for (Object item : (List<?>) o) {
                    if (UtilsBasic.isSqlInjection(String.valueOf(item), pattern)) {
                        log.error("sql injection: {}", item);
                        return false;
                    }
                }
                return true;
            }
            else if (o instanceof Map<?, ?>) {
                for (Object item : ((Map<?, ?>) o).keySet()) {
                    if (UtilsBasic.isSqlInjection(String.valueOf(item), pattern)) {
                        log.error("sql injection: {}", item);
                        return false;
                    }
                }
                for (Object item : ((Map<?, ?>) o).values()) {
                    if (UtilsBasic.isSqlInjection(String.valueOf(item), pattern)) {
                        log.error("sql injection: {}", item);
                        return false;
                    }
                }
                return true;
            }
        }
        String data = String.valueOf(o);
        boolean isMatch = UtilsBasic.isSqlInjection(data, pattern);
        if (isMatch) {
            log.error("sql injection: {}", data);
        }
        return !isMatch;
    }
}
