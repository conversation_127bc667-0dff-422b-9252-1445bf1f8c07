package com.hl.test.config.Sql;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintStream;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

@Slf4j
public class UtilsBasic {
    // 异常信息 转为 字符串
    public static String getTrace(Throwable e) {
        FastByteArrayOutputStream baos = new FastByteArrayOutputStream();
        e.printStackTrace(new PrintStream(baos));
        String exceptionStr = baos.toString();
        String start_with = "com.hl";
        if (Strings.isEmpty(exceptionStr) || Strings.isEmpty(start_with)) {
            return exceptionStr;
        }
        StringBuilder sb = StrUtil.builder(e.getClass().getName());
        sb.append(": ");
        sb.append(e.getMessage());
        String[] exceptionStrs = exceptionStr.split("\n");
        start_with = "at " + start_with;
        for (String str : exceptionStrs) {
            if (Strings.isEmpty(str)) {
                continue;
            }
            if (str.trim().contains(start_with)) {
                sb.append("\n\t").append(str.trim());
            }
        }
        return sb.toString();
    }

    public static String getCallStack() {
        Throwable exception = new Throwable();
        StackTraceElement[] stackTraceElements = exception.getStackTrace();
        StringBuilder CallStack = new StringBuilder();
        String start_with = "com.hl";
        if (stackTraceElements != null) {
            // 前2个不要了
            int discard = 0;
            for (StackTraceElement element : stackTraceElements) {
                discard++;
                if (discard <= 2) {
                    continue;
                }
                if (!element.getClassName().contains(start_with)) {
                    continue;
                }
                CallStack.append("[").append(element.getClassName()).append(":")
                        .append(element.getLineNumber()).append("]")
                        .append(element.getMethodName()).append("\n\t");
            }
        }
        return CallStack.toString().trim();
    }

       // 字符串的过滤
    public static String trim(String str, char trim) {
        if (Strings.isEmpty(str)) {
            return str;
        }
        int i;
        for (i = 0; i < str.length(); i++) {
            if (str.charAt(i) == trim) {
                continue;
            }
            break;
        }
        int j;
        for (j = str.length() - 1; j >= 0; j--) {
            if (str.charAt(j) == trim) {
                continue;
            }
            break;
        }
        return str.substring(i, j + 1);
    }

    // 读取Resource中的json5文件
    public static JSONObject readJson5Obj(String file_name) {
        return JSONObject.parseObject(getJson5String(file_name));
    }

    public static JSONArray readJson5Arr(String file_name) {
        return JSONArray.parseArray(getJson5String(file_name));
    }

    public static String getJson5String(String file_name) {
        StringBuilder content = new StringBuilder();
        try (InputStream json_is = UtilsBasic.class.getClassLoader().getResourceAsStream(file_name)) {
            if (json_is == null) {
                return null;
            }
            try (BufferedReader br = new BufferedReader(new InputStreamReader(json_is))) {
                while (true) {
                    String line = br.readLine();
                    if (line == null) {
                        break;
                    }
                    if (Strings.isEmpty(line)) {
                        continue;
                    }
                    line = line.replaceAll("\\s+", "");
                    if (line.contains("//")) {
                        line = line.substring(0, line.indexOf("//"));
                    }
                    content.append(line);
                }
            }
            return content.toString();
        }
        catch (Exception e) {
            log.error(content.toString(), e);
        }
        return null;
    }

    // 对象转map
    public static Map<String, Object> convertToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }
        return map;
    }

    private final static HashMap<String, String> time_format = new HashMap<String, String>() {{
        put("^20[234]\\d-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$", "yyyy-MM-dd");
        put("^20[234]\\d/(0[1-9]|1[012])/(0[1-9]|[12][0-9]|3[01])$", "yyyy/MM/dd");
        put("^20[234]\\d-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])\\s+([01]\\d|2[01234]):[012345]\\d$", "yyyy-MM-dd HH:mm");
        put("^20[234]\\d/(0[1-9]|1[012])/(0[1-9]|[12][0-9]|3[01])\\s+([01]\\d|2[01234]):[012345]\\d$", "yyyy/MM/dd HH:mm");
        put("^20[234]\\d-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])\\s+([01]\\d|2[01234]):[012345]\\d:[012345]\\d$", "yyyy-MM-dd HH:mm:ss");
        put("^20[234]\\d/(0[1-9]|1[012])/(0[1-9]|[12][0-9]|3[01])\\s+([01]\\d|2[01234]):[012345]\\d:[012345]\\d$", "yyyy/MM/dd HH:mm:ss");
    }};

    // 如果fill2end为true时，会补充到当天结束
    public static long timeStr2Long(String time) {
        return timeStr2Long(time, false);
    }

    public static long timeStr2Long(String time, boolean fill2end) {
        try {
            if (Strings.isBlank(time)) {
                return -1;
            }

            // 判断是不是天数
            Pattern pattern = Pattern.compile("\\d+[天日]");
            if (pattern.matcher(time).find()) {
                int day = Integer.parseInt(time.replaceAll("[^0-9]", ""));
                SimpleDateFormat sdf_h = new SimpleDateFormat("yyyy-MM-dd");
                SimpleDateFormat sdf_s = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                String str = sdf_h.format(new Date()) + " 23:59:59";
                return sdf_s.parse(str).getTime() + day * 24 * 3600000L;
            }

            // 判断时间格式
            for (String _format : time_format.keySet()) {
                if (Pattern.matches(_format, time)) {
                    if (fill2end) {
                        String _time = time;
                        String time_f = time_format.get(_format);
                        if (time.length() == 10) {
                            _time += " 23:59:59";
                            time_f += " HH:mm:ss";
                        }
                        else if (time.length() == 13) {
                            _time += ":59:59";
                            time_f += ":mm:ss";
                        }
                        else if (time.length() == 16) {
                            _time += ":59";
                            time_f += ":ss";
                        }
                        SimpleDateFormat sdf = new SimpleDateFormat(time_f);
                        return sdf.parse(_time).getTime();
                    }
                    else {
                        SimpleDateFormat sdf = new SimpleDateFormat(time_format.get(_format));
                        return sdf.parse(time).getTime();
                    }
                }
            }
            log.error("不认识的时间结束格式 {}", time);
            return -1;
        }
        catch (ParseException e) {
            log.error("", e);
            return -1;
        }
    }

    public static boolean isSqlInjection(String str, String pattern) {
        if (Strings.isEmpty(str)) {
            return false;
        }
        return str.trim().matches(pattern);
    }

    public final static String SqlInjection = ".*['\\s()<>|=;]+.*";

    public static boolean isSqlInjection(String str) {
        return isSqlInjection(str, SqlInjection);
    }
}
