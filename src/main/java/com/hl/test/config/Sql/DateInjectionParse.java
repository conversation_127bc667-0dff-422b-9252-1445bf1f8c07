package com.hl.test.config.Sql;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

@Slf4j
public class DateInjectionParse implements ConstraintValidator<DateInjection, Object> {
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext context) {
        if (o == null) {
            return true;
        }
        // 获取数据值
        String data = String.valueOf(o);
        if (Strings.isEmpty(data)) {
            return true;
        }
        data = data.trim();

        long bbb = UtilsBasic.timeStr2Long(data);
        return bbb > 0;
    }
}
