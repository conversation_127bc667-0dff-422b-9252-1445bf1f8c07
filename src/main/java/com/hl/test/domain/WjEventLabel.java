package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("wj_event_label")
public class WjEventLabel {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    @TableField("event_name")
    private String eventName;
    private Integer status;
}
