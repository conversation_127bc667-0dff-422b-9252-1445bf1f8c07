package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 警情分析常州接警信息
 */
@ApiModel(description="警情分析常州接警信息")
@Data
@TableName(value = "wjsc_jq_jjxx")
public class WjscJqJjxx {
    /**
     * 接警编号
     */
    @TableField(value = "jjbh")
    @ApiModelProperty(value="接警编号")
    private String jjbh;

    /**
     * 处警标识
     */
    @TableField(value = "cjbs")
    @ApiModelProperty(value="处警标识")
    private String cjbs;

    /**
     * 接警报警人
     */
    @TableField(value = "bjr")
    @ApiModelProperty(value="接警报警人")
    private String bjr;

    /**
     * 报警类型
     */
    @TableField(value = "bjlx")
    @ApiModelProperty(value="报警类型")
    private String bjlx;

    /**
     * 接警单位名称
     */
    @TableField(value = "jjdwmc")
    @ApiModelProperty(value="接警单位名称")
    private String jjdwmc;

    /**
     * 接警报警时间
     */
    @TableField(value = "bjdhsj_time")
    @ApiModelProperty(value="接警报警时间")
    private String bjdhsjTime;

    /**
     * 接警单位
     */
    @TableField(value = "jjdw")
    @ApiModelProperty(value="接警单位")
    private String jjdw;

    /**
     * 接警报警人联系电话
     */
    @TableField(value = "lxdh")
    @ApiModelProperty(value="接警报警人联系电话")
    private String lxdh;

    /**
     * 报警内容
     */
    @TableField(value = "bjnr")
    @ApiModelProperty(value="报警内容")
    private String bjnr;

    /**
     * 接警登记单位名称
     */
    @TableField(value = "djdwmc")
    @ApiModelProperty(value="接警登记单位名称")
    private String djdwmc;

    /**
     * 发生地点
     */
    @TableField(value = "sfdd")
    @ApiModelProperty(value="发生地点")
    private String sfdd;

    /**
     * 报警方式
     */
    @TableField(value = "bjxs")
    @ApiModelProperty(value="报警方式")
    private String bjxs;

    /**
     * 接警登记时间
     */
    @TableField(value = "djsj_time")
    @ApiModelProperty(value="接警登记时间")
    private String djsjTime;

    /**
     * 接警登记编号
     */
    @TableField(value = "jjdbh")
    @ApiModelProperty(value="接警登记编号")
    private String jjdbh;

    @TableField(value = "uuid")
    @ApiModelProperty(value="")
    private String uuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "xgsj_time")
    @ApiModelProperty(value="修改时间")
    private String xgsjTime;

    /**
     * 警情等级
     */
    @TableField(value = "bjdhsj")
    @ApiModelProperty(value="警情等级")
    private Date bjdhsj;

    /**
     * 接警报警时间
     */
    @TableField(value = "djsj")
    @ApiModelProperty(value="接警报警时间")
    private Date djsj;

    /**
     * 修改时间
     */
    @TableField(value = "xgsj")
    @ApiModelProperty(value="修改时间")
    private Date xgsj;

    /**
     * 接警日期时间
     */
    @TableField(value = "jjrqsj")
    @ApiModelProperty(value="接警日期时间")
    private String jjrqsj;

    /**
     * 警情等级
     */
    @TableField(value = "jqdj")
    @ApiModelProperty(value="警情等级")
    private String jqdj;

    /**
     * 警情坐标-X
     */
    @TableField(value = "gis_x")
    @ApiModelProperty(value="警情坐标-X")
    private String gisX;

    /**
     * 警情坐标-y
     */
    @TableField(value = "gis_y")
    @ApiModelProperty(value="警情坐标-y")
    private String gisY;
}