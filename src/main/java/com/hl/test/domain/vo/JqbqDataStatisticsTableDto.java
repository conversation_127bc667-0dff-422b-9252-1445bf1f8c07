package com.hl.test.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(description = "警情标签数据统计表")
public class JqbqDataStatisticsTableDto {

    @ExcelIgnore
    @ApiModelProperty("单位Id")
    private String unitId;

    @ApiModelProperty("单位名称")
    @ColumnWidth(14)
    @ExcelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("警情数")
    @ColumnWidth(14)
    @ExcelProperty("警情数")
    private long jqs = 0;

    @ApiModelProperty("标注数")
    @ColumnWidth(14)
    @ExcelProperty("标注数")
    private long bzs = 0;

    @ApiModelProperty("标注率")
    @ColumnWidth(14)
    @ExcelProperty("标注率")
    private String bzl = "";

    @ApiModelProperty("退回警情数")
    @ColumnWidth(14)
    @ExcelProperty("退回警情数")
    private long thjqs = 0;

    @ApiModelProperty("退回率")
    @ColumnWidth(14)
    @ExcelProperty("退回率")
    private String thl = "";

    @ApiModelProperty("退回总次数")
    @ColumnWidth(14)
    @ExcelProperty("退回总次数")
    private long thzcs = 0;

    @ApiModelProperty("待审核数")
    @ColumnWidth(14)
    @ExcelProperty("待审核数")
    private long dshs = 0;

    @ApiModelProperty("审核总次数")
    @ColumnWidth(14)
    @ExcelProperty("审核总次数")
    private long shzcs = 0;
}
