package com.hl.test.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.domain.Dict;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BzbqLazyVo {
    private String id;
    @JsonProperty("dict_name")
    private String dickName;
    private String fatherId;
    private Integer type;
    private Integer isdelete;
    @ApiModelProperty("是否有子节点")
    private Boolean hasChild;
}
