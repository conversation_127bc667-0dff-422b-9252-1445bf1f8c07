package com.hl.test.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class LabelDictDto {
    @ExcelProperty("序号")
    @ColumnWidth(14)
    private Integer number;

    @ExcelProperty("一级标签")
    @ColumnWidth(20)
    private String level1;

    @ExcelProperty("二级标签")
    @ColumnWidth(20)
    private String level2;

    @ExcelProperty("三级标签")
    @ColumnWidth(20)
    private String level3;

    @ExcelProperty("四级标签")
    @ColumnWidth(20)
    private String level4;

    @ExcelProperty("五级标签")
    @ColumnWidth(20)
    private String level5;
}
