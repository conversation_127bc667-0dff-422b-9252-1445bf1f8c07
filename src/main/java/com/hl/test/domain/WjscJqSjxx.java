package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("wjsc_jq_sjxx")
public class WjscJqSjxx {
    private String jjbh;
    private String gmsfhm;
    private String xm;
    private String sjlb;
    @TableField("djsj_time")
    private String djsjTime;
    @TableId("uuid")
    private String uuid;
    @TableField("create_time")
    private String createTime;
    private String djsj;
    @TableField("personM")
    private String personM;
    @TableField("personMs")
    private String personMs;
    private String jgbh;
    private String dwmc;
    private Integer zxbs;
}
