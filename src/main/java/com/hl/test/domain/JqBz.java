package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("jq_bz")
public class JqBz {
    @TableId
    private Integer id;
    private String jjbh;
    private String addressM;
    private String personM;
    private String resultM;
    private String timeM;
    private String toolM;
    private String reasonM;
    private String unit;
    private String bzr;
    @TableField("bz_time")
    private String bzTime;
    private int isdelete;
    @TableField("delete_time")
    private String deleteTime;
    @TableField("delete_user")
    private String deleteUser;
    private int spjg;
    private String spr;
    @TableField("sp_time")
    private String spTime;
    private String spnr;
    @TableField("his_type")
    private int hisType;
    private int bzzt;
    private int mark;





}
