package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "wj_jq_person_label")
public class WjJqPersonLabel {
    @TableId
    private long id;

    private String jjbh;

    private String label;
    @TableField("is_deleted")
    private int isDeleted;

    @TableField("update_time")
    private String updateTime;

    private String gmsfhm;

}
