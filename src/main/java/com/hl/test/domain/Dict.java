package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@TableName("dict")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Dict {
    @TableId(type = IdType.INPUT)
    private String id;
    @TableField("dict_name")
    @JsonProperty("dict_name")
    private String dictName;
    @TableField("father_id")
    private String fatherId;
    private Integer type;
    private String gadm;
    @TableField("index_no")
    private Integer indexNo;
    private String permission;
    private String color;
    private Integer isdelete;
    @TableField("create_user")
    private String createUser;
    @TableField("create_time")
    private String createTime;
    @TableField("delete_time")
    private String deleteTime;
    @TableField("delete_user")
    private String deleteUser;
    private String unit;
    @TableField("is_kh")
    private String isKh;
    private String remark;
    @TableField("static_index")
    private Integer staticIndex;
    @TableField("is_show")
    private String isShow;
    @TableField("label_jz")
    private String labelJz;
    @TableField("label_yw")
    private String labelYw;
    private String memo;
    @TableField("up_time")
    private String upTime;

    @TableField(exist =false)
    @JsonProperty("dets")
    private List<Dict> child;

    @TableField(exist = false)
    private boolean isLeaf = false;
}
