package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespSYRK {
    @ApiModelProperty("身份证")
    @JsonProperty("gmsfhm")
    String gmsfhm = "";
    @ApiModelProperty("姓名")
    @JsonProperty("xm")
    String xm = "";
    @ApiModelProperty("涉警类别")
    @JsonProperty("sjlb")
    String sjlb = "";
    @ApiModelProperty("登记时间格式")
    @JsonProperty("djsj_time")
    String djsj_time = "";
    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    String create_time = "";
}
