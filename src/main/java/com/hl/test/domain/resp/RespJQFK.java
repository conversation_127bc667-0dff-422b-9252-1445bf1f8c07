package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespJQFK {


    @ApiModelProperty("反馈内容")
    @JsonProperty("content")
    String content = null;

    @ApiModelProperty("反馈人")
    @JsonProperty("_create_user")
    JSONObject _create_user = null;

    @ApiModelProperty("反馈单位")
    @JsonProperty("_unit")
    JSONObject _unit = null;


    @ApiModelProperty("反馈时间")
    @JsonProperty("create_time")
    String create_time = null;


    @ApiModelProperty("id")
    @JsonProperty("id")
    String id = null;


}
