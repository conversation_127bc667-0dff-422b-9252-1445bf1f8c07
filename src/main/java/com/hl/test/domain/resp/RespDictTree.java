package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RespDictTree {

    @ApiModelProperty("编号")
    @JsonProperty("id")
    String id = "";
    @ApiModelProperty("名称")
    @JsonProperty("dict_name")
    String dict_name = "";
    @ApiModelProperty("父id")
    @JsonProperty("father_id")
    String father_id = "";


    @ApiModelProperty("类型")
    @JsonProperty("type")
    int type =-1;
    @ApiModelProperty("排序")
    @JsonProperty("index_no")
    int index_no =-1;
    @ApiModelProperty("权限")
    @JsonProperty("permission")
    String permission = "";
    @ApiModelProperty("颜色")
    @JsonProperty("color")
    String color = "";
    @ApiModelProperty("是否删除 1否2是")
    @JsonProperty("isdelete")
    int isdelete =-1;
    @ApiModelProperty("创建人")
    @JsonProperty("create_user")
    String create_user = "";
    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    String create_time = "";
    @ApiModelProperty("删除时间")
    @JsonProperty("delete_time")
    String delete_time = "";
    @ApiModelProperty("删除人")
    @JsonProperty("delete_user")
    String delete_user = "";
    @ApiModelProperty("所属单位")
    @JsonProperty("unit")
    String unit = "";
    @ApiModelProperty("备注")
    @JsonProperty("remark")
    String remark = "";
    @ApiModelProperty("等级")
    @JsonProperty("static_index")
    int static_index =-1;


    @ApiModelProperty("下一层级")
    @JsonProperty("dets")
    List<RespDictTree> dets =new ArrayList<>();

    public void setId(String id) {
        this.id = id;
    }

    public void setDict_name(String dict_name) {
        this.dict_name = dict_name;
    }

    public void setFather_id(String father_id) {
        this.father_id = father_id;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setIndex_no(int index_no) {
        this.index_no = index_no;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public void setIsdelete(int isdelete) {
        this.isdelete = isdelete;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public void setDelete_time(String delete_time) {
        this.delete_time = delete_time;
    }

    public void setDelete_user(String delete_user) {
        this.delete_user = delete_user;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setStatic_index(int static_index) {
        this.static_index = static_index;
    }

    public String getId() {
        return id;
    }

    public String getDict_name() {
        return dict_name;
    }

    public String getFather_id() {
        return father_id;
    }

    public int getType() {
        return type;
    }

    public int getIndex_no() {
        return index_no;
    }

    public String getPermission() {
        return permission;
    }

    public String getColor() {
        return color;
    }

    public int getIsdelete() {
        return isdelete;
    }

    public String getCreate_user() {
        return create_user;
    }

    public String getCreate_time() {
        return create_time;
    }

    public String getDelete_time() {
        return delete_time;
    }

    public String getDelete_user() {
        return delete_user;
    }

    public String getUnit() {
        return unit;
    }

    public String getRemark() {
        return remark;
    }

    public int getStatic_index() {
        return static_index;
    }
}
