package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespJQgz {


    @ApiModelProperty("id")
    @JsonProperty("id")
    int id = -1;
    @ApiModelProperty("关注原因")
    @JsonProperty("reason")
    String reason = null;
    @ApiModelProperty("创建人")
    @JsonProperty("_create_user")
    JSONObject _create_user = new JSONObject();

    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    String create_time = null;

    @ApiModelProperty("jjbh")
    @JsonProperty("jjbh")
    String jjbh = null;
    @ApiModelProperty("单位")
    @JsonProperty("_unit")
    JSONObject _unit = new JSONObject();

}
