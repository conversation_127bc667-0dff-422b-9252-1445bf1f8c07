package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespSjryJqList {


    @ApiModelProperty("涉警类别 dict:55")
    @JsonProperty("_sjlb")
    JSONObject _sjlb = new JSONObject();
    @ApiModelProperty("涉警类别 dict:55")
    @JsonProperty("sjlb")
    String sjlb = null;
    @ApiModelProperty("接警时间")
    @JsonProperty("bjdhsj_time")
    String bjdhsj_time = null;
    @ApiModelProperty("报警类型 dict:500")
    @JsonProperty("_bjlx")
    JSONObject _bjlx = new JSONObject();
    @ApiModelProperty("报警类型 dict:500")
    @JsonProperty("bjlx")
    String bjlx = null;
    @ApiModelProperty("处警类别 dict:510")
    @JsonProperty("_cjlb")
    JSONObject _cjlb = new JSONObject();
    @ApiModelProperty("处警类别 dict:510")
    @JsonProperty("cjlb")
    String cjlb = null;
    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("_cjjg")
    JSONObject _cjjg = new JSONObject();
    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    String cjjg = null;
    @ApiModelProperty("报警人")
    @JsonProperty("bjr")
    String bjr = null;
    @ApiModelProperty("报警电话")
    @JsonProperty("lxdh")
    String lxdh = null;
    @ApiModelProperty("处警单位")
    @JsonProperty("cjdwmc")
    String cjdwmc = null;

    @ApiModelProperty("jjbh")
    @JsonProperty("jjbh")
    String jjbh = null;

    @ApiModelProperty("处警内容")
    @JsonProperty("cljgnr")
    String cljgnr = null;



}
