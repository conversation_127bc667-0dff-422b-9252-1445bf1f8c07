package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RespJQBZ {

    @ApiModelProperty("id")
    @JsonProperty("id")
    String id = "";
    @ApiModelProperty("警情编号")
    @JsonProperty("jjbh")
    String jjbh = null;
    @ApiModelProperty("地址标签")
    @JsonProperty("_addressM")
    JSONArray _addressM = new JSONArray();

    @ApiModelProperty("结果标签")
    @JsonProperty("_resultM")
    JSONArray _resultM = new JSONArray();

    @ApiModelProperty("时间标签")
    @JsonProperty("_timeM")
    JSONObject _timeM = new JSONObject();
    @ApiModelProperty("手段标签")
    @JsonProperty("_toolM")
    JSONArray _toolM = new JSONArray();
    @ApiModelProperty("原因标签")
    @JsonProperty("_reasonM")
    JSONArray _reasonM = new JSONArray();

    @ApiModelProperty("标注单位")
    @JsonProperty("_unit")
    JSONObject _unit = new JSONObject();

    @ApiModelProperty("标注人")
    @JsonProperty("_create_user")
    JSONObject _create_user = new JSONObject();

    @ApiModelProperty("标注时间")
    @JsonProperty("create_time")
    String create_time = "";

    @ApiModelProperty("是否标注")
    @JsonProperty("mark")
    int mark = 0;

    @ApiModelProperty("标注状态")
    @JsonProperty("bzzt")
    int bzzt = 0;

    @ApiModelProperty("审批结果:-1未标注 0已标注待审批 1同意 2退回")
    @JsonProperty("spjg")
    int spjg = -1;
    @ApiModelProperty("审批人")
    @JsonProperty("_spr")
    JSONObject _spr = new JSONObject();
    @ApiModelProperty("审批时间")
    @JsonProperty("sp_time")
    String sp_time = "";
    @ApiModelProperty("审批内容")
    @JsonProperty("spnr")
    String spnr = "";

    @ApiModelProperty("审批人单位")
    @JsonProperty("_spr_unit")
    JSONObject _spr_unit = new JSONObject();


    @ApiModelProperty("@地址标签")
    @JsonProperty("addressM")
    List<String> addressM = new ArrayList<>();


    @ApiModelProperty("@地址标签")
    @JsonProperty("addressMNew")
    List<String> addressMNew = new ArrayList<>();

    @ApiModelProperty("@结果标签")
    @JsonProperty("resultM")
    List<String> resultM = new ArrayList<>();

    @ApiModelProperty("@时间标签")
    @JsonProperty("timeM")
    String timeM = "";
    @ApiModelProperty("@手段标签")
    @JsonProperty("toolM")
    List<String> toolM = new ArrayList<>();
    @ApiModelProperty("@原因标签")
    @JsonProperty("reasonM")
    List<String> reasonM = new ArrayList<>();



    @ApiModelProperty("处警类别全")
    @JsonProperty("cjlbs")
    String cjlbs = "";

    @ApiModelProperty("发生地点")
    @JsonProperty("fsdd")
    String fsdd = "";

    @ApiModelProperty("百度AI推荐")
    @JsonProperty("baidu")
    JSONObject baidu = new JSONObject();


    @ApiModelProperty("标准地址")
    @JsonProperty("dwdz")
    String dwdz = "";

    @ApiModelProperty("地址名称")
    @JsonProperty("dwmc")
    String dwmc = "";

    @ApiModelProperty("地址类型 1单位2房屋 其他数字都归99")
    @JsonProperty("dz_type")
    String dz_type = "";

    @ApiModelProperty("地址id")
    @JsonProperty("dzid")
    String dzid = "";

    @ApiModelProperty("事件标签id")
    String eventId = "";

    String sszrq = "";

    String jgbh = "";

}
