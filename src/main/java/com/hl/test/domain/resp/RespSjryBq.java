package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespSjryBq {

    @ApiModelProperty("人员标签")
    @JsonProperty("_personM")
    JSONArray _personM = new JSONArray();

    @ApiModelProperty("地址标签")
    @JsonProperty("_addressM")
    JSONArray _addressM = new JSONArray();

    @ApiModelProperty("结果标签")
    @JsonProperty("_resultM")
    JSONArray _resultM = new JSONArray();

    @ApiModelProperty("时间标签")
    @JsonProperty("_timeM")
    JSONArray _timeM = new JSONArray();
    @ApiModelProperty("手段标签")
    @JsonProperty("_toolM")
    JSONArray _toolM = new JSONArray();
    @ApiModelProperty("原因标签")
    @JsonProperty("_reasonM")
    JSONArray _reasonM = new JSONArray();


    @ApiModelProperty("涉警情况-处警类别")
    @JsonProperty("cjlbs")
    JSONArray cjlbs = new JSONArray();


    @ApiModelProperty("涉警情况-单位")
    @JsonProperty("cjdws")
    JSONArray cjdws = new JSONArray();

    @ApiModelProperty("近一周涉警数")
    @JsonProperty("week")
    JSONObject week = new JSONObject();
    @ApiModelProperty("近一月涉警数")
    @JsonProperty("month")
    JSONObject month = new JSONObject();
    @ApiModelProperty("近3个月涉警数")
    @JsonProperty("month3")
    JSONObject month3 = new JSONObject();
    @ApiModelProperty("近半年涉警数")
    @JsonProperty("month6")
    JSONObject month6 = new JSONObject();
    @ApiModelProperty("近一年涉警数")
    @JsonProperty("year")
    JSONObject year = new JSONObject();
    @ApiModelProperty("近2年涉警数")
    @JsonProperty("year2")
    JSONObject year2 = new JSONObject();
    @ApiModelProperty("历史涉警数")
    @JsonProperty("total")
    JSONObject total = new JSONObject();
    @ApiModelProperty("同警人数")
    @JsonProperty("tjrs")
    String tjrs = "";
    @ApiModelProperty("涉及分局")
    @JsonProperty("sjfj")
    JSONArray sjfj = new JSONArray();
    @ApiModelProperty("涉警人员类别")
    @JsonProperty("rylbs")
    JSONArray rylbs = new JSONArray();
    @ApiModelProperty("报警类型")
    @JsonProperty("bjlxs")
    JSONArray bjlxs = new JSONArray();
    @ApiModelProperty("处警结果")
    @JsonProperty("cjjgs")
    JSONArray cjjgs = new JSONArray();
}
