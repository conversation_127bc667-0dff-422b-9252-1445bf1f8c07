package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RespJQList {


    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    String jjbh = null;

    @ApiModelProperty("接警时间")
    @JsonProperty("djsj_time")
    String djsj_time = null;
    @ApiModelProperty("接警单位")
    @JsonProperty("jjdwmc")
    String jjdwmc = null;
    @ApiModelProperty("报警类型")
    @JsonProperty("bjlx")
    String bjlx = null;
    @ApiModelProperty("处警类别")
    @JsonProperty("cjlb")
    String cjlb = null;
    @ApiModelProperty("处警结果")
    @JsonProperty("cjjg")
    String cjjg = null;
    @ApiModelProperty("分局警情标签")
    @JsonProperty("fjbq")
    String fjbq = null;
    @ApiModelProperty("事发场所")
    @JsonProperty("sfcs")
    String sfcs = null;
    @ApiModelProperty("涉警人员")
    @JsonProperty("sjry")
    JSONArray sjry = null;

    @ApiModelProperty("省厅标签dict:75")
    @JsonProperty("cjjqbq")
    String cjjqbq = null;

    @ApiModelProperty("处警内容")
    @JsonProperty("cljgnr")
    String cljgnr = null;


    public String getJjbh() {
        return jjbh;
    }

    public void setJjbh(String jjbh) {
        this.jjbh = jjbh;
    }

    public String getDjsj_time() {
        return djsj_time;
    }

    public void setDjsj_time(String djsj_time) {
        this.djsj_time = djsj_time;
    }

    public String getJjdwmc() {
        return jjdwmc;
    }

    public void setJjdwmc(String jjdwmc) {
        this.jjdwmc = jjdwmc;
    }

    public String getBjlx() {
        return bjlx;
    }

    public void setBjlx(String bjlx) {
        this.bjlx = bjlx;
    }



    public String getCjjg() {
        return cjjg;
    }

    public void setCjjg(String cjjg) {
        this.cjjg = cjjg;
    }

    public String getFjbq() {
        return fjbq;
    }

    public void setFjbq(String fjbq) {
        this.fjbq = fjbq;
    }

    public String getSfcs() {
        return sfcs;
    }

    public void setSfcs(String sfcs) {
        this.sfcs = sfcs;
    }

    public JSONArray getSjry() {
        return sjry;
    }

    public void setSjry(JSONArray sjry) {
        this.sjry = sjry;
    }
}
