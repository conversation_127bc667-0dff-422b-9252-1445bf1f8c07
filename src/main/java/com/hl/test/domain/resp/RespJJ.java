package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespJJ {
    @ApiModelProperty("接警报警时间")
    @JsonProperty("bjdhsj_time")
    String bjdhsj_time = "";
    @ApiModelProperty("报警类型 dict:50/51")
    @JsonProperty("bjlx")
    String bjlx = "";
    @ApiModelProperty("报警内容")
    @JsonProperty("bjnr")
    String bjnr = "";
    @ApiModelProperty("接警报警人")
    @JsonProperty("bjr")
    String bjr = "";
    @ApiModelProperty("报警方式 dict:45")
    @JsonProperty("bjxs")
    String bjxs = "";
    @ApiModelProperty("处警标识 dict:??")
    @JsonProperty("cjbs")
    String cjbs = "";
    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    String create_time = "";
    @ApiModelProperty("接警登记单位名称")
    @JsonProperty("djdwmc")
    String djdwmc = "";
    @ApiModelProperty("接警登记时间")
    @JsonProperty("djsj_time")
    String djsj_time = "";
    @ApiModelProperty("警情坐标-X")
    @JsonProperty("gis_x")
    String gis_x = "";
    @ApiModelProperty("警情坐标-y")
    @JsonProperty("gis_y")
    String gis_y = "";
    @ApiModelProperty("接警编号")
    @JsonProperty("jjbh")
    String jjbh = "";
    @ApiModelProperty("接警登记编号")
    @JsonProperty("jjdbh")
    String jjdbh = "";
    @ApiModelProperty("接警单位")
    @JsonProperty("jjdw")
    String jjdw = "";
    @ApiModelProperty("接警单位名称")
    @JsonProperty("jjdwmc")
    String jjdwmc = "";
    @ApiModelProperty("接警日期时间")
    @JsonProperty("jjrqsj")
    String jjrqsj = "";
    @ApiModelProperty("警情等级 dict:??")
    @JsonProperty("jqdj")
    String jqdj = "";
    @ApiModelProperty("接警报警人联系电话")
    @JsonProperty("lxdh")
    String lxdh = "";
    @ApiModelProperty("发生地点")
    @JsonProperty("sfdd")
    String sfdd = "";
    @ApiModelProperty("修改时间")
    @JsonProperty("xgsj_time")
    String xgsj_time = "";

}
