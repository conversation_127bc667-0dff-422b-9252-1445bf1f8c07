package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespJQBZList {

    @ApiModelProperty("接警编号")
    @JsonProperty("jjbh")
    String jjbh = "";
    @ApiModelProperty("接警时间")
    @JsonProperty("bjdhsj_time")
    String bjdhsj_time = "";
    @ApiModelProperty("接警单位")
    @JsonProperty("jjdwmc")
    String jjdwmc = "";
    @ApiModelProperty("报警类型 dict:51")
    @JsonProperty("bjlx")
    String bjlx = "";
    @ApiModelProperty("处警类别 dict：51")
    @JsonProperty("cjlb")
    String cjlb = "";
    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    String cjjg = "";
    @ApiModelProperty("处理结果内容")
    @JsonProperty("cljgnr")
    String cljgnr = "";
    @ApiModelProperty("审批结果0未审批1审批通过2审批不通过")
    @JsonProperty("spjg")
    String spjg = "";
    @ApiModelProperty("事发场所dict:74")
    @JsonProperty("sfcs")
    String sfcs = "";

    @ApiModelProperty("是否标注1是0否")
    @JsonProperty("mark")
    int mark = 0;

    @ApiModelProperty("是否关注1是0否")
    @JsonProperty("is_gz")
    int is_gz = 0;

    @ApiModelProperty("警情标注标签")
    @JsonProperty("jqbzs")
    JSONArray jqbzs = new JSONArray();

    @ApiModelProperty("地址标签")
    @JsonProperty("_addressM")
    JSONArray _addressM = new JSONArray();

    @ApiModelProperty("结果标签")
    @JsonProperty("_resultM")
    JSONArray _resultM = new JSONArray();

    @ApiModelProperty("时间标签")
    @JsonProperty("_timeM")
    JSONObject _timeM = new JSONObject();
    @ApiModelProperty("手段标签")
    @JsonProperty("_toolM")
    JSONArray _toolM = new JSONArray();
    @ApiModelProperty("原因标签")
    @JsonProperty("_reasonM")
    JSONArray _reasonM = new JSONArray();

    @ApiModelProperty("人员标签")
    @JsonProperty("_personM")
    JSONArray _personM = new JSONArray();

    @ApiModelProperty("社区")
    String sszrq = "";

    @ApiModelProperty("事件名称")
    String eventName = "";

}
