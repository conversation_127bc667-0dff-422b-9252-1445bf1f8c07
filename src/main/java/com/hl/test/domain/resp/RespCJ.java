package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespCJ {
    @ApiModelProperty("补充处理结果")
    @JsonProperty("bccljg")
    String bccljg = "";
    @ApiModelProperty("地址补充")
    @JsonProperty("bzdzmc")
    String bzdzmc = "";
    @ApiModelProperty("处警编号")
    @JsonProperty("cjbh")
    String cjbh = "";
    @ApiModelProperty("处警处警单位名称")
    @JsonProperty("cjdwmc")
    String cjdwmc = "";
    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    String cjjg = "";
    @ApiModelProperty("警情标签 dict:75")
    @JsonProperty("cjjqbq")
    String cjjqbq = "";
    @ApiModelProperty("处警类别 dict：51")
    @JsonProperty("cjlb")
    String cjlb = "";
    @ApiModelProperty("处警人")
    @JsonProperty("cjr")
    String cjr = "";
    @ApiModelProperty("处警处警时间")
    @JsonProperty("cjsj_time")
    String cjsj_time = "";
    @ApiModelProperty("处警信息地点")
    @JsonProperty("cjxxdd")
    String cjxxdd = "";
    @ApiModelProperty("处警详址")
    @JsonProperty("cjxz")
    String cjxz = "";
    @ApiModelProperty("处理结果内容")
    @JsonProperty("cljgnr")
    String cljgnr = "";
    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    String create_time = "";
    @ApiModelProperty("登记人")
    @JsonProperty("djr")
    String djr = "";
    @ApiModelProperty("登记时间")
    @JsonProperty("djsj_time")
    String djsj_time = "";
    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    String jjbh = "";
    @ApiModelProperty("警情属性（XXX）")
    @JsonProperty("jqsx")
    String jqsx = "";
    @ApiModelProperty("区域类别（XXX）")
    @JsonProperty("qylb")
    String qylb = "";
    @ApiModelProperty("事发场所dict:74")
    @JsonProperty("sfcs")
    String sfcs = "";
    @ApiModelProperty("事发时间上限")
    @JsonProperty("sfsjsx_time")
    String sfsjsx_time = "";
    @ApiModelProperty("事发时间下限")
    @JsonProperty("sfsjxx_time")
    String sfsjxx_time = "";
    @ApiModelProperty("事发星期")
    @JsonProperty("sfxq")
    String sfxq = "";
    @ApiModelProperty("所属辖区(xxx)")
    @JsonProperty("ssxq")
    String ssxq = "";
    @ApiModelProperty("损失详细情况")
    @JsonProperty("ssxxqk")
    String ssxxqk = "";
    @ApiModelProperty("天气情况")
    @JsonProperty("tqqk")
    String tqqk = "";
    @ApiModelProperty("修改时间")
    @JsonProperty("xgsj_time")
    String xgsj_time = "";
    @ApiModelProperty("警情坐标-X")
    @JsonProperty("xzb")
    String xzb = "";
    @ApiModelProperty("警情坐标-Y'")
    @JsonProperty("yzb")
    String yzb = "";

    @ApiModelProperty("省厅标签 dict:??'")
    @JsonProperty("stbq")
    String stbq = "";

    @ApiModelProperty("分局标签 dict:??'")
    @JsonProperty("fjbq")
    String fjbq = "";

    @ApiModelProperty("处警类别全")
    @JsonProperty("cjlbs")
    String cjlbs = "";

    @ApiModelProperty("发生原因")
    @JsonProperty("fsyy")
    String fsyy = "";
}
