package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RespJqDet {
    @ApiModelProperty("报警信息")
    @JsonProperty("jjxx")
    RespJJ jjxx = null;

    @ApiModelProperty("处警信息")
    @JsonProperty("cjxx")
    RespCJ cjxx = null;

    @ApiModelProperty("涉警人员信息列表")
    @JsonProperty("sjrys")
    List<RespSJRYList> sjrys = null;

    @ApiModelProperty("案件信息")
    @JsonProperty("ajxx")
    RespAJ ajxx = null;
}
