package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespSJRY {
    @ApiModelProperty("姓名")
    @JsonProperty("xm")
    String xm = "";
    @ApiModelProperty("现住地址")
    @JsonProperty("sjjzd_dzmc")
    String sjjzd_dzmc = "";
    @ApiModelProperty("婚姻状况")
    @JsonProperty("hyzk")
    String hyzk = "";
    @ApiModelProperty("性别 1:女2男")
    @JsonProperty("xbdm")
    String xbdm = "";
    @ApiModelProperty("身份证号")
    @JsonProperty("gmsfhm")
    String gmsfhm = "";
    @ApiModelProperty("身高")
    @JsonProperty("sg")
    String sg = "";
    @ApiModelProperty("人员类别 11户籍12流口 13寄住 14人户一致")
    @JsonProperty("rkgllbdm")
    String rkgllbdm = "";
    @ApiModelProperty("国籍")
    @JsonProperty("gj")
    String gj = "";
    @ApiModelProperty("曾用名")
    @JsonProperty("cym")
    String cym = "";
    @ApiModelProperty("民族dict:126")
    @JsonProperty("mzdm")
    String mzdm = "";
    @ApiModelProperty("联系电话")
    @JsonProperty("lxdh")
    String lxdh = "";
    @ApiModelProperty("户号")
    @JsonProperty("hh")
    String hh = "";
    @ApiModelProperty("出生日期")
    @JsonProperty("csrq")
    String csrq = "";
    @ApiModelProperty("户籍地址")
    @JsonProperty("hjdz_dzmc")
    String hjdz_dzmc = "";
    @ApiModelProperty("入库时间")
    @JsonProperty("rksj")
    String rksj = "";
    @ApiModelProperty("文化程度")
    @JsonProperty("whcd")
    String whcd = "";
    @ApiModelProperty("照片")
    @JsonProperty("zplj")
    String zplj = "";




}
