package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespJQStaDet {
    @ApiModelProperty("标签名称")
    @JsonProperty("dict_name")
    String dict_name = null;

    @ApiModelProperty("标签名称全")
    @JsonProperty("memo")
    String memo = null;


    @ApiModelProperty("标签id")
    @JsonProperty("label")
    String label = null;

    @ApiModelProperty("标签个数")
    @JsonProperty("count")
    int count = 0;

    @ApiModelProperty("所属单位")
    @JsonProperty("unit")
    String unit = null;
}
