package com.hl.test.domain.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RespAJ {

    @ApiModelProperty("案件编号")
    @JsonProperty("case_no")
    String case_no = null;
    @ApiModelProperty("案件名称")
    @JsonProperty("case_name")
    String case_name = null;
    @ApiModelProperty("简要案情")
    @JsonProperty("case_summary")
    String case_summary = null;
    @ApiModelProperty("受理单位名称")
    @JsonProperty("case_accept_dep_name")
    String case_accept_dep_name = null;
    @ApiModelProperty("受理时间")
    @JsonProperty("case_accept_time")
    String case_accept_time = null;
    @ApiModelProperty("案件类别")
    @JsonProperty("case_type")
    String case_type = null;
    @ApiModelProperty("案件副案别")
    @JsonProperty("case_sub_type")
    String case_sub_type = null;
    @ApiModelProperty("案件状态")
    @JsonProperty("case_state")
    String case_state = null;
    @ApiModelProperty("涉案总价值")
    @JsonProperty("value_involved")
    String value_involved = null;


}
