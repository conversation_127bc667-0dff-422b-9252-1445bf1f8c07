package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RespSJRYList {
    @ApiModelProperty("涉警类别 dict:55")
    @JsonProperty("sjlb")
    String sjlb = null;
    @ApiModelProperty("姓名")
    @JsonProperty("xm")
    String xm = null;
    @ApiModelProperty("身份证")
    @JsonProperty("gmsfhm")
    String gmsfhm = null;
    @ApiModelProperty("接警编号")
    @JsonProperty("jjbh")
    String jjbh = null;
    @ApiModelProperty("接警报警时间开始")
    @JsonProperty("bjdhsj_time_start")
    String bjdhsj_time_start = null;

    @ApiModelProperty("报警类型 dict:500")
    @JsonProperty("bjlx")
    String bjlx = null;
    @ApiModelProperty("处警类别 dict：510")
    @JsonProperty("cjlb")
    String cjlb = null;
    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    String cjjg = null;

    @ApiModelProperty("涉警登记时间")
    @JsonProperty("djsj_time")
    String djsj_time = null;

    @ApiModelProperty("@人员标签")
    @JsonProperty("personM")
    List<String> personM = new ArrayList<>();

    @ApiModelProperty("@人员标签新")
    @JsonProperty("personMNew")
    List<String> personMNew = new ArrayList<>();

    @ApiModelProperty("@人员标签-该人最近一次打过的人员标签")
    @JsonProperty("personMLastTime")
    List<String> personMLastTime = new ArrayList<>();

    @ApiModelProperty("人员标签")
    @JsonProperty("_personM")
    JSONArray _personM = new JSONArray();

    @ApiModelProperty("工作单位编号")
    @JsonProperty("jgbh")
    String jgbh = "";

    @ApiModelProperty("工作单位名称")
    @JsonProperty("dwmc")
    String dwmc = "";

    @ApiModelProperty("uuid")
    @JsonProperty("uuid")
    String uuid = "";

    @ApiModelProperty("是否未成年")
    String sfwcn = "0";

    @ApiModelProperty("年龄范围id")
    String nlfwId = "";
}
