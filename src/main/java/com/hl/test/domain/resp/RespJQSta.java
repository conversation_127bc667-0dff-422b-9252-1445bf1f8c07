package com.hl.test.domain.resp;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RespJQSta {
    @ApiModelProperty("标签名称")
    @JsonProperty("name")
    String name = null;


    @ApiModelProperty("标签统计明细")
    @JsonProperty("dets")
    List<RespJQStaDet> dets = new ArrayList<>();

    @ApiModelProperty("标签类型")
    @JsonProperty("type")
    int type = 0;

    @ApiModelProperty("标签子集")
    @JsonProperty("tabs")
    JSONArray tabs = new JSONArray();
}
