package com.hl.test.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgTestVo {

    private String parent_id;
    private String organization_id;
    private String organization_nick;
    private String organization_index;
    private List<OrgTestVo> sub_organization;

    public OrgTestVo(String parent_id, String organization_id, String organization_nick, String organization_index) {
        this.parent_id = parent_id;
        this.organization_id = organization_id;
        this.organization_nick = organization_nick;
        this.organization_index = organization_index;
    }
}
