package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqSYDW {

    @ApiModelProperty("机构编号")
    @JsonProperty("JGBH")
    @SqlInjection
    String JGBH = null;


    @ApiModelProperty("单位名称")
    @JsonProperty("DWMC")
    @SqlInjection
    String DWMC = null;

    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = 1;

    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;

    @ApiModelProperty("类型1单位2房屋99其他98字典")
    @JsonProperty("type")
    int type = 1;

}
