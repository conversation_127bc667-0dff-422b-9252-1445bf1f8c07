package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.DateInjection;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqDictList {

    @ApiModelProperty("编号")
    @JsonProperty("id")
    @SqlInjection
    String id = null;

    @ApiModelProperty("名称")
    @JsonProperty("dict_name")
    @SqlInjection
    String dict_name = null;

    @ApiModelProperty("父id")
    @JsonProperty("father_id")
    @SqlInjection
    String father_id = null;

    @ApiModelProperty("类型")
    @JsonProperty("type")
    int type = -1;


    @ApiModelProperty("是否删除 1否2是")
    @JsonProperty("isdelete")
    int isdelete = 1;

    @ApiModelProperty("创建人")
    @JsonProperty("create_user")
    @SqlInjection
    String create_user = null;

    @ApiModelProperty("创建时间_开始")
    @JsonProperty("create_time_start")
    @DateInjection
    String create_time_start = null;

    @ApiModelProperty("创建时间_结束")
    @JsonProperty("create_time_end")
    @DateInjection
    String create_time_end = null;


    @ApiModelProperty("所属单位")
    @JsonProperty("unit")
    @SqlInjection
    String unit = null;

    @ApiModelProperty("所属单位")
    @JsonProperty("gadm")
    @SqlInjection
    String gadm = null;


    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = 1;

    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;

    public int getPage() {
        return page;
    }

    public int getLimit() {
        return limit;
    }

    public String getId() {
        return id;
    }

    public String getDict_name() {
        return dict_name;
    }

    public String getFather_id() {
        return father_id;
    }

    public int getType() {
        return type;
    }


    public int getIsdelete() {
        return isdelete;
    }

    public String getCreate_user() {
        return create_user;
    }

    public String getCreate_time_start() {
        return create_time_start;
    }

    public String getCreate_time_end() {
        return create_time_end;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setDict_name(String dict_name) {
        this.dict_name = dict_name;
    }

    public void setFather_id(String father_id) {
        this.father_id = father_id;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getUnit() {
        return unit;
    }

    public void setIsdelete(int isdelete) {
        this.isdelete = isdelete;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public void setCreate_time_start(String create_time_start) {
        this.create_time_start = create_time_start;
    }

    public void setCreate_time_end(String create_time_end) {
        this.create_time_end = create_time_end;
    }


    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }
}
