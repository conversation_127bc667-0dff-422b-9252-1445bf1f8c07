package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqJQgzCreate {


    @ApiModelProperty("警情编号")
    @JsonProperty("jjbh")
    @SqlInjection
    String jjbh = "";

    @ApiModelProperty("关注原因")
    @JsonProperty("reason")
    @SqlInjection
    String reason = null;

}
