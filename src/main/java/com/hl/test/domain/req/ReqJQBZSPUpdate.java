package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqJQBZSPUpdate {

    @ApiModelProperty("jjbh")
    @JsonProperty("jjbh")
    String jjbh = "";

    @ApiModelProperty("审批结果:-1未标注 0已标注待审批 1同意 2退回")
    @JsonProperty("spjg")
    int spjg = -1;

    @ApiModelProperty("审批内容")
    @JsonProperty("spnr")
    String spnr = null;


}
