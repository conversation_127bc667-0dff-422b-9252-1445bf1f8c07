package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.DateInjection;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqJQStaQuery {

    @ApiModelProperty("接警单位")
    @JsonProperty("jjdw")
    @SqlInjection
    List<String> jjdw = null;

    @ApiModelProperty("接警时间开始")
    @JsonProperty("bjdhsj_time_start")
    @DateInjection
    String bjdhsj_time_start = null;

    @ApiModelProperty("接警时间结束")
    @JsonProperty("bjdhsj_time_end")
    @DateInjection
    String bjdhsj_time_end = null;

    @ApiModelProperty("标注时间开始")
    @JsonProperty("bz_time_start")
    @DateInjection
    String bz_time_start = null;

    @ApiModelProperty("标注时间结束")
    @JsonProperty("bz_time_end")
    @DateInjection
    String bz_time_end = null;
    @ApiModelProperty("处警类别")
    @JsonProperty("cjlb")
    @SqlInjection
    List<String> cjlb = null;

    @ApiModelProperty("警情标签")
    @JsonProperty("jqbz")
    @SqlInjection
    List<String> jqbz = null;

    @JsonProperty("bzbq")
    @SqlInjection
    List<String> bzbq = null;

    @ApiModelProperty("统计层级")
    @JsonProperty("level")
    int level = 0;

    @ApiModelProperty("标签类型")
    @JsonProperty("type")
    int type = 0;

    @ApiModelProperty("标签id")
    @JsonProperty("label")
    @SqlInjection
    String label=null;


    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;


    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = -1;

    @ApiModelProperty("标签公式")
    @JsonProperty("bzgs")
    @SqlInjection
    List<String> bzgs = null;

    @ApiModelProperty("tab选择的id")
    @JsonProperty("tabId")
    @SqlInjection
    String tabId=null;

    @JsonProperty("jjbh_list")
    @SqlInjection
    private List<String> jjbh_list;

}
