package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.DateInjection;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqJQBZPersonHis {


    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    String jjbh = "";


    @ApiModelProperty("涉警人员身份证")
    @JsonProperty("gmsfhm")
    String gmsfhm = "";

    @ApiModelProperty("人员标签")
    @JsonProperty("personM")
    List<String> personM = null;

    @ApiModelProperty("标注人")
    @JsonProperty("opt_user")
    String opt_user = null;

    @ApiModelProperty("标注时间")
    @JsonProperty("create_time")
    String create_time = null;
}
