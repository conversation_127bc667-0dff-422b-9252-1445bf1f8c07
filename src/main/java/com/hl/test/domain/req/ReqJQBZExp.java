package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ReqJQBZExp {


    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    String jjbh = null;

    @ApiModelProperty("处警类别 dict:51")
    @JsonProperty("cjlb")
    List<String> cjlb = null;

    @ApiModelProperty("事发场所 dict:74")
    @JsonProperty("sfcs")
    String sfcs = null;

    @ApiModelProperty("处理结果内容")
    @JsonProperty("cljgnr")
    String cljgnr = null;

    @ApiModelProperty("处警详址")
    @JsonProperty("cjxz")
    String cjxz = null;

    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    String cjjg = null;

    @ApiModelProperty("事发时间下限_开始")
    @JsonProperty("sfsjxx_start")
    String sfsjxx_start = null;

    @ApiModelProperty("事发时间下限_结束")
    @JsonProperty("sfsjxx_end")
    String sfsjxx_end = null;

    @ApiModelProperty("事发时间下限")
    @JsonProperty("sfsjxx")
    String sfsjxx = null;

    @ApiModelProperty("事发时间上限_开始")
    @JsonProperty("sfsjsx_start")
    String sfsjsx_start = null;

    @ApiModelProperty("事发时间上限_结束")
    @JsonProperty("sfsjsx_end")
    String sfsjsx_end = null;

    @ApiModelProperty("事发时间上限")
    @JsonProperty("sfsjsx")
    String sfsjsx = null;

    @ApiModelProperty("处警登记时间_开始")
    @JsonProperty("djsj_start")
    String djsj_start = null;

    @ApiModelProperty("处警登记时间_结束")
    @JsonProperty("djsj_end")
    String djsj_end = null;

    @ApiModelProperty("处警登记时间")
    @JsonProperty("djsj")
    String djsj = null;

    @ApiModelProperty("处警修改时间_开始")
    @JsonProperty("xgsj_start")
    String cj_xgsj_start = null;

    @ApiModelProperty("处警修改时间_结束")
    @JsonProperty("xgsj_end")
    String cj_xgsj_end = null;


    @ApiModelProperty("处警标识 dict:??")
    @JsonProperty("cjbs")
    String cjbs = null;

    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = 1;

    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;

    @ApiModelProperty("涉警人员身份证")
    @JsonProperty("gmsfhm")
    String gmsfhm = null;

    @ApiModelProperty("涉警人员姓名")
    @JsonProperty("xm")
    String xm = null;

    @ApiModelProperty("涉警类别 dict:55")
    @JsonProperty("sjlb")
    String sjlb = null;

    @ApiModelProperty("接警报警人")
    @JsonProperty("bjr")
    String bjr = null;

    @ApiModelProperty("报警类型 dict:51")
    @JsonProperty("bjlx")
    String bjlx = null;

    @ApiModelProperty("接警单位")
    @JsonProperty("jjdw")
    List<String> jjdw = null;

    @ApiModelProperty("接警报警人联系电话")
    @JsonProperty("lxdh")
    String lxdh = null;

    @ApiModelProperty("报警内容")
    @JsonProperty("bjnr")
    String bjnr = null;


    @ApiModelProperty("发生地点")
    @JsonProperty("sfdd")
    String sfdd = null;

    @ApiModelProperty("报警方式 dict:45")
    @JsonProperty("bjxs")
    String bjxs = null;

    @ApiModelProperty("接警登记时间_开始")
    @JsonProperty("jj_djsj_start")
    String jj_djsj_start = null;

    @ApiModelProperty("接警登记时间_结束")
    @JsonProperty("jj_djsj_end")
    String jj_djsj_end = null;

    @ApiModelProperty("接警修改时间_开始")
    @JsonProperty("jj_xgsj_start")
    String jj_xgsj_start = null;

    @ApiModelProperty("接警修改时间_结束")
    @JsonProperty("jj_xgsj_end")
    String jj_xgsj_end = null;

    @ApiModelProperty("接警日期时间_开始")
    @JsonProperty("jjrqsj_start")
    String jjrqsj_start = null;

    @ApiModelProperty("接警日期时间_结束")
    @JsonProperty("jjrqsj_end")
    String jjrqsj_end = null;

    @ApiModelProperty("是否导出 0否1是")
    @JsonProperty("isExp")
    int isExp = 0;

    @ApiModelProperty("反馈时间开始")
    @JsonProperty("fk_time_start")
    String fk_time_start = null;

    @ApiModelProperty("反馈时间结束")
    @JsonProperty("fk_time_end")
    String fk_time_end = null;


    @ApiModelProperty("反馈人")
    @JsonProperty("fk_user")
    String fk_user = null;

    @ApiModelProperty("反馈单位")
    @JsonProperty("fk_unit")
    String fk_unit = null;

    @ApiModelProperty("省厅标签dict:75")
    @JsonProperty("cjjqbq")
    String cjjqbq = null;


    @ApiModelProperty("分局标签dict:76")
    @JsonProperty("fjbq")
    String fjbq = null;

    @ApiModelProperty("警情标注标签dict:10")
    @JsonProperty("bzbq")
    List<String> bzbq = null;

    @ApiModelProperty("警情标注标签dict:10")
    @JsonProperty("bzbq4Sta")
    List<String> bzbq4Sta = null;

    @ApiModelProperty("警情统计最开始 选的标签")
    @JsonProperty("currentSearch")
    @SqlInjection
    List<String> currentSearch = null;

    @ApiModelProperty("标注状态")
    @JsonProperty("bzzt")
    String bzzt = null;

    @ApiModelProperty("是否标注")
    @JsonProperty("mark")
    String mark = null;
    @ApiModelProperty("标注时间_开始")
    @JsonProperty("bz_time_start")
    String bz_time_start = null;

    @ApiModelProperty("标注时间_结束")
    @JsonProperty("bz_time_end")
    String bz_time_end = null;
    @ApiModelProperty("标注人")
    @JsonProperty("bzr")
    String bzr = null;
    @ApiModelProperty("审批状态")
    @JsonProperty("spjg")
    String spjg = null;
    @ApiModelProperty("是否关注")
    @JsonProperty("is_gz")
    String is_gz = null;
    @ApiModelProperty("审批人")
    @JsonProperty("spr")
    String spr = null;
    @ApiModelProperty("审批时间开始")
    @JsonProperty("spsj_start")
    String spsj_start = null;

    @ApiModelProperty("审批时间结束")
    @JsonProperty("spsj_end")
    String spsj_end = null;


    @ApiModelProperty("表头keys")
    @JsonProperty("keys")
    List<String> keys = new ArrayList<>();

}
