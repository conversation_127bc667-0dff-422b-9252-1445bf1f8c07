package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqJQFKCreate {


    @ApiModelProperty("反馈内容")
    @JsonProperty("content")
    @SqlInjection
    String content = null;

    @ApiModelProperty("接警编号")
    @JsonProperty("jjbh")
    @SqlInjection
    String jjbh = "";


}
