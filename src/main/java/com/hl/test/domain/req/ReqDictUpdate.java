package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqDictUpdate {


    @ApiModelProperty("id")
    @JsonProperty("id")
    String id = "";

    @ApiModelProperty("标签名称")
    @JsonProperty("dict_name")
    String dict_name = null;

    @ApiModelProperty("父id,标签类型")
    @JsonProperty("father_id")
    String father_id = null;

    @ApiModelProperty("颜色")
    @JsonProperty("color")
    String color = null;

    @ApiModelProperty("备注")
    @JsonProperty("remark")
    String remark = null;


    @ApiModelProperty("所属单位")
    @JsonProperty("gadm")
    String gadm = null;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDict_name() {
        return dict_name;
    }

    public void setDict_name(String dict_name) {
        this.dict_name = dict_name;
    }

    public String getFather_id() {
        return father_id;
    }

    public void setFather_id(String father_id) {
        this.father_id = father_id;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
