package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqDictTree {


    @ApiModelProperty("类型 10：警情标注(去掉最后一层） 500:报警 510：处警 74:事发场所 75:省厅标签 11：警情标注（完整） 3：地址标签 4：人员标签 6：时间标签 7：结果标签 " +
            "8：原因标签 9：手段标签")
    @JsonProperty("type")
    @SqlInjection
    String type = "";

    @ApiModelProperty("需不需要叶子节点")
    private boolean noLeave;

    @ApiModelProperty("单位id 用于 type为21的时候")
    private String oid;
}
