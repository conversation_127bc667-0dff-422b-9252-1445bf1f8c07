package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.DateInjection;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqJQQuery {
    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    @SqlInjection
    String jjbh = null;

    @ApiModelProperty("处警类别 dict:51")
    @JsonProperty("cjlb")
    @SqlInjection
    List<String> cjlb = null;

    @ApiModelProperty("事发场所 dict:74")
    @JsonProperty("sfcs")
    @SqlInjection
    String sfcs = null;


    @ApiModelProperty("事发原因 dict:77")
    @JsonProperty("fsyy")
    @SqlInjection
    String fsyy = null;

    @ApiModelProperty("处理结果内容")
    @JsonProperty("cljgnr")
    @SqlInjection
    String cljgnr = null;

    @ApiModelProperty("处警详址")
    @JsonProperty("cjxz")
    @SqlInjection
    String cjxz = null;

    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    @SqlInjection
    String cjjg = null;

    @ApiModelProperty("事发时间下限_开始")
    @JsonProperty("sfsjxx_start")
    @DateInjection
    String sfsjxx_start = null;

    @ApiModelProperty("事发时间下限_结束")
    @JsonProperty("sfsjxx_end")
    @DateInjection
    String sfsjxx_end = null;

    @ApiModelProperty("事发时间下限")
    @JsonProperty("sfsjxx")
    @DateInjection
    String sfsjxx = null;

    @ApiModelProperty("事发时间上限_开始")
    @JsonProperty("sfsjsx_start")
    @DateInjection
    String sfsjsx_start = null;

    @ApiModelProperty("事发时间上限_结束")
    @JsonProperty("sfsjsx_end")
    @DateInjection
    String sfsjsx_end = null;

    @ApiModelProperty("事发时间上限")
    @JsonProperty("sfsjsx")
    @DateInjection
    String sfsjsx = null;

    @ApiModelProperty("处警登记时间_开始")
    @JsonProperty("djsj_start")
    @DateInjection
    String djsj_start = null;

    @ApiModelProperty("处警登记时间_结束")
    @JsonProperty("djsj_end")
    @DateInjection
    String djsj_end = null;

    @ApiModelProperty("处警登记时间")
    @JsonProperty("djsj")
    @DateInjection
    String djsj = null;

    @ApiModelProperty("处警修改时间_开始")
    @JsonProperty("xgsj_start")
    @DateInjection
    String cj_xgsj_start = null;

    @ApiModelProperty("处警修改时间_结束")
    @JsonProperty("xgsj_end")
    @DateInjection
    String cj_xgsj_end = null;


    @ApiModelProperty("处警标识 dict:??")
    @JsonProperty("cjbs")
    @SqlInjection
    String cjbs = null;

    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = 1;

    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;

    @ApiModelProperty("涉警人员身份证")
    @JsonProperty("gmsfhm")
    @SqlInjection
    String gmsfhm = null;

    @ApiModelProperty("涉警人员姓名")
    @JsonProperty("xm")
    @SqlInjection
    String xm = null;

    @ApiModelProperty("涉警类别 dict:55")
    @JsonProperty("sjlb")
    @SqlInjection
    String sjlb = null;

    @ApiModelProperty("接警报警人")
    @JsonProperty("bjr")
    @SqlInjection
    String bjr = null;

    @ApiModelProperty("报警类型 dict:50/51")
    @JsonProperty("bjlx")
    @SqlInjection
    String bjlx = null;

    @ApiModelProperty("接警单位")
    @JsonProperty("jjdw")
    @SqlInjection
    List<String> jjdw = null;

    @ApiModelProperty("接警报警人联系电话")
    @JsonProperty("lxdh")
    @SqlInjection
    String lxdh = null;

    @ApiModelProperty("报警内容")
    @JsonProperty("bjnr")
    @SqlInjection
    String bjnr = null;


    @ApiModelProperty("发生地点")
    @JsonProperty("sfdd")
    @SqlInjection
    String sfdd = null;

    @ApiModelProperty("报警方式 dict:45")
    @JsonProperty("bjxs")
    @SqlInjection
    String bjxs = null;

    @ApiModelProperty("接警登记时间_开始")
    @JsonProperty("jj_djsj_start")
    @DateInjection
    String jj_djsj_start = null;

    @ApiModelProperty("接警登记时间_结束")
    @JsonProperty("jj_djsj_end")
    @DateInjection
    String jj_djsj_end = null;

    @ApiModelProperty("接警修改时间_开始")
    @JsonProperty("jj_xgsj_start")
    @DateInjection
    String jj_xgsj_start = null;

    @ApiModelProperty("接警修改时间_结束")
    @JsonProperty("jj_xgsj_end")
    @DateInjection
    String jj_xgsj_end = null;

    @ApiModelProperty("接警日期时间_开始")
    @JsonProperty("jjrqsj_start")
    @DateInjection
    String jjrqsj_start = null;

    @ApiModelProperty("接警日期时间_结束")
    @JsonProperty("jjrqsj_end")
    @DateInjection
    String jjrqsj_end = null;

    @ApiModelProperty("是否导出 0否1是")
    @JsonProperty("isExp")
    int isExp = 0;

    @ApiModelProperty("反馈时间开始")
    @JsonProperty("fk_time_start")
    @DateInjection
    String fk_time_start = null;

    @ApiModelProperty("反馈时间结束")
    @JsonProperty("fk_time_end")
    @DateInjection
    String fk_time_end = null;


    @ApiModelProperty("反馈人")
    @JsonProperty("fk_user")
    @SqlInjection
    String fk_user = null;

    @ApiModelProperty("反馈单位")
    @JsonProperty("fk_unit")
    @SqlInjection
    String fk_unit = null;

    @ApiModelProperty("省厅标签")
    @JsonProperty("cjjqbq")
    @SqlInjection
    String cjjqbq = null;


    @ApiModelProperty("分局标签")
    @JsonProperty("fjbq")
    @SqlInjection
    String fjbq = null;

    @ApiModelProperty("警情标注标签dict:10")
    @JsonProperty("bzbq")
    @SqlInjection
    List<String> bzbq = null;

    @ApiModelProperty("标注搜索公式")
    @JsonProperty("bzgs")
    @SqlInjection
    List<String> bzgs = null;


}
