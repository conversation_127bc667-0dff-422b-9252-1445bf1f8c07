package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqJQBZUpdate {


    @ApiModelProperty("警情编号")
    @JsonProperty("jjbh")
    String jjbh = "";
    @ApiModelProperty("地址标签")
    @JsonProperty("addressM")
    List<String> addressM = null;

    @ApiModelProperty("结果标签")
    @JsonProperty("resultM")
    List<String> resultM = null;


    @ApiModelProperty("手段标签")
    @JsonProperty("toolM")
    List<String> toolM = null;
    @ApiModelProperty("原因标签")
    @JsonProperty("reasonM")
    List<String> reasonM = null;

    @ApiModelProperty("时间标签")
    @JsonProperty("timeM")
    String timeM = null;

    @JsonProperty("is_sp")
    private String is_sp;


    @ApiModelProperty("事件标签匹配id")
    @JsonProperty("eventId")
    String eventId = null;

    @ApiModelProperty("所属责任区")
    @JsonProperty("sszrq")
    String sszrq = null;

}
