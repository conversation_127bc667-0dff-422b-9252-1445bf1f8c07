package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.DateInjection;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqJQBZUpdate_his {


    @ApiModelProperty("警情编号")
    @JsonProperty("jjbh")
    String jjbh = "";
    @ApiModelProperty("地址标签")
    @JsonProperty("addressM")
    List<String> addressM = null;
    @ApiModelProperty("人员标签")
    @JsonProperty("personM")
    List<String> personM = null;
    @ApiModelProperty("结果标签")
    @JsonProperty("resultM")
    List<String> resultM = null;


    @ApiModelProperty("手段标签")
    @JsonProperty("toolM")
    List<String> toolM = null;
    @ApiModelProperty("原因标签")
    @JsonProperty("reasonM")
    List<String> reasonM = null;

    @ApiModelProperty("时间标签")
    @JsonProperty("timeM")
    String timeM = null;

    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    String create_time = "";

    @ApiModelProperty("创建人")
    @JsonProperty("create_user")
    String create_user = "";

    @ApiModelProperty("创建人姓名")
    @JsonProperty("name")
    String name = "";

}
