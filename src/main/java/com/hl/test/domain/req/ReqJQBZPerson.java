package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqJQBZ<PERSON>erson {


    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    String jjbh = "";


    @ApiModelProperty("涉警人员身份证")
    @JsonProperty("gmsfhm")
    String gmsfhm = "";

    @ApiModelProperty("人员标签")
    @JsonProperty("personM")
    List<String> personM = null;


    @ApiModelProperty("工作单位编号")
    @JsonProperty("jgbh")
    String jgbh = null;

    @ApiModelProperty("工作单位名称")
    @JsonProperty("dwmc")
    String dwmc = null;

    @ApiModelProperty("uuid")
    @JsonProperty("uuid")
    String uuid = null;

}
