package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqJQBZCreate {


    @ApiModelProperty("警情编号")
    @JsonProperty("jjbh")
    String jjbh = null;
    @ApiModelProperty("地址标签")
    @JsonProperty("addressM")
    String addressM = null;
    @ApiModelProperty("人员标签")
    @JsonProperty("personM")
    String personM = null;
    @ApiModelProperty("结果标签")
    @JsonProperty("resultM")
    String resultM = null;

    @ApiModelProperty("时间标签")
    @JsonProperty("timeM")
    String timeM = null;
    @ApiModelProperty("手段标签")
    @JsonProperty("toolM")
    String toolM = null;
    @ApiModelProperty("原因标签")
    @JsonProperty("reasonM")
    String reasonM = null;


}
