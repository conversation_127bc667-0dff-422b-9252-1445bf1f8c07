package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.DateInjection;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReqSJRY {
    @ApiModelProperty("涉警类别 dict:55")
    @JsonProperty("sjlb")
    @SqlInjection
    String sjlb = null;
    @ApiModelProperty("姓名")
    @JsonProperty("xm")
    @SqlInjection
    String xm = null;
    @ApiModelProperty("身份证")
    @JsonProperty("gmsfhm")
    @SqlInjection
    String gmsfhm = null;
    @ApiModelProperty("接警报警人")
    @JsonProperty("bjr")
    @SqlInjection
    String bjr = null;
    @ApiModelProperty("报警方式 dict:45")
    @JsonProperty("bjxs")
    @SqlInjection
    String bjxs = null;
    @ApiModelProperty("报警类型 dict:500")
    @JsonProperty("bjlx")
    @SqlInjection
    String bjlx = null;
    @ApiModelProperty("处警类别 dict：510")
    @JsonProperty("cjlb")
    @SqlInjection
    String cjlb = null;
    @ApiModelProperty("处警结果 dict:46")
    @JsonProperty("cjjg")
    @SqlInjection
    String cjjg = null;
    @ApiModelProperty("接警单位")
    @JsonProperty("jjdw")
    @SqlInjection
    String jjdw = null;
    @ApiModelProperty("接警编号")
    @JsonProperty("jjbh")
    @SqlInjection
    String jjbh = null;
    @ApiModelProperty("处理结果内容")
    @JsonProperty("cljgnr")
    @SqlInjection
    String cljgnr = null;
    @ApiModelProperty("接警报警时间开始")
    @JsonProperty("bjdhsj_time_start")
    @DateInjection
    String bjdhsj_time_start = null;
    @ApiModelProperty("接警报警时间结束")
    @JsonProperty("bjdhsj_time_end")
    @DateInjection
    String bjdhsj_time_end = null;
    @ApiModelProperty("是否未成年")
    @JsonProperty("isWcn")
    int isWcn = -1;
    @ApiModelProperty("省厅标签 dict:75")
    @JsonProperty("cjjgbq")
    @SqlInjection
    String cjjgbq = null;
    @ApiModelProperty("分局标签 dict:76")
    @JsonProperty("fjbq")
    @SqlInjection
    String fjbq = null;

    @ApiModelProperty("警情标签")
    @JsonProperty("_personM")
    @SqlInjection
    String _personM = null;

    Integer page = 1;

    Integer limit = 20;
}
