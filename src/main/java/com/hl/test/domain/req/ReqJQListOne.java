package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqJQListOne {
    @ApiModelProperty("处警接警编号－接警编号")
    @JsonProperty("jjbh")
    @SqlInjection
    String jjbh = "";


    public String getJjbh() {
        return jjbh;
    }

    public void setJjbh(String jjbh) {
        this.jjbh = jjbh;
    }
}
