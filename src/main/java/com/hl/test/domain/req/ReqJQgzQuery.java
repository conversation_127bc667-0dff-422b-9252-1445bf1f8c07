package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqJQgzQuery {


    @ApiModelProperty("警情编号")
    @JsonProperty("jjbh")
    @SqlInjection
    String jjbh = "";
    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = 1;
    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;

}
