package com.hl.test.domain.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hl.test.config.Sql.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReqSjryQuery {


    @ApiModelProperty("公民身份号码")
    @JsonProperty("gmsfhm")
    @SqlInjection
    String gmsfhm = "";

    @ApiModelProperty("统计参数")
    @JsonProperty("sta_cols")


    ReqJQStaQuery sta_cols=null;

    @ApiModelProperty("page")
    @JsonProperty("page")
    int page = 1;

    @ApiModelProperty("limit")
    @JsonProperty("limit")
    int limit = 20;


    @ApiModelProperty("jjbhs")
    @JsonProperty("jjbhs")
    @SqlInjection
    String jjbhs = "";


}
