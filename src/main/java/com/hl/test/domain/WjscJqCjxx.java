package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(description="wjsc_jq_cjxx")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "wjsc_jq_cjxx")
public class WjscJqCjxx {
    /**
     * 记录主键
     */
    @TableId(value = "uuid", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="记录主键")
    private String uuid;

    /**
     * 处警处警时间
     */
    @TableField(value = "cjsj_time")
    @ApiModelProperty(value="处警处警时间")
    private String cjsjTime;

    /**
     * 处警类别
     */
    @TableField(value = "cjlb")
    @ApiModelProperty(value="处警类别")
    private String cjlb;

    /**
     * 处警接警编号－接警编号
     */
    @TableField(value = "jjbh")
    @ApiModelProperty(value="处警接警编号－接警编号")
    private String jjbh;

    /**
     * 处警单位
     */
    @TableField(value = "cjdw")
    @ApiModelProperty(value="处警单位")
    private String cjdw;

    /**
     * 事发场所
     */
    @TableField(value = "sfcs")
    @ApiModelProperty(value="事发场所")
    private String sfcs;

    /**
     * 损失详细情况
     */
    @TableField(value = "ssxxqk")
    @ApiModelProperty(value="损失详细情况")
    private String ssxxqk;

    /**
     * 补充处理结果
     */
    @TableField(value = "bccljg")
    @ApiModelProperty(value="补充处理结果")
    private String bccljg;

    /**
     * 事发时间下限
     */
    @TableField(value = "sfsjxx_time")
    @ApiModelProperty(value="事发时间下限")
    private String sfsjxxTime;

    /**
     * 事发星期
     */
    @TableField(value = "sfxq")
    @ApiModelProperty(value="事发星期")
    private String sfxq;

    /**
     * 处理结果内容
     */
    @TableField(value = "cljgnr")
    @ApiModelProperty(value="处理结果内容")
    private String cljgnr;

    /**
     * 事发时间上限
     */
    @TableField(value = "sfsjsx_time")
    @ApiModelProperty(value="事发时间上限")
    private String sfsjsxTime;

    /**
     * 登记人
     */
    @TableField(value = "djr")
    @ApiModelProperty(value="登记人")
    private String djr;

    /**
     * 警情属性
     */
    @TableField(value = "jqsx")
    @ApiModelProperty(value="警情属性")
    private String jqsx;

    /**
     * 天气情况
     */
    @TableField(value = "tqqk")
    @ApiModelProperty(value="天气情况")
    private String tqqk;

    /**
     * 处警编号
     */
    @TableField(value = "cjbh")
    @ApiModelProperty(value="处警编号")
    private String cjbh;

    /**
     * 处警详址
     */
    @TableField(value = "cjxz")
    @ApiModelProperty(value="处警详址")
    private String cjxz;

    /**
     * 处警处警单位名称
     */
    @TableField(value = "cjdwmc")
    @ApiModelProperty(value="处警处警单位名称")
    private String cjdwmc;

    /**
     * 处警结果
     */
    @TableField(value = "cjjg")
    @ApiModelProperty(value="处警结果")
    private String cjjg;

    /**
     * 登记时间
     */
    @TableField(value = "djsj_time")
    @ApiModelProperty(value="登记时间")
    private String djsjTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "xgsj_time")
    @ApiModelProperty(value="修改时间")
    private String xgsjTime;

    /**
     * 处警处警时间
     */
    @TableField(value = "cjsj")
    @ApiModelProperty(value="处警处警时间")
    private Date cjsj;

    /**
     * 事发时间下限
     */
    @TableField(value = "sfsjxx")
    @ApiModelProperty(value="事发时间下限")
    private Date sfsjxx;

    /**
     * 事发时间上限
     */
    @TableField(value = "sfsjsx")
    @ApiModelProperty(value="事发时间上限")
    private Date sfsjsx;

    /**
     * 登记时间
     */
    @TableField(value = "djsj")
    @ApiModelProperty(value="登记时间")
    private Date djsj;

    /**
     * 修改时间
     */
    @TableField(value = "xgsj")
    @ApiModelProperty(value="修改时间")
    private Date xgsj;

    /**
     * 所属辖区
     */
    @TableField(value = "ssxq")
    @ApiModelProperty(value="所属辖区")
    private String ssxq;

    /**
     * 处警人
     */
    @TableField(value = "cjr")
    @ApiModelProperty(value="处警人")
    private String cjr;

    /**
     * 警情标签
     */
    @TableField(value = "cjjqbq")
    @ApiModelProperty(value="警情标签")
    private String cjjqbq;

    /**
     * 地址补充
     */
    @TableField(value = "bzdzmc")
    @ApiModelProperty(value="地址补充")
    private String bzdzmc;

    /**
     * 区域类别
     */
    @TableField(value = "qylb")
    @ApiModelProperty(value="区域类别")
    private String qylb;

    /**
     * 处警信息地点
     */
    @TableField(value = "cjxxdd")
    @ApiModelProperty(value="处警信息地点")
    private String cjxxdd;

    /**
     * 警情坐标-X
     */
    @TableField(value = "xzb")
    @ApiModelProperty(value="警情坐标-X")
    private String xzb;

    /**
     * 警情坐标-Y'
     */
    @TableField(value = "yzb")
    @ApiModelProperty(value="警情坐标-Y'")
    private String yzb;

    /**
     * 警情标注标签
     */
    @TableField(value = "jqbz")
    @ApiModelProperty(value="警情标注标签")
    private String jqbz;

    /**
     * 分局标签
     */
    @TableField(value = "fjbq")
    @ApiModelProperty(value="分局标签")
    private String fjbq;

    /**
     * 标注状态  - 未标注 0
  - 已标注审核通过 1
  - 已标注待审核 2
  - 已标注审核未通过 3
     */
    @TableField(value = "bzzt")
    @ApiModelProperty(value="标注状态  - 未标注 0,  - 已标注审核通过 1,  - 已标注待审核 2,  - 已标注审核未通过 3")
    private Integer bzzt;

    /**
     * 地址标签
     */
    @TableField(value = "addressM")
    @ApiModelProperty(value="地址标签")
    private String addressm;

    /**
     * 人员标签
     */
    @TableField(value = "personM")
    @ApiModelProperty(value="人员标签")
    private String personm;

    /**
     * 结果标签
     */
    @TableField(value = "resultM")
    @ApiModelProperty(value="结果标签")
    private String resultm;

    /**
     * 时间标签
     */
    @TableField(value = "timeM")
    @ApiModelProperty(value="时间标签")
    private String timem;

    /**
     * 手段标签
     */
    @TableField(value = "toolM")
    @ApiModelProperty(value="手段标签")
    private String toolm;

    /**
     * 原因标签
     */
    @TableField(value = "reasonM")
    @ApiModelProperty(value="原因标签")
    private String reasonm;

    /**
     * 标注单位
     */
    @TableField(value = "unit")
    @ApiModelProperty(value="标注单位")
    private String unit;

    /**
     * 审批结果:-1未标注 0已标注待审批 1同意 2退回
     */
    @TableField(value = "spjg")
    @ApiModelProperty(value="审批结果:-1未标注 0已标注待审批 1同意 2退回")
    private Integer spjg;

    /**
     * 审批人
     */
    @TableField(value = "spr")
    @ApiModelProperty(value="审批人")
    private String spr;

    /**
     * 审批时间
     */
    @TableField(value = "sp_time")
    @ApiModelProperty(value="审批时间")
    private String spTime;

    /**
     * 审批内容
     */
    @TableField(value = "spnr")
    @ApiModelProperty(value="审批内容")
    private String spnr;

    /**
     * 标注人
     */
    @TableField(value = "bzr")
    @ApiModelProperty(value="标注人")
    private String bzr;

    /**
     * 标注时间
     */
    @TableField(value = "bz_time")
    @ApiModelProperty(value="标注时间")
    private String bzTime;

    /**
     * 0未标注1已标注
     */
    @TableField(value = "mark")
    @ApiModelProperty(value="0未标注1已标注")
    private Integer mark;

    @TableField(value = "bzrxm")
    @ApiModelProperty(value="")
    private String bzrxm;

    @TableField(value = "sprxm")
    @ApiModelProperty(value="")
    private String sprxm;

    /**
     * 事发原因 
     */
    @TableField(value = "fsyy")
    @ApiModelProperty(value="事发原因 ")
    private String fsyy;

    @TableField(value = "baidu")
    @ApiModelProperty(value="")
    private String baidu;

    @TableField(value = "dz_type")
    @ApiModelProperty(value="")
    private String dzType;

    @TableField(value = "dsdz")
    @ApiModelProperty(value="")
    private String dsdz;

    @TableField(value = "jgbh")
    @ApiModelProperty(value="")
    private String jgbh;

    @TableField(value = "dzid")
    @ApiModelProperty(value="")
    private String dzid;

    @TableField(value = "dwmc")
    @ApiModelProperty(value="")
    private String dwmc;


    @TableField(value = "dwdz")
    @ApiModelProperty(value="")
    private String dwdz;

    @TableField(value = "xq")
    @ApiModelProperty(value="")
    private String xq;

    @TableField(value = "build_no")
    @ApiModelProperty(value="")
    private String build_no;

    @TableField(value = "zrq")
    @ApiModelProperty(value="")
    private String zrq;

    @TableField(value = "eventId")
    @ApiModelProperty(value="事件id")
    private String eventId;

    @TableField(exist = false)
    private String oldId;

    @TableField(exist = false)
    private String newId;
}