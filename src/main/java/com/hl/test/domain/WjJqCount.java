package com.hl.test.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("wj_jq_count")
public class WjJqCount {
    @TableId
    private String jjbh;
    private String bjlx;
    @TableField("bjdhsj_time")
    private LocalDateTime bjdhsjTime;
    @TableField("cjsj_time")
    private LocalDateTime cjsjTime;
    private String jjdw;
    private String cjlb;
    private String cjdw;
    private int bzzt;
    private int spjg;
    private int mark;
}
