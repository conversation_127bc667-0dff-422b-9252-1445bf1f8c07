<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.test.mapper.WjscJqCjxxMapper">
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update wjsc_jq_cjxx
            set addressM = #{item.addressm}, jqbz = #{item.jqbz}
            where jjbh = #{item.jjbh} and uuid = #{item.uuid}
        </foreach>
    </update>


    <select id="selectJqbz" resultType="String">
        select s.id from jq_bz s INNER JOIN (
        SELECT jjbh, id, max(bz_time) as max_bz_time from jq_bz
        where jjbh in
        <foreach collection="jjbhs" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and his_type != 3 GROUP BY jjbh
        ) l on s.jjbh = l.jjbh and s.bz_time = l.max_bz_time where s.his_type != 3
    </select>





</mapper>