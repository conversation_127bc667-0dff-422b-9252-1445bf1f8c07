<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.test.mapper.WjJqLabelMapper">
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update wj_jq_label
            set label = #{item.label}, is_deleted = 0, update_time = now()
            where id = #{item.id}
        </foreach>
    </update>




</mapper>