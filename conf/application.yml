# 项目相关配置
hualong:
  # title
  title: 武进警情分析
  # 描述
  description: hl_wj
  # 版本
  version: 0.0.1
  # 返回内容包含字段: 成功码
  success: 200
  # 返回最大字段长度
  logMaxLen: 100000
  # corePoolSize
  corePoolSize: 5

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 10364
  # GRACEFUL (优雅)	当应用程序以"GRACEFUL"模式关闭时，它不会接受新的请求且会尝试完成所有正在进行的请求和处理，然后才会终止。
  # IMMEDIATE( 立即)	当应用程序以"IMMEDIATE"模式关闭时，它会立即终止，而不管当前是否有任何活动任务或请求。
  shutdown: graceful
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.hl: debug
    org.springframework: info
  config: ./conf/log4j2.xml
  # 所有的请求日志，基于aop的写入日志进入mysql
  mysql:
    # 默认关闭
    enabled: true
    # 使用的数据源
    datasource: MASTER
    # 写入的数据库
    database: user_log
    # 日志的最大存储时长，单位天
    maxSaveDays: 90
  # 所有的请求日志，基于aop的写入日志进入Slf4j
  slf4j:
    # 默认关闭
    enabled: false
    filterUrl: /person,/sso,/export_dem1

# Spring配置
spring:
  main:
    banner-mode: off
    # servlet, none,(Servlet、Reactive)
  #    web-application-type: none
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
    prefix: file:./templates/
    check-template-location: false
  profiles:
    active: druid
  #    activea:
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  application:
    name: hl-wj-jq
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        group: default
        namespace: cz-wj
        username: nacos
        password: hl123
      config:
        server-addr: *************:8848
        group: default
        namespace: cz-wj
        username: nacos
        password: hl123
  redis:
    host: 127.0.0.1  # 地址
    port: 6379  # 端口号
    database: 0  # 数据库索引（默认为0）
    timeout: 3000  # 连接超时时间（毫秒）
    lettuce:
      pool:
        max-active: 20  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1  # 最大阻塞等待时间（负数表示没有限制）
        max-idle: 5  # 连接池中最大空闲连接
        min-idle: 0  # 连接池中最小空闲连接
  security:
    # 未认证是，返回的错误码，默认401
    # unauthorized: 401
    # 在header中token名称
    type: ssork
    passToken:
    sso:
      # token: token
      # sso认证地址
      url: http://*************:8183/
      # project_id
      projectId: jzzl
      # sso中项目访问的token
      projectToken: 56as4da344dc7cc4f57909fd5264588d
liteflow:
  print-banner: false

# MyBatis配置
mybatis:
  # 是否开启
  # druidEnabled: true
  printSql: false
  # 搜索指定包别名
  typeAliasesPackage: com.hl.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

mybatis-plus:
  global-config:
    banner: off
#  configuration:
#    map-underscore-to-camel-case: false

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
#  pathMapping: /task/api/
#  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

sms:
  smsEnabled: true
  swtEnabled: true
  server: 192.168.10.98
  port: 16041
  token: 1234567890-abcd-efgh-ijkl-9876543210

task:
  # 文件预览地址
  taskPreview: https://192.168.10.98:28086/preview/
  # 局长指令
  jzzl:
    uuid: NOUDPUUX4K
    title: 任务标题,指令内容,类别,分局领导,承办单位:organization,拟办意见,任务期限,任务状态
    type_id: FormSelect_2Qb5R5y48Hov5w9y
    org_id:
  # 督查指令
  dczl:
    uuid: NRWY340ZPW
    title: 任务标题,指令内容,类别,分局领导,承办单位:organization,拟办意见,任务期限,任务状态
    type_id: FormSelect_w2FAJOM-8kEv4XvR
  # 领导批示
  ldps:
    uuid: 5LHV5JEENW
    title: 任务标题,指令内容,类别,分局领导,组织选择:organization,拟办意见,任务期限,任务状态
    type_id: FormSelect_WBhzjBCzuQaWExRW
  # 执法考评
  zfkp:
    uuid: 0BW881UVH9
    post_url: http://192.168.10.98:17080/task/
    jq_uuid: 0BW881UVHA
    aj_uuid: 0BW881UVH9
    enabled: true
    url: 192.168.10.98
    port: 52000
    token: da2f4d96ac779fc43ee3a07ac2cef3f8
  police:
    url: http://192.168.10.98:18084/
  # 信访管理
  myjc:
    enabled: true
    url: 192.168.10.98
    port: 52000
    token: 56as4da344dc7cc4f57909fd5264588d
upload_path: ./files/
#jq_sta_url
jq_sta_url: http://50.56.229.11:10368/jq_sta
mysql_addr: 50.58.123.123
#抓拍照片存放路径
picture_path: /data1/wulei/rqdj-photo
#库照片存放路径
offender_picture_path: /data1/wulei/rqdj-photo
#督察统计
zrmj_score:
  ss: 5
  bfss: 3
  bss: 0
  bssdcmqtqk: 1
cheduled:
  jq_cron: 0 0/30 * * * ?
mysql:
  addr: 127.0.0.1
  port: 3306
  user: hl
  pass: hl1234!@#$
  data: wjjq
  maxpool: 10
  timeout: 60
